package com.ruoyi.tms.controller.invoice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.Global;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.WechatMessageUtils;
import com.ruoyi.common.utils.ZLUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.poi.ExcelUtilNew;
import com.ruoyi.common.utils.poi.JxlsUtils;
import com.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.ruoyi.system.domain.SysDept;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.service.*;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.constant.InvoiceStatusEnum;
import com.ruoyi.tms.constant.dept.DeptIdConstant;
import com.ruoyi.tms.constant.finance.FreeTypeEnum;
import com.ruoyi.tms.constant.finance.OtherFeeStatusEnum;
import com.ruoyi.tms.constant.finance.PayDetailStatusEnum;
import com.ruoyi.tms.constant.finance.ReceiveDetailStatusEnum;
import com.ruoyi.tms.constant.guideprice.ReferencePriceTypeConstant;
import com.ruoyi.tms.domain.basic.Address;
import com.ruoyi.tms.domain.basic.AddressDTO;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.TransLine;
import com.ruoyi.tms.domain.carrier.Entrust;
import com.ruoyi.tms.domain.carrier.EntrustLot;
import com.ruoyi.tms.domain.check.BusinessCheck;
import com.ruoyi.tms.domain.client.*;
import com.ruoyi.tms.domain.enquiry.QuoteDtl;
import com.ruoyi.tms.domain.enquiry.RfqEnquiry;
import com.ruoyi.tms.domain.enquiry.RfqEnquiryLine;
import com.ruoyi.tms.domain.finance.CloseAccount;
import com.ruoyi.tms.domain.finance.OtherFee;
import com.ruoyi.tms.domain.finance.PayDetail;
import com.ruoyi.tms.domain.guideprice.CustGuidePrice;
import com.ruoyi.tms.domain.guideprice.GuidePriceDetail;
import com.ruoyi.tms.domain.guideprice.ReferencePrice;
import com.ruoyi.tms.domain.guideprice.ReferencePriceHistory;
import com.ruoyi.tms.domain.invoice.*;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.domain.trace.*;
import com.ruoyi.tms.domain.wecom.ApprovalDetail;
import com.ruoyi.tms.dto.AddrParseDTO;
import com.ruoyi.tms.handler.WecomHandler;
import com.ruoyi.tms.mapper.basic.ProvinceMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.client.CargoConfigMapper;
import com.ruoyi.tms.mapper.client.CustTransLineMapper;
import com.ruoyi.tms.mapper.client.MSalesGroupMapper;
import com.ruoyi.tms.mapper.enquiry.RfqMapper;
import com.ruoyi.tms.mapper.finance.CloseAccountMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.trace.EntrustCostMapper;
import com.ruoyi.tms.mapper.trace.EntrustWorkMapper;
import com.ruoyi.tms.service.basic.IAddressService;
import com.ruoyi.tms.service.basic.ICarrierService;
import com.ruoyi.tms.service.basic.IProvinceService;
import com.ruoyi.tms.service.basic.ITransLineService;
import com.ruoyi.tms.service.check.IBusinessCheckService;
import com.ruoyi.tms.service.client.ClientService;
import com.ruoyi.tms.service.client.CustGoodsService;
import com.ruoyi.tms.service.client.IAutoDispatchConfigService;
import com.ruoyi.tms.service.client.ICustMiscFeeConfigService;
import com.ruoyi.tms.service.enquiry.IRfqService;
import com.ruoyi.tms.service.finance.ICloseAccountService;
import com.ruoyi.tms.service.finance.IOtherFeeService;
import com.ruoyi.tms.service.finance.IReceiveDetailService;
import com.ruoyi.tms.service.finance.ReceivableReconciliationService;
import com.ruoyi.tms.service.guideprice.*;
import com.ruoyi.tms.service.invoice.*;
import com.ruoyi.tms.service.message.IWechatHookService;
import com.ruoyi.tms.service.segment.ISegmentService;
import com.ruoyi.tms.service.trace.IAllocationService;
import com.ruoyi.tms.service.trace.IBookingSendService;
import com.ruoyi.tms.service.trace.ICarLocusService;
import com.ruoyi.tms.service.trace.ITraceService;
import com.ruoyi.tms.service.trace.impl.AsyncJobService;
import com.ruoyi.tms.service.wecom.IWecomService;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.client.CustGoodsVO;
import com.ruoyi.tms.vo.client.CustMiscFeeVO;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.guideprice.ReferencePriceHistoryVO;
import com.ruoyi.tms.vo.guideprice.SearchReferencePriceVO;
import com.ruoyi.tms.vo.invoice.*;
import com.ruoyi.tms.vo.trace.AllocationVO;
import com.ruoyi.tms.vo.trace.InvoiceVO;
import com.ruoyi.util.ShiroUtils;
import com.ruoyi.util.WecomHelper;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/9/9 14:00
 */
@Controller
@RequestMapping("/invoice")
public class InvoiceController extends BaseController {
    private String prefix = "tms/invoice";

    @Autowired
    private IInvoiceService invoiceService;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ICloseAccountService closeAccountService;
    @Autowired
    private CloseAccountMapper closeAccountMapper;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysUploadFileService sysUploadFileService;
    @Autowired
    private IOtherFeeService otherFeeService;
    @Autowired
    private IBusinessCheckService businessCheckService;
    @Autowired
    private ICustGuidePriceService custGuidePriceService;
    @Autowired
    private EntrustMapper entrustMapper;
    @Autowired
    private EntrustLotMapper entrustLotMapper;
    @Autowired
    private ClientService clientService;
    @Autowired
    private IReceiveDetailService receiveDetailService;
    @Autowired
    private ICarLocusService carLocusService;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    private EntrustWorkMapper entrustWorkMapper;
    @Autowired
    private IAllocationService allocationService;
    @Autowired
    private IActualCarrierService actualCarrierService;
    @Autowired
    private IGuidePriceDetailService guidePriceDetailService;
    @Autowired
    private ITransLineService transLineService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ISegmentService segmentService;
    @Autowired
    private IMultipleShippingAddressService multipleShippingAddressService;
    @Autowired
    private IWechatHookService wechatHookService;
    @Autowired
    private IReferencePriceService referencePriceService;
    @Autowired
    private IReferencePriceHistoryService referencePriceHistoryService;
    @Autowired
    private IProvinceService provinceService;
    @Autowired
    private IAddressService addressService;
    @Autowired
    private ProvinceMapper provinceMapper;
    @Resource
    private WecomHandler wecomHandler;
    @Resource
    private IInvoiceDetailService invoiceDetailService;
    @Resource
    private IInvoiceDetailAddrService invoiceDetailAddrService;
    @Resource
    private IAutoDispatchConfigService autoDispatchConfigService;
    @Resource
    private IRfqService rfqService;
    @Resource
    private SysDictDataMapper sysDictDataMapper;
    @Resource
    private MSalesGroupMapper salesGroupMapper;
    @Resource
    private IBookingSendService bookingSendService;
    @Autowired
    private ITraceService traceService;
    @Autowired
    private ReceivableReconciliationService receivableReconciliationService;
    @Autowired
    private CargoConfigMapper cargoConfigMapper;
    @Autowired
    private ISysDictDataService dictService;
    @Resource
    private IWecomService wecomService;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private EntrustCostMapper entrustCostMapper;
    @Resource
    private CustGoodsService custGoodsService;
    @Resource
    private CustTransLineMapper custTransLineMapper;
    @Lazy
    @Autowired
    private AsyncJobService asyncJobService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private RfqMapper rfqMapper;
    @Resource
    private ICustMiscFeeConfigService custMiscFeeConfigService;
    @Resource
    private ICarrierService carrierService;

    /**画面id*/
    private String pageId = "invoice";
    @Autowired
    private IInvPackGoodsService invPackGoodsService;

    @RequiresPermissions("tms:invoice:view")
    @GetMapping()
    public String invoice(@RequestParam(required = false) String vbillstatus
            ,@RequestParam(required = false) String reqDeliDateStart
            ,@RequestParam(required = false) String reqDeliDateEnd
            ,ModelMap map) {
        //获取关账时间
        List<CloseAccount> closeAccountList = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", closeAccountList);
        //发货单状态list
        List<Map<String, Object>> invoiceStatusList = InvoiceStatusEnum.getAllToMap();
        map.put("invoiceStatusList", invoiceStatusList);
        //发货单map
        Map<String, Object> value = EnumUtil.getNameFieldMap(InvoiceStatusEnum.class, "value");
        map.put("invoiceStatusMap", value);
        // 运营部
        map.put("salesDept", deptService.selectDeptByParentIds(DeptIdConstant.getSalesDeptId()));

        if (StringUtils.isNotEmpty(vbillstatus)) {
            map.put("vbillstatus", vbillstatus);
        }

        if (reqDeliDateStart != null) {
            map.put("reqDeliDateStart", reqDeliDateStart);
        }
        if (reqDeliDateEnd != null) {
            map.put("reqDeliDateEnd", reqDeliDateEnd);
        }
        List<Map<String,String>> provinceList = provinceService.selectProvinces();
        map.put("provinceList", provinceList);
        //是否是车队数据
        map.put("isFleet", false);

        String biz = "fhd";
        String templateId = wecomService.getTemplateIdByBiz(biz);
        map.put("biz", biz);
        map.put("templateId", templateId);

        return "tms/invoice/invoice";
    }

    /**
     * 查询发货单列表
     * @param invoice 发货单对象
     * @return 发货单list
     */
    @RequiresPermissions("tms:invoice:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(InvoicePageVO invoice) {
        invoice.setDelFlag(0);
        //取非车队的数据
        invoice.setIsFleetData("0");
        //
        invoice.setInvoiceType(0);
        startPage();
        List<InvoicePageVO> list = invoiceService.selectInvoicePageList(invoice);
        return getDataTable(list);
    }

    /**
     * 查询发货单列表(弹窗使用)
     * @param invoice 发货单对象
     * @return 发货单list
     */
    @PostMapping("/invoiceList")
    @ResponseBody
    public TableDataInfo invoiceList(Invoice invoice) {
        startPage();
        invoice.setDelFlag(0);
        // 去除新建状态的发货单
        invoice.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
        // 未关账
        invoice.setIsClose(0);
        List<Invoice> list = invoiceService.selectInvoicePopupList(invoice);
        return getDataTable(list);
    }


    /**
     * 导出发货单列表
     */
    @RequiresPermissions(value = {"tms:invoice:export", "tms:invoice:ledger"}, logical = Logical.OR)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Invoice invoice) {
        Subject subject = ShiroUtils.getSubject();
        if (subject.isPermitted("tms:invoice:ledger")) {
            /*
             * 台账导出
             */
            //取非车队的数据
            invoice.setIsFleetData("0");
            List<Map<String, Object>> list = invoiceService.ledgerList(invoice);
            if (list.size() == 0) {
                return AjaxResult.error("没有可导出数据");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String outName = UUID.randomUUID() + "_" + "台账" + sdf.format(new Date()) + ".xlsx";
            try (OutputStream os = new FileOutputStream(Global.getDownloadPath() + outName)) {
                Map<String, Object> param = new HashMap<>();
                param.put("list", list);
                Function<Segment, String> nwv = segment -> {
                    return null;
                };
                param.put("nwv", nwv);
                JxlsUtils.createExcel(Global.getDownloadPath() + "/template/台账.xlsx", os, param);
                return AjaxResult.success(outName);
            } catch (Exception e) {
                logger.error("生成{}时失败：", outName, e);
                logger.error("删除文件{}：{}", Global.getDownloadPath() + outName, new File(Global.getDownloadPath() + outName).delete());
                return AjaxResult.error(e.getMessage() == null ? e.toString() : e.getMessage());
            }

        }else {
            /*
             * 普通导出
             */
            invoice.setDelFlag(0);
            //取非车队的数据
            invoice.setIsFleetData("0");
            Map<String, Object> dataBase = new HashMap<>();
            Map<String, String> transCodeMap = new HashMap<>();
            List<SysDictData> transCode = dictService.selectDictDataByType("trans_code");
            transCode.forEach(dict -> {
                transCodeMap.put(dict.getDictValue(), dict.getDictLabel());
            });
            dataBase.put("trans_code", transCodeMap);

            List<InvoiceExportVO> list = invoiceService.selectInvoiceListExport(invoice);
            ExcelUtilNew<InvoiceExportVO> util = new ExcelUtilNew<>(InvoiceExportVO.class);
            return util.exportExcel(list, "发货单",dataBase);

        }
    }

    /**
     * 新增发货单
     * @return 添加页面
     */
    @RequiresPermissions("tms:invoice:add")
    @GetMapping("/add")
    public String add(ModelMap map) {
        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        map.addAttribute("billingMethods",billingMethods );

        //调度组
        map.addAttribute("dispatcherDeptList", deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId()));
        //驻场组
        map.addAttribute("stationDeptList", deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID));

        map.put("years", actualCarrierService.getSixMonth());

        //是否是车队数据
        map.put("isFleet", false);
        return "tms/invoice/add";
    }

    /**
     * 新增保存发货单
     *
     * @param invoice 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @RequiresPermissions(value = {"tms:invoice:add", "rfq:enquiry:list"}, logical = Logical.OR)
    @Log(title = "发货单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated InvoiceAddVO invoice) {
        invoice.setCorScrId(pageId);
        invoice.setRegScrId(pageId);
        return invoiceService.insertInvoice(invoice, 0);
    }

    /**
     * 新增保存发货单
     * @param invoice 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:add")
    @Log(title = "发货单", businessType = BusinessType.INSERT)
    @PostMapping("/add_many")
    @ResponseBody
    public AjaxResult addManySave(@Validated InvoiceAddVO invoice,Integer invoiceCt) {
        invoiceCt = invoiceCt == null ? 1 : invoiceCt;
        if (invoiceCt > 10) {
            return AjaxResult.error("最多支持同时生成10单。");
        }

        invoice.setCorScrId(pageId);
        invoice.setRegScrId(pageId);
        return invoiceService.insertInvoiceMany(invoice, invoiceCt, 0);
    }

    /**
     * 修改发货单 与 复制发货单
     * @param invoiceId 发货单id
     * @param type copy or edit
     * @param mmap
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:copy","tms:invoice:edit"},logical = Logical.OR)
    @GetMapping("/edit/{invoiceId}/{type}")
    public String edit(@PathVariable("invoiceId") String invoiceId,@PathVariable("type") String type, ModelMap mmap) {
        //发货单信息
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);
        if (invoice == null) {
            return "error/not_exist";
        }
        if (invoice.getRfqEnquiryId() != null && "copy".equalsIgnoreCase(type)) {
            final RfqEnquiry e = rfqMapper.getEnquiryById(invoice.getRfqEnquiryId());
            if (e == null || e.getByNew() == null || e.getByNew() < 2 || invoice.getRfqCartypeId() == null) {
                throw new RuntimeException("旧版询价单创建的发货单不再支持复制");
            }
            if (Objects.equals(e.getStatus(), 7)) {
                throw new RuntimeException("创建发货单的询价单已结项，不再支持复制");
            }
            if (e.getStatus() != 5) {
                throw new RuntimeException("创建发货单的询价单不是已中标状态，不再支持复制");
            }
            if (e.getClosed() == 1) {
                throw new RuntimeException("创建发货单的询价单已关闭，不再支持复制");
            }
        }
        if (invoice.getRfqCartypeId() != null) { // 询价信息
            final RfqEnquiryLine.CarType rfqCarLenObj = rfqService.getCarLenObjById(invoice.getRfqCartypeId());
            /*if (rfqCarLenObj.getBillingMethod() == null || rfqCarLenObj.getBillingMethod() == 3) { // 包车时判断车次是否达标
                final Map<String, Object> m = rfqService.timesAndInvoiceCount(invoice.getRfqCartypeId());
                Integer times = (Integer) m.get("times");
                Integer invoiceCount = (Integer) m.get("invoiceCount");
                if (invoiceCount >= times) {
                    throw new RuntimeException("转单车次已满，不可再复制此单");
                }
            }*/
            mmap.put("rfqCarLenObj", rfqCarLenObj);
            RfqEnquiryLine rfqLine = rfqService.getLineByLineId(invoice.getRfqEnquiryLineId());
            mmap.put("rfqLine", rfqLine);
            mmap.put("rfqCartypeId", invoice.getRfqCartypeId());
        }
        mmap.put("invoice", invoice);
        if (invoice.getRfqEnquiryLineId() != null) {
            RfqEnquiry enquiry = new RfqEnquiry();
            enquiry.setId(invoice.getRfqEnquiryId());
            List<QuoteDtl> dtls = rfqService.listQuoteDtls(enquiry, true);
            BigDecimal rfqCost = BigDecimal.ZERO;
            for (int i = 0; i < dtls.size(); i++) {
                if (dtls.get(i).getLineId().equals(invoice.getRfqEnquiryLineId()) && dtls.get(i).getChoosed() == 1) {
                    rfqCost = rfqCost.add(BigDecimal.valueOf(dtls.get(i).getSuggest()));
                }
            }
            mmap.put("rfqCost", rfqCost); // 转发货单的成本价

            RfqEnquiry rfqEnquiry = rfqService.getEnquiryXById(invoice.getRfqEnquiryId());
            mmap.put("rfqEnquiryId", invoice.getRfqEnquiryId());
            mmap.put("rfqEnquiryLineId", invoice.getRfqEnquiryLineId());
            mmap.put("enquiryNo", rfqEnquiry.getVbillno());

        }

        //客户信息
        ClientPopupVO client = clientService.selectClientById(invoice.getCustomerId());
        mmap.put("client", client);


        //结算客户信息        22/2/23  取默认的结算客户
//        ClientPopupVO balaClient = clientService.selectClientById(invoice.getBalaCustomerId());
        Client balaClient = clientService.getDefaultCustBalaByCustomerId(invoice.getCustomerId());
        mmap.put("balaClient", balaClient == null ? new Client() : balaClient);

        //保险附件
        String tid = invoice.getInsuranceAppendixId() == null ? "" : invoice.getInsuranceAppendixId();
        List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(tid);
        mmap.put("sysUploadFiles", sysUploadFiles);

        //议价凭证附件
        String bargainFileId = invoice.getBargainFileId() == null ? "" : invoice.getBargainFileId();
        List<SysUploadFile> bargainFiles = sysUploadFileService.selectSysUploadFileByTid(bargainFileId);
        mmap.put("bargainFiles", bargainFiles);

        //驻场组
        SysDept sysDept = sysDeptService.selectDeptById(Convert.toLong(invoice.getStationDept(), -1L));
        mmap.put("stationDeptName", sysDept == null ? "" : sysDept.getDeptName());

        //驻场人员
        String residentsId = invoice.getResidentsId();
        String residentsName = "";
        if (StringUtils.isNotEmpty(residentsId)) {
            List<SysUser> sysUserList = sysUserService.selectUserByIds(Convert.toStrArray(residentsId));
            residentsName = sysUserList.stream().map(SysUser::getUserName).collect(Collectors.joining(","));
        }
        mmap.put("residentsName", residentsName);

        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        mmap.put("billingMethods",billingMethods );



        //多装多卸 地址与货品信息
        List<MultipleShippingAddress> multipleShippingAddresses = new ArrayList<>();

//        if (1 == invoice.getIsMultiple()) {
        //运输方式为多装多卸的  直接取多装多卸表数据
        multipleShippingAddresses = multipleShippingAddressService
                .selectAddrAndGoodsByInvoiceId(invoiceId);
       /* } else {
            //运输方式不为多装多卸的   取发货单与货品表数据  进行封装
            *//*
         * 货品信息封装
         *//*
            List<InvPackGoods> invPackGoodsList = invoice.getInvPackGoodsList();
            List<MultipleShippingGoods> goodsList = new ArrayList<>();
            for (InvPackGoods invPackGoods : invPackGoodsList) {
                MultipleShippingGoods goods = new MultipleShippingGoods();
                BeanUtils.copyBeanProp(goods, invPackGoods);
                goodsList.add(goods);
            }

            *//*
         * 提货地址
         *//*
            MultipleShippingAddress deliAddress = new MultipleShippingAddress();
            deliAddress.setDeliveryId(invoice.getDeliveryId());
            deliAddress.setAddrCode(invoice.getDeliAddrCode());
            deliAddress.setAddrName(invoice.getDeliAddrName());
            deliAddress.setProvinceId(invoice.getDeliProvinceId());
            deliAddress.setProvinceName(invoice.getDeliProName());

            deliAddress.setCityId(invoice.getDeliCityId());
            deliAddress.setCityName(invoice.getDeliCityName());
            deliAddress.setAreaId(invoice.getDeliAreaId());
            deliAddress.setAreaName(invoice.getDeliAreaName());
            deliAddress.setDetailAddr(invoice.getDeliDetailAddr());
            deliAddress.setContact(invoice.getDeliContact());
            deliAddress.setMobile(invoice.getDeliMobile());
            deliAddress.setAddressType(0);
            deliAddress.setInvoiceId(invoice.getInvoiceId());
            deliAddress.setShippingGoodsList(goodsList);
            multipleShippingAddresses.add(deliAddress);
            *//*
         * 收货地址
         *//*
            MultipleShippingAddress arriAddress = new MultipleShippingAddress();
            arriAddress.setDeliveryId(invoice.getArrivalId());
            arriAddress.setAddrCode(invoice.getArriAddrCode());
            arriAddress.setAddrName(invoice.getArriAddrName());
            arriAddress.setProvinceId(invoice.getArriProvinceId());
            arriAddress.setProvinceName(invoice.getArriProName());

            arriAddress.setCityId(invoice.getArriCityId());
            arriAddress.setCityName(invoice.getArriCityName());
            arriAddress.setAreaId(invoice.getArriAreaId());
            arriAddress.setAreaName(invoice.getArriAreaName());
            arriAddress.setDetailAddr(invoice.getArriDetailAddr());
            arriAddress.setContact(invoice.getArriContact());
            arriAddress.setMobile(invoice.getArriMobile());
            arriAddress.setAddressType(1);
            arriAddress.setInvoiceId(invoice.getInvoiceId());
            arriAddress.setShippingGoodsList(goodsList);
            multipleShippingAddresses.add(arriAddress);
        }*/
        //每个货品 加入一个8位的唯一id  用于前端判断货品的唯一性
        multipleShippingAddresses.forEach(x -> x.getShippingGoodsList().forEach(y -> {
            Map<String, Object> param = new HashMap<>();
            param.put("uid", IdUtil.randomUUID().substring(0, 8));
            y.setParams(param);
        }));

        mmap.put("multipleShippingAddresses", multipleShippingAddresses);

        double numCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(0))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getNum).sum();

        double weightCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(0))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getWeight).sum();

        double volumeCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(0))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getVolume).sum();
        mmap.put("numCount", numCount);
        mmap.put("weightCount", weightCount);
        mmap.put("volumeCount", volumeCount);


        //地址数量
        mmap.put("addr_index", multipleShippingAddresses.size());

        List<Integer> fhdzAddrList = new ArrayList<>();
        List<Integer> shdzAddrList = new ArrayList<>();
        Map<Object, List<Map<String, Object>>> addrGoodsIndex = new HashMap<>();
        for (int i = 0; i < multipleShippingAddresses.size(); i++) {
            if (multipleShippingAddresses.get(i).getAddressType() == 0) {
                fhdzAddrList.add(i);
            }else {
                shdzAddrList.add(i);
            }

            //货品list
            List<MultipleShippingGoods> shippingGoodsList = multipleShippingAddresses.get(i).getShippingGoodsList();
            List<Map<String, Object>> goodsList = new ArrayList<>();
            for (int j = 0; j < shippingGoodsList.size(); j++) {
                Map<String, Object> goodsMap = new HashMap<>();
                goodsMap.put("id", j);
                goodsMap.put("value", shippingGoodsList.get(j).getParams().get("uid"));
                goodsList.add(goodsMap);
            }
            addrGoodsIndex.put(i, goodsList);
        }
        //发货单地址的集合
        mmap.put("fhdz_addr_list", fhdzAddrList);
        //收货地址的集合
        mmap.put("shdz_addr_list", shdzAddrList);
        //每个地址对应的货品下标
        mmap.put("addr_goods_index", addrGoodsIndex);


        //调度组
        List<SysDept> dispatcherDept = deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId());
        mmap.put("dispatcherDeptList", dispatcherDept);
        //驻场组
        mmap.put("stationDeptList", deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID));

        mmap.put("years", actualCarrierService.getSixMonth());

        String transLineId = invoice.getTransLineId();
        List<String> transLineNameList = dispatcherDept.stream()
                .filter(x -> transLineId.equals(x.getDeptId().toString()))
                .map(SysDept::getDeptName).collect(Collectors.toList());

        if (transLineNameList.size() > 0) {
            mmap.put("transLineName", transLineNameList.get(0));
        }

        mmap.put("type",type);

        Boolean b = invoiceService.checkCustomerContractValid(client.getCustomerId());
        mmap.put("customerContractBL", b);

        if ("copy".equals(type)) {
            invoice.setOtherFeeType(null);
            invoice.setOtherFee(null);
            invoice.setOtherFeeMemo(null);

        }

        //是否是车队数据
        mmap.put("isFleet", false);
        return "tms/invoice/edit";
    }

    @GetMapping("/checkCustomerContractValid")
    @ResponseBody
    public Boolean checkCustomerContractValidAjax(@RequestParam("customerId") String customerId) {
        return invoiceService.checkCustomerContractValid(customerId);
    }

    /**
     * 转换发货单
     *
     * @param
     * @param mmap
     * @return
     */
    @RequiresPermissions(value = {"rfq:enquiry:list"}, logical = Logical.OR)
    @GetMapping("/convert/{rfqEnquiryId}/{lineId}/{cartypeId}")
    public String convertInvoice(@PathVariable("rfqEnquiryId") String rfqEnquiryId,
            @PathVariable("lineId") String lineId, @PathVariable("cartypeId") String cartypeId, ModelMap mmap) {
        mmap.put("isFleet", false);
        RfqEnquiry enquiry = rfqService.getEnquiryXById(rfqEnquiryId);
        if (enquiry.getStatus() != 5 || enquiry.getClosed() == 1) {
            throw new RuntimeException("只有已中标状态且未关闭的单据才能转发货单");
        }
        RfqEnquiryLine.CarType lenObj = rfqService.getCarLenObjById(cartypeId);
        /*if (lenObj.getBillingMethod() == null || lenObj.getBillingMethod() == 3) { // 包车
            Map<String, Object> m = rfqService.timesAndInvoiceCount(lenObj.getId());
            Integer times = (Integer) m.get("times");
            Integer invoiceCount = (Integer) m.get("invoiceCount");
            if (invoiceCount >= times) {
                throw new RuntimeException("转单车次已满，不可再转单");
            }
        }*/
        RfqEnquiryLine rfqEnquiryLine = rfqService.getLineByLineId(lineId);
        //List<QuoteDtl> dtls = rfqService.listQuoteDtls(enquiry, true);
        //BigDecimal rfqCost = BigDecimal.ZERO;
        //for (int i = 0; i < dtls.size(); i++) {
        //    if (dtls.get(i).getLineId().equals(lineId) && dtls.get(i).getChoosed() == 1) {
        //        rfqCost = rfqCost.add(BigDecimal.valueOf(dtls.get(i).getSuggest()));
        //    }
        //}
        //mmap.put("rfqCost", rfqCost); // 转发货单的成本价
        List<RfqEnquiry.Addr> addrList = rfqService.listEnquiryAddrByLineId(lineId);

        //客户信息
        ClientPopupVO client = clientService.selectClientById(enquiry.getCustomerId());
        mmap.put("client", client);
        //结算客户信息
        Client balaClient = clientService.getDefaultCustBalaByCustomerId(enquiry.getCustomerId());
        mmap.put("balaClient", balaClient == null ? new Client() : balaClient);

        mmap.put("rfqEnquiryId", rfqEnquiryId);
        mmap.put("rfqEnquiryLineId", lineId);
        mmap.put("rfqLine", rfqEnquiryLine);
        mmap.put("rfqCartypeId", cartypeId);
        mmap.put("enquiryNo", enquiry.getVbillno());
        mmap.put("convert", 1);

        //发货单信息
        Invoice invoice = new Invoice();
        invoice.setIsOversize(0); // 默认0，下方可能修改
        invoice.setReqDeliDate(DateUtil.parse(enquiry.getDeliDate()));
        invoice.setCustomerId(client.getCustomerId());
        invoice.setCustCode(client.getCustCode());
        // 运输方式改从carLenObjs中取
        //final List<RfqEnquiryLine.CarType> carLenObjs = rfqService.listLineHitCarTypes(lineId);
        // 计价方式设置成包车
        invoice.setBillingMethod("3");

        if (enquiry.getByNew() >= 2) { // 多运输方式、多货量、多尺寸、多车长版本
            mmap.put("numCount", lenObj.getNum());
            mmap.put("weightCount", lenObj.getWeight());
            mmap.put("volumeCount", lenObj.getVolume());
            mmap.put("rfqCarLenObj", lenObj);
            invoice.setCarLen(lenObj.getCarLen());
            invoice.setCarLenName(lenObj.getCarLenName());
            String transCode = lenObj.getTransCode();
            invoice.setTransCode(transCode);
            invoice.setTransName(dictService.selectDictLabel("trans_code", transCode));
            invoice.setIsOversize(lenObj.getIsOversize());
            if (Objects.equals(lenObj.getIsOversize(), 1)) {
                invoice.setGoodsLength(lenObj.getLength() == null ? null : BigDecimal.valueOf(lenObj.getLength()));
                invoice.setGoodsWidth(lenObj.getWidth() == null ? null : BigDecimal.valueOf(lenObj.getWidth()));
                invoice.setGoodsHeight(lenObj.getHeight() == null ? null : BigDecimal.valueOf(lenObj.getHeight()));
            }
            invoice.setCarType(lenObj.getCarType());
            invoice.setCarTypeName(lenObj.getCarTypeName());
            if (lenObj.getBillingMethod() != null) { // 计价方式
                invoice.setBillingMethod(lenObj.getBillingMethod().toString());
            }
            invoice.setIsRoundTrip(rfqEnquiryLine.getRoundTrip());
            // 取策略报价的单车报价
            /*for (int i = 0; i < dtls.size(); i++) {
                if (dtls.get(i).getCartypeId().equals(cartypeId) && dtls.get(i).getHit() == 1) {
                    invoice.setUnitPrice(dtls.get(i).getQuoteUnitPrice()); // 整车时，单车报价作为发货单单价
                    final String billingType = dtls.get(i).getBillingType();
                    BigDecimal suggest = BigDecimal.valueOf(dtls.get(i).getSuggest());
                    if (billingType != null && !billingType.equals("6")) {
                        final String cb_sjdk_kp = sysConfigService.selectConfigByKey("cb_sjdk_kp");
                        suggest = suggest.multiply(BigDecimal.ONE.subtract(new BigDecimal(cb_sjdk_kp)));
                    }
                    mmap.put("rfqCost", suggest); // 单车建议价转发货单的成本价
                    break;
                }
            }*/

        } else {//已全部结项
            /*if (rfqEnquiryLine.getTransCodeArr() != null && rfqEnquiryLine.getTransCodeArr().length == 1) {
                invoice.setTransCode(rfqEnquiryLine.getTransCode());
                invoice.setTransName(dictService.selectDictLabel("trans_code", rfqEnquiryLine.getTransCode()));
            }
            if (carLenObjs.size() == 1) {
                // 取策略报价的单车报价
                for (int i = 0; i < dtls.size(); i++) {
                    if (dtls.get(i).getType() == 1 && dtls.get(i).getCartypeId().equals(carLenObjs.get(0).getId()) && dtls.get(i).getHit() == 1) {
                        invoice.setUnitPrice(dtls.get(i).getQuotePrice());
                        final String billingType = dtls.get(i).getBillingType();
                        BigDecimal suggest = BigDecimal.valueOf(dtls.get(i).getSuggest());
                        if (billingType != null && !billingType.equals("6")) {
                            final String cb_sjdk_kp = sysConfigService.selectConfigByKey("cb_sjdk_kp");
                            suggest = suggest.multiply(BigDecimal.ONE.subtract(new BigDecimal(cb_sjdk_kp)));
                        }
                        mmap.put("rfqCost", suggest); // 单车建议价转发货单的成本价
                        break;
                    }
                }
                invoice.setCarLen(carLenObjs.get(0).getCarLen());
                invoice.setCarLenName(carLenObjs.get(0).getCarLenName());
                if (carLenObjs.get(0).getCarLenName().equals("大件")) {
                    invoice.setIsOversize(1);
                }
            }*/
        }


        //if (rfqEnquiryLine.getCarTypeArr() != null && rfqEnquiryLine.getCarTypeArr().length == 1) {
        //    invoice.setCarType(rfqEnquiryLine.getCarTypeArr()[0]);
        //    invoice.setCarTypeName(dictService.selectDictLabel("car_type", rfqEnquiryLine.getCarTypeArr()[0]));
        //}

//        invoice.setIsOversize(enquiry.getOverload());
        invoice.setMemo(rfqEnquiryLine.getRemark());
        invoice.setBillingType(client.getBillingType()); // 是否开票
        invoice.setIfBargain(1);
        invoice.setRfqEnquiryId(rfqEnquiryId);
        invoice.setRfqEnquiryLineId(lineId);
        invoice.setRfqCartypeId(cartypeId);
        invoice.setUrgentLevel("0"); // 紧急程度：普通
        invoice.setBalaType("1"); // 回单结算

        CustGoods query = new CustGoods();
        query.setGoodsName(rfqEnquiryLine.getGoodsName()); // 模糊查询
        query.setCustomerId(client.getCustomerId());
        final List<CustGoodsVO> goodsListByCustomerId = custGoodsService.list(query); // 到客户货物信息中匹配询价单的货物
        CustGoodsVO rfqGoods = null;
        for (int i = 0; i < goodsListByCustomerId.size(); i++) {
            if (goodsListByCustomerId.get(i).getGoodsName().equals(rfqEnquiryLine.getGoodsName())) {
                rfqGoods = goodsListByCustomerId.get(i);
                break;
            }
        }

        mmap.put("invoice", invoice);


        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        mmap.put("billingMethods", billingMethods);


        //多装多卸 地址与货品信息
        List<MultipleShippingAddress> multipleShippingAddresses = new ArrayList<>();
        for (RfqEnquiry.Addr addr : addrList) {
            MultipleShippingAddress address = new MultipleShippingAddress();

            address.setProvinceId(addr.getProvinceId());
            address.setProvinceName(addr.getProvinceName());
            address.setCityId(addr.getCityId());
            address.setCityName(addr.getCityName());
            address.setAreaId(addr.getAreaId());
            address.setAreaName(addr.getAreaName());

            address.setAddrName(addr.getAddrName());
            address.setDetailAddr(addr.getDetailAddr());
            //地址类型  0发货地址  1收货地址
            address.setAddressType(addr.getType());
            //提货/到货联系人
            address.setContact(addr.getContact());
            //联系人手机
            address.setMobile(addr.getContactMobile());
            if (addr.getAddressId() != null) {
                final Address address1 = addressService.selectAddressById(addr.getAddressId());
                address.setLongitudedegree(address1.getLongitudedegree());
                address.setLatitudedegree(address1.getLatitudedegree());
            }

            List<MultipleShippingGoods> goodsList = new ArrayList<>();
            if (rfqGoods != null) {
                MultipleShippingGoods goods = new MultipleShippingGoods();
                goods.setGoodsName(rfqEnquiryLine.getGoodsName());
                if (enquiry.getByNew() >= 2) { // 新版数据的件吨方跟这车长数据走
                    goods.setNum(lenObj.getNum());
                    goods.setWeight(lenObj.getWeight());
                    goods.setVolume(lenObj.getVolume());
                } else {
                    goods.setNum(rfqEnquiryLine.getNum());
                    goods.setWeight(rfqEnquiryLine.getWeight());
                    goods.setVolume(rfqEnquiryLine.getVolume());
                }
                goods.setPackId(rfqGoods.getPackId());
                goodsList.add(goods);
            }
            address.setShippingGoodsList(goodsList);

            multipleShippingAddresses.add(address);
        }




        //每个货品 加入一个8位的唯一id  用于前端判断货品的唯一性
        multipleShippingAddresses.forEach(x -> x.getShippingGoodsList().forEach(y -> {
            Map<String, Object> param = new HashMap<>();
            param.put("uid", IdUtil.randomUUID().substring(0, 8));
            y.setParams(param);
        }));

        mmap.put("multipleShippingAddresses", multipleShippingAddresses);
        //地址数量
        mmap.put("addr_index", multipleShippingAddresses.size());

        List<Integer> fhdzAddrList = new ArrayList<>();
        List<Integer> shdzAddrList = new ArrayList<>();
        Map<Object, List<Map<String, Object>>> addrGoodsIndex = new HashMap<>();
        for (int i = 0; i < multipleShippingAddresses.size(); i++) {
            if (multipleShippingAddresses.get(i).getAddressType() == 0) {
                fhdzAddrList.add(i);
            }else {
                shdzAddrList.add(i);
            }

            //货品list
            List<MultipleShippingGoods> shippingGoodsList = multipleShippingAddresses.get(i).getShippingGoodsList();
            List<Map<String, Object>> goodsList = new ArrayList<>();
            for (int j = 0; j < shippingGoodsList.size(); j++) {
                Map<String, Object> goodsMap = new HashMap<>();
                goodsMap.put("id", j);
                goodsMap.put("value", shippingGoodsList.get(j).getParams().get("uid"));
                goodsList.add(goodsMap);
            }
            addrGoodsIndex.put(i, goodsList);
        }
        //发货单地址的集合
        mmap.put("fhdz_addr_list", fhdzAddrList);
        //收货地址的集合
        mmap.put("shdz_addr_list", shdzAddrList);
        //每个地址对应的货品下标
        mmap.put("addr_goods_index", addrGoodsIndex);

        //调度组
        List<SysDept> dispatcherDept = deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId());
        mmap.put("dispatcherDeptList", dispatcherDept);
        //驻场组
        mmap.put("stationDeptList", deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID));

        Boolean b = invoiceService.checkCustomerContractValid(client.getCustomerId());
        mmap.put("customerContractBL", b);

        mmap.put("type", "copy");

        return "tms/invoice/edit";
    }



    /**
     * 修改保存发货单
     * @param invoice 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:edit")
    @Log(title = "发货单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated InvoiceEditVO invoice) {
        invoice.setCorScrId(pageId);
        return invoiceService.updateInvoice(invoice, 0);
    }

    /**
     * 删除发货单(逻辑删除)
     * @param ids id
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:remove")
    @Log(title = "发货单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return invoiceService.deleteInvoiceByIds(ids , pageId);
    }

    /**
     * 发货单详情
     * @param mmap
     * @return
     */
//    @RequiresPermissions("tms:invoice:detail")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String invoiceId, ModelMap mmap) {
        //发货单信息
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);
        if (invoice == null) {
            return "error/not_exist";
        }
        mmap.put("invoice", invoice);

        String unitPriceMsg = "";
        if (DateUtil.compare(invoice.getRegDate(), DateUtil.parse("2025-01-08")) < 0
                && invoice.getIfBargain() == 0
                && ("8".equals(invoice.getBillingMethod()) || "9".equals(invoice.getBillingMethod()))) {
            unitPriceMsg = "2025-01-08之前的数据，单价金额=公里单价*公里";
        }
        mmap.put("unitPriceMsg", unitPriceMsg);

        //保险附件
        String tid = invoice.getInsuranceAppendixId() == null ? "" : invoice.getInsuranceAppendixId();
        List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(tid);
        mmap.put("sysUploadFiles", sysUploadFiles);

        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        mmap.put("billingMethods",billingMethods );
        //发货单状态
        List<Map<String, Object>> invoiceStatus = InvoiceStatusEnum.getAllToMap();
        mmap.put("invoiceStatusList",invoiceStatus );

        //结算组
        SysDept balanceDept = sysDeptService.selectDeptById(Convert.toLong(invoice.getBalaDept(), -1L));
        mmap.put("balanceDept", balanceDept == null ? "" : balanceDept.getDeptName());
        //驻场人员
        SysUser sysUser = sysUserService.selectUserById(Convert.toLong(invoice.getResidentsId(), -1L));
        mmap.put("residentsName", sysUser == null ? "" : sysUser.getUserName());
        //驻场组
        SysDept stationDeptName = sysDeptService.selectDeptById(Convert.toLong(invoice.getStationDept(), -1L));
        mmap.put("stationDeptName", stationDeptName == null ? "" : stationDeptName.getDeptName());
        //运营部
        SysDept salesDept = sysDeptService.selectDeptById(Convert.toLong(invoice.getSalesDept(), -1L));
        mmap.put("salesDept", salesDept == null ? "" : salesDept.getDeptName());
        //业务员
        SysUser psndoc = sysUserService.selectUserById(Convert.toLong(invoice.getPsndoc(), -1L));
        mmap.put("psndocName", psndoc == null ? "" : psndoc.getUserName());

        /*
         * 应收
         */
        List<ReceiveDetailVO> receiveDetailVOList = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
        receiveDetailVOList.sort(Comparator.comparing(ReceiveDetailVO::getRegDate));
        mmap.put("receiveDetailList", receiveDetailVOList);

        //运输方式为多装多卸的  直接取多装多卸表数据
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService
                .selectAddrAndGoodsByInvoiceId(invoiceId);

        //发货地址
        List<MultipleShippingAddress> deliShippingAddressList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0).collect(Collectors.toList());
        mmap.put("deliShippingAddressList", deliShippingAddressList);
        //收货地址
        List<MultipleShippingAddress> arriShippingAddressList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 1).collect(Collectors.toList());
        mmap.put("arriShippingAddressList", arriShippingAddressList);

        //提货货品
        List<MultipleShippingGoods> deliShippingGoodsList = deliShippingAddressList.stream()
                .flatMap(x -> x.getShippingGoodsList().stream())
                .collect(Collectors.toList());
        mmap.put("deliShippingGoodsList", deliShippingGoodsList);

        /*
         *  委托单
         */
        List<OperationHistoryVO> operationHistoryVOList = new ArrayList<>();
        List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
        for (Entrust entrust : entrustList) {
            OperationHistoryVO operationHistoryVO = new OperationHistoryVO();
            BeanUtils.copyBeanProp(operationHistoryVO, entrust);

            EntrustWork entrustWorkSele = new EntrustWork();
            entrustWorkSele.setEntrustId(entrust.getEntrustId());
            entrustWorkSele.setDelFlag(0);
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSele);
            for (EntrustWork entrustWork : entrustWorks) {
                if ("1".equals(entrustWork.getWorkType())) {
                    SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                    //提货
                    operationHistoryVO.setPickUpUserName(user.getUserName());
                    //提货时间
                    operationHistoryVO.setPickUpDate(entrustWork.getRegDate());
                } else if ("2".equals(entrustWork.getWorkType())) {
                    SysUser user = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                    //到货
                    operationHistoryVO.setArrivalsUserName(user.getUserName());
                    //到货时间
                    operationHistoryVO.setArrivalsDate(entrustWork.getRegDate());
                }
            }

            //应付
            List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(entrust.getLotId());
            payDetailList.sort(Comparator.comparing(PayDetail::getRegDate));
            operationHistoryVO.setPayDetailList(payDetailList);
//            Allocation allocation = new Allocation();
//
//            allocationService.selectAllocationList(allocation);
            //定位信息
            List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrust.getEntrustId());
            carLocusList.sort(Comparator.comparing(CarLocus::getTrackingTime));
            operationHistoryVO.setCarLocusList(carLocusList);

            operationHistoryVOList.add(operationHistoryVO);
        }

        mmap.put("operationHistoryVOList", operationHistoryVOList);

        Map<String, String> priceMap = new HashMap<>();
        //合同价计算
        if (invoice.getIfBargain() != null && invoice.getIfBargain() == 0) {
            //查询合同价
            SearchContractPriceVO priceVO = new SearchContractPriceVO();
            List<String> deliAreaIdList = multipleShippingAddresses.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());
            priceVO.setDeliAreaIdList(deliAreaIdList);

            List<String> deliAddrNameList = multipleShippingAddresses.stream()
                    .filter(x -> x.getAddressType() == 0
                            && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                    .map(MultipleShippingAddress::getAddrName)
                    .collect(Collectors.toList());
            priceVO.setDeliAddrNameList(deliAddrNameList);

            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriArriNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
                if(multipleShippingAddress.getAddressType() == 1
                        && multipleShippingAddress.getIsGetContractPrice() != null
                        && multipleShippingAddress.getIsGetContractPrice() == 1){
                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                        arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                    }else{
                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
                        arriArriNameList.add(multipleShippingAddress.getAddrName());
                    }
                }
            }
            priceVO.setArriAreaIdList(arriAreaIdList);
            priceVO.setArriAddrNameList(arriArriNameList);

            priceVO.setCustomerId(invoice.getCustomerId());

            priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));


            Set<String> goodsNames = new HashSet<>();
            for (MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses) {
                if (multipleShippingAddress.getAddressType() == 0) {
                    List<MultipleShippingGoods> goods = multipleShippingAddress.getShippingGoodsList();
                    for (MultipleShippingGoods good : goods) {
                        goodsNames.add(good.getGoodsName());
                    }
                }
            }

            if (goodsNames.size() == 1) {
                priceVO.setGoodsName(goodsNames.stream().findFirst().get());
            }

            //货品特性
            String goodsCharacter;
            if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
                goodsCharacter = "1";
            } else if ("4".equals(invoice.getTransCode())) {
                goodsCharacter = "2";
            }else {
                goodsCharacter = "0";

            }

            priceVO.setGoodsCharacter(goodsCharacter);
            //车长
            priceVO.setCarLen(invoice.getCarLen());
            priceVO.setCarType(invoice.getCarType());

            priceVO.setNum(cn.hutool.core.convert.Convert.toBigDecimal(invoice.getNumCount(),BigDecimal.ZERO));
            priceVO.setWeight(com.ruoyi.common.core.text.Convert.toBigDecimal(invoice.getWeightCount(),BigDecimal.ZERO));
            priceVO.setVolume(cn.hutool.core.convert.Convert.toBigDecimal(invoice.getVolumeCount(),BigDecimal.ZERO));
            priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

            priceVO.setIsOversize(invoice.getIsOversize());

            priceVO.setTransCode(invoice.getTransCode());

            priceVO.setIsRoundTrip(invoice.getIsRoundTrip());
            priceVO.setVersionId(invoice.getContractpcVersionId());
            priceMap = invoiceService.getPrice(priceVO);

        }

        mmap.put("priceMap", priceMap);

        return "tms/invoice/detail";
    }

    /**
     * 发货单详情
     * @param mmap
     * @return
     */
    @RequiresPermissions("tms:invoice:detail_all")
    @GetMapping("/detail_all/{id}")
    public String detailAll(@PathVariable("id") String invoiceId, ModelMap mmap) {
        //发货单信息
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);
        if (invoice == null) {
            return "error/not_exist";
        }
        mmap.put("invoice", invoice);

//        SysDictData billingTypeDictData = sysDictDataMapper
//                .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());

//        List<SysDictData> billingTypeList = sysDictDataMapper.selectAllDictDataByType("billing_type");
//
//        SysDictData billingTypeDictData = billingTypeList.stream()
//                .filter(x -> x.getDictValue().equals(invoice.getBillingType()))
//                .findFirst().orElse(new SysDictData());
//        //税率
//        BigDecimal rate = billingTypeDictData.getNumVal1();
//
//        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
//            BigDecimal totalFeeIncludeTax = NumberUtil.div(invoice.getTotalFee(), rate, 2);
//            mmap.put("totalFeeIncludeTax", totalFeeIncludeTax);
//        }else {
//            mmap.put("totalFeeIncludeTax", invoice.getTotalFee());
//        }

        ClientPopupVO clientPopupVO = clientService.selectClientById(invoice.getCustomerId());
        mmap.put("client", clientPopupVO);

        String custSalesDept = clientPopupVO.getSalesDept();
        SysDept sysDept = sysDeptService.selectDeptById(Long.valueOf(custSalesDept));
        mmap.put("custSalesDept", sysDept.getDeptName());

        //运营部
        SysDept custSales = sysDeptService.getParentDeptInfo(custSalesDept);
        mmap.put("custSalesName", custSales.getDeptName());
        //管理部
        SysDept mgmtDept = sysDeptService.getParentDeptInfo(String.valueOf(custSales.getDeptId()));
        mmap.put("mgmtDeptName", mgmtDept.getDeptName());


        //结算组
        mmap.put("balanceDept",deptService.selectDeptByParentId(DeptIdConstant.BALANCE_DEPT_ID));
        // 驻场组
        mmap.put("stationDept",deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID));
        //所有运营部信息
        MSalesGroupExample example = new MSalesGroupExample();
        example.createCriteria().andDelFlagEqualTo((short) 0);
        List<MSalesGroup> list = salesGroupMapper.selectByExample(example);
        mmap.put("salesGroupList", list);
        // 运营组
        SysDept salesDept = sysDeptService.selectDeptById(Convert.toLong(invoice.getSalesDept(), -1L));
        mmap.put("salesDept", salesDept == null ? "" : salesDept.getDeptName());


        //发货单状态
        List<Map<String, Object>> invoiceStatus = InvoiceStatusEnum.getAllToMap();
        mmap.put("invoiceStatusList",invoiceStatus );

        //运输方式为多装多卸的  直接取多装多卸表数据
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService
                .selectAddrAndGoodsByInvoiceId(invoiceId);

        //发货地址
        List<MultipleShippingAddress> deliShippingAddressList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0).collect(Collectors.toList());
        mmap.put("deliShippingAddressList", deliShippingAddressList);
        //收货地址
        List<MultipleShippingAddress> arriShippingAddressList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 1).collect(Collectors.toList());
        mmap.put("arriShippingAddressList", arriShippingAddressList);


        List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoiceId);
        List<EntrustLot> entrustLotList = null;
        List<Map<String, Object>> bookingSendList = new ArrayList<>();
        List<Map<String, Object>> receiptPicInfoList = new ArrayList<>();

        if (entrustList != null && entrustList.size() > 0) {
            //查询对应运单
            String[] lotIds = entrustList.stream().map(Entrust::getLotId).toArray(String[]::new);
            entrustLotList = entrustLotMapper.selectEntrustLotByIds(lotIds);
            entrustLotList = entrustLotList.stream()
                    .sorted((o1, o2) -> {
                        if(o1.getLtlType() == null) {
                            return 1;
                        }
                        if(o2.getLtlType() == null) {
                            return -1;
                        }
                        return o1.getLtlType().compareTo(o2.getLtlType());
                    })
                    .collect(Collectors.toList());
            for (EntrustLot entrustLot : entrustLotList) {
                Map<String, Object> param = new HashMap<>();
                //到货信息
                List<Map<String, Object>> arrivalList = new ArrayList<>();

                //调度人
                String regUserId = entrustLot.getRegUserId();
                SysUser sysUser = sysUserService.selectUserById(Long.valueOf(regUserId));
                entrustLot.setRegUserName(sysUser.getUserName());

                //查询到货信息
                List<Entrust> collect = entrustList.stream()
                        .filter(x -> entrustLot.getEntrustLotId().equals(x.getLotId()))
                        .collect(Collectors.toList());
                for (Entrust entrust : collect) {
                    Map<String, Object> arrMap = new HashMap<>();
                    //到货
                    List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrust.getEntrustId(), "2");
                    //到货图片
                    if(entrustWorkList != null && entrustWorkList.size() > 0) {
                        arrMap.put("arrivalUser",entrustWorkList.get(0).getRegUserName());
                        arrMap.put("arrivalDate",entrustWorkList.get(0).getRegDate());

                        List<EntrustWorkPic> daohuoList = traceService.selectEntrustWorkPicById(entrustWorkList.get(0).getEntrustWorkId());
                        arrMap.put("picList", daohuoList);

                        arrivalList.add(arrMap);
                    }
                }
                param.put("arrivalList", arrivalList);

                //实际提货最早日期
                Date actDeliDate = collect.stream().map(Entrust::getActDeliDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
                param.put("actDeliDate", actDeliDate);
                Date actArriDate = collect.stream().map(Entrust::getActArriDate).filter(Objects::nonNull).min(Date::compareTo).orElse(null);
                param.put("actArriDate", actArriDate);

                List<MultipleShippingAddress> lotAddresses = multipleShippingAddressService
                        .selectAddrAndGoodsByLotId(entrustLot.getEntrustLotId());

                //发货地址
                String lotDeli = lotAddresses.stream()
                        .filter(x -> x.getAddressType() == 0)
                        .map(x -> x.getProvinceName() + x.getCityName())
                        .distinct().collect(Collectors.joining(","));
                param.put("lotDeli", lotDeli);
                //收货地址
                String lotArri = lotAddresses.stream()
                        .filter(x -> x.getAddressType() == 1)
                        .map(x -> x.getProvinceName() + x.getCityName())
                        .distinct().collect(Collectors.joining(","));
                param.put("lotArri", lotArri);
                /*
                 * 司机代收金额
                 */
                BigDecimal collectAmount = collect.stream()
                        .map(Entrust::getCollectAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                param.put("collectAmount", collectAmount);

                /*
                 * 成本
                 */
                //不含税
                Map<String, Object> yfMap = receivableReconciliationService.yfNoTaxInfo(invoiceId, entrustLot.getEntrustLotId(), null);
                param.put("yf", NumberUtil.round(Convert.toBigDecimal(yfMap.get("yf"), BigDecimal.ZERO), 2));
                param.put("yfsf", NumberUtil.round(Convert.toBigDecimal(yfMap.get("yfsf"), BigDecimal.ZERO), 2));

                //含税
                List<Map<String, Object>> payAmountList = receivableReconciliationService.yfTaxInfo(invoiceId, entrustLot.getEntrustLotId());
                BigDecimal g7 = BigDecimal.ZERO;
                BigDecimal yk = BigDecimal.ZERO;
                BigDecimal kp = BigDecimal.ZERO;
                BigDecimal bkp = BigDecimal.ZERO;

                if (payAmountList != null) {

                    g7 = payAmountList.stream()
                            .filter(x -> x.get("YFLX") != null && "G7".equals(x.get("YFLX")))
                            .map(x -> Convert.toBigDecimal(x.get("XTYF"), BigDecimal.ZERO))
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    yk = payAmountList.stream()
                            .filter(x -> x.get("YFLX") != null && "油卡".equals(x.get("YFLX")))
                            .map(x -> Convert.toBigDecimal(x.get("XTYF"), BigDecimal.ZERO))
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    kp = payAmountList.stream()
                            .filter(x -> x.get("YFLX") != null && "开票".equals(x.get("YFLX")))
                            .map(x -> Convert.toBigDecimal(x.get("XTYF"), BigDecimal.ZERO))
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    bkp = payAmountList.stream()
                            .filter(x -> x.get("YFLX") != null && "不开票".equals(x.get("YFLX")))
                            .map(x -> Convert.toBigDecimal(x.get("XTYF"), BigDecimal.ZERO))
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

//                    payAmount = Convert.toBigDecimal(dtl1.get("yf"), BigDecimal.ZERO);
                }
                param.put("cost_g7", NumberUtil.round(g7, 2));
                param.put("cost_yk", NumberUtil.round(yk, 2));
                param.put("cost_kp", NumberUtil.round(kp, 2));
                param.put("cost_bkp", NumberUtil.round(bkp, 2));

                entrustLot.setParams(param);
            }

            for (Entrust entrust : entrustList) {
                /*
                 * 查询安装信息
                 */
                Map<String, Object> map = new HashMap<>();
                List<BookingSend> bookingSends = bookingSendService.selectAllByEntrustId(entrust.getEntrustId());

                bookingSends = bookingSends.stream()
                        .sorted(Comparator.comparing(BookingSend::getRegDate))
                        .collect(Collectors.toList());

                for (BookingSend bookingSend : bookingSends) {
                    Map<String, Object> params = new HashMap<>();

                    if (StringUtils.isNotEmpty(bookingSend.getVideoTid())) {
                        List<SysUploadFile> videoFiles = sysUploadFileService.selectSysUploadFileByTid(bookingSend.getVideoTid());
                        params.put("videoFiles", videoFiles);
                    }

                    if (StringUtils.isNotEmpty(bookingSend.getPicTid())) {
                        List<SysUploadFile> picFiles = sysUploadFileService.selectSysUploadFileByTid(bookingSend.getPicTid());
                        params.put("picFiles", picFiles);
                    }
                    bookingSend.setParams(params);
                }

                if (bookingSends.size() > 0) {
                    bookingSends.sort(Comparator.comparing(BookingSend::getOperateTime));

                    map.put("entrustNo", entrust.getVbillno());
                    map.put("time", bookingSends.get(0).getOperateTime());

                    map.put("bookingSends", bookingSends);
                    bookingSendList.add(map);
                }

                /*
                 * 查询回单信息
                 */
                Map<String, Object> receiptMap = new HashMap<>();

                //查询回单照片
                List<SysUploadFile> sysUploadFiles = entrustWorkMapper.selectReceiptPicByEntrustId(entrust.getEntrustId());
                if (sysUploadFiles != null && sysUploadFiles.size() > 0) {
                    receiptMap.put("receiptUploadFiles",sysUploadFiles);
                    //查询回单人和时间
                    EntrustWork receiptPicInfo = null;
                    List<EntrustWork> picInfoList = entrustWorkMapper.selectArrivalReceiptPicAndUserByEntrustId(entrust.getEntrustId());
                    if(picInfoList != null && picInfoList.size() > 0){
                        receiptPicInfo = picInfoList.get(0);

                        receiptMap.put("receiptUploadUserName", receiptPicInfo.getRegUserName());
                        receiptMap.put("receiptUploadDate", receiptPicInfo.getRegDate());
                    }
                    //回单确认人和时间
                    receiptMap.put("receiptConfirmTime", entrust.getReceiptConfirmTime());
                    receiptMap.put("receiptConfirmUser", entrust.getReceiptConfirmUser());

                    //回单确认人和时间
                    receiptMap.put("receiptMan", entrust.getReceiptMan());
                    receiptMap.put("receiptDate", entrust.getReceiptDate());

                    receiptPicInfoList.add(receiptMap);
                }
            }
        }
        mmap.put("entrustLotList", entrustLotList);
        mmap.put("bookingSendList", bookingSendList);
        mmap.put("receiptPicInfoList", receiptPicInfoList);

        /*
         * 应收
         */
        List<ReceiveDetailVO> receiveDetailVOList = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
        receiveDetailVOList = receiveDetailVOList.stream()
                .filter(x -> x.getPayoutMark() != null && x.getPayoutMark() == 0)
                .collect(Collectors.toList());


        BigDecimal costAmount = receiveDetailVOList.stream()
                .filter(x -> x.getFreeType().equals("0") && x.getTransFeeCount() != null)
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        mmap.put("costAmount", costAmount);

        BigDecimal onWayAmountFee = receiveDetailVOList.stream()
                .filter(x -> x.getFreeType().equals("1") && x.getTransFeeCount() != null)
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        mmap.put("onWayAmountFee", onWayAmountFee);

        BigDecimal totalFee = receiveDetailVOList.stream()
                .filter(x -> x.getTransFeeCount() != null)
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        mmap.put("totalFee", totalFee);

        receiveDetailVOList.sort(Comparator.comparing(ReceiveDetailVO::getRegDate));

        for (ReceiveDetailVO receiveDetailVO : receiveDetailVOList) {
            String freeType = receiveDetailVO.getFreeType();
            String context = FreeTypeEnum.getContext(freeType);
            receiveDetailVO.setFreeType(context);

            if (FreeTypeEnum.PREPAID_OIL_CARD.getValue().equals(freeType)) {
                String feeTypeNm = dictService.selectDictLabel("cost_type_on_way", receiveDetailVO.getCostTypeOnWay()); //费用类型
                receiveDetailVO.setFreeType(context + "（" + feeTypeNm + "）");
            }

        }
        mmap.put("receiveDetailList", receiveDetailVOList);
        //费用类型
//        mmap.put("freeTypeEnum", FreeTypeEnum.getAllToMap());
        //应收单状态
        mmap.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());


        /*
         * 成本分摊
         */
        Allocation allocation = new Allocation();
        allocation.setInvoiceId(invoiceId);
        List<AllocationVO> allocationList = allocationService.selectList(allocation);
        allocationList = allocationList.stream()
                .filter(x -> x.getIncomeRemark() != null && x.getIncomeRemark() == 0)
                .collect(Collectors.toList());
        mmap.put("allocationList", allocationList);
        //应付状态
        mmap.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());

        /*
         * 三方
         */
        List<OtherFee> otherFeeList = otherFeeService.selectOtherFeeListByInvoiceId(invoiceId);
        for (OtherFee otherFee : otherFeeList) {
            if (otherFee.getRegUserId() != null) {
                SysUser sysUser = sysUserService.selectUserById(Long.valueOf(otherFee.getRegUserId()));
                otherFee.setRegUserId(sysUser.getUserName());
            }
        }
        mmap.put("otherFeeList", otherFeeList);
        //三方状态
        mmap.put("otherFeeStatusEnum", OtherFeeStatusEnum.getAllToMap());

        /*
         * 平台费
         */
//        Map<String, Object> ptfMap = receivableReconciliationService.ptfTaxInfo(invoiceId);
//        BigDecimal ptf = BigDecimal.ZERO;
//        if (ptfMap != null) {
//            ptf = Convert.toBigDecimal(ptfMap.get("ptf"), BigDecimal.ZERO);
//        }
//        mmap.put("ptf", ptf);


        //总应收
        BigDecimal transFeeCount = receiveDetailVOList.stream()
                .map(ReceiveDetailVO::getTransFeeCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //总应付
 /*       List<Map<String, Object>> costShareList = receivableReconciliationService.yfTaxInfo(invoiceId, null);
        BigDecimal costShare = BigDecimal.ZERO;
        if (costShareList != null) {
            costShare = costShareList.stream()
                    .map(x -> Convert.toBigDecimal(x.get("YF"), BigDecimal.ZERO))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
//            costShare = Convert.toBigDecimal(costShareMap.get("yf"), BigDecimal.ZERO);
        }
        mmap.put("costShare", NumberUtil.round(costShare, 2));
*/
//        BigDecimal costShare = allocationList.stream()
//                .map(AllocationVO::getCostShare)
//                .filter(Objects::nonNull)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 总三方
        BigDecimal feeAmount = otherFeeList.stream()
                .map(OtherFee::getFeeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<String, Object> dtl = receivableReconciliationService.netProfitsInfoA(invoiceId, null);

        BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal yfsf = new BigDecimal(dtl.get("yfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal dsfsf = new BigDecimal(dtl.get("dsfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);

        BigDecimal profit = NumberUtil.sub(ys, yf, yfsf, dsf, dsfsf, ptf);
        BigDecimal profitRate = ys.compareTo(BigDecimal.ZERO) == 0
                ? BigDecimal.ZERO : NumberUtil.round(NumberUtil.mul(NumberUtil.div(profit, ys), 100), 2);

        mmap.put("ys", ys);
        mmap.put("yf", yf);
        mmap.put("yfsf", yfsf);
        mmap.put("dsf", dsf);
        mmap.put("dsfsf", dsfsf);
        mmap.put("ptf", ptf);

        mmap.put("profit", profit);
        mmap.put("profitRate", profitRate);


        mmap.put("rowspanCt", NumberUtil.add(receiveDetailVOList.size(), allocationList.size(), otherFeeList.size(), 1));

        return "tms/invoice/detail_all";
    }

    /**
     * 发货单所有费用明细
     * @param map
     * @return
     */
    @RequiresPermissions("tms:invoice:cost_detail")
    @GetMapping("/cost_detail")
    public String orderDetail(ModelMap map) {
        return prefix + "/cost_detail";
    }

    /**
     * 根据客户id、提货区id、到货区id、车长、车型、运输方式 获取指导价
     * @param param
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @PostMapping("/getGuidingPrice")
    @ResponseBody
    public AjaxResult getGuidingPrice(@RequestParam Map<String, String> param) {
        param.put("pricingMethod" , "1");
        return AjaxResult.success("", invoiceService.getGuidingPriceList(param));
    }

    /**
     * 根据提货区id、到货区id、车长、车型 获取指导价
     * @param searchReferencePriceVO
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @PostMapping("/get_reference_price")
    @ResponseBody
    public AjaxResult getReferencePrice(SearchReferencePriceVO searchReferencePriceVO) {
        return referencePriceService.searchReferencePrice(searchReferencePriceVO);
    }

    /**
     * 查询是否有待审核的数据
     *
     * @param referencePriceId      指导价id
     * @param priceType             要查询的价格类型
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add", "tms:fleet:invoice:add", "tms:invoice:edit"
            , "tms:fleet:invoice:edit", "tms:check:guidePrice:check"}, logical = Logical.OR)
    @PostMapping("/has_reference_check_data")
    @ResponseBody
    public AjaxResult hasReferenceCheckData(String referencePriceId, Integer priceType) {
        ReferencePriceHistory history = new ReferencePriceHistory();
        history.setReferencePriceId(referencePriceId);
        history.setPriceType(priceType);
        history.setCheckStatus(0);
        List<ReferencePriceHistory> referencePriceHistories = referencePriceHistoryService.selectList(history);
        boolean b = referencePriceHistories != null && referencePriceHistories.size() > 0;
        return AjaxResult.success(b);
    }

    /**
     * 历史指导价
     * @param mmap
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @GetMapping("/history_reference_price")
    public String historyReferencePrice(ModelMap mmap) {
        return "tms/invoice/history_reference_price";
    }
    /**
     * 历史价价格列表
     * @param referencePriceId     指导价id
     * @param priceType            价格类别
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @PostMapping("/history_reference_price")
    @ResponseBody
    public TableDataInfo historyReferencePrice(String referencePriceId, Integer priceType) {
        List<ReferencePriceHistoryVO> list;
        if (StringUtils.isNotEmpty(referencePriceId)) {
            ReferencePriceHistoryVO historyVO = new ReferencePriceHistoryVO();
            historyVO.setReferencePriceId(referencePriceId);
            historyVO.setPriceType(priceType);

            startPage();
            list = referencePriceHistoryService.selectReferencePriceHistoryVO(historyVO);
        }else {
            list = new ArrayList<>();
        }

        return getDataTable(list);
    }



    /**
     * @param custGuidePriceId
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add", "tms:fleet:invoice:add", "tms:invoice:edit", "tms:fleet:invoice:edit"}, logical = Logical.OR)
    @PostMapping("/getActualCarrier")
    @ResponseBody
    public AjaxResult getActualCarrier(String custGuidePriceId, String carLen, String carType) {
        Map<String, Object> map = new HashMap<>();
        //指导价主表
        CustGuidePrice custGuidePrice = custGuidePriceService.selectCustGuidePriceById(custGuidePriceId);

        //明细
        GuidePriceDetail guidePriceDetail = new GuidePriceDetail();
        guidePriceDetail.setCustGuidePriceId(custGuidePriceId);
        guidePriceDetail.setDelFlag(0);
        guidePriceDetail.setCarLen(carLen);
        guidePriceDetail.setCarType(carType);
        List<GuidePriceDetail> guidePriceDetailList = guidePriceDetailService.selectGuidePriceDetailListOrderByPrice(guidePriceDetail);
        map.put("guidePriceDetailList", guidePriceDetailList);

        //查询 提货/到货省市区编码 运输方式
        TransLine transLine = transLineService.selectTransLineById(custGuidePrice.getTransLineId());
        map.put("transLine", transLine);

        //平均实际成交运费
        List<Map<String, Object>> actualSixMonth = actualCarrierService
                .selectActualSixMonth(guidePriceDetailList, custGuidePrice.getCustomerId(), transLine);
        map.put("actualSixMonth", actualSixMonth);

        return AjaxResult.success("", map);
    }

    /**
     * 历史价格
     * @param mmap
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @GetMapping("/historyGuidingPrice")
    public String historyGuidingPrice(ModelMap mmap) {
        return "tms/invoice/history_guiding_price";
    }

    /**
     * 历史价价格列表
     * @param carType       车型
     * @param carLen        车长
     * @param startAreaId   提货区id
     * @param endAreaId     到货区id
     * @param transType     运输方式
     * @param customerId    客户id
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:add","tms:fleet:invoice:add","tms:invoice:edit"
            ,"tms:fleet:invoice:edit","tms:check:guidePrice:check"},logical = Logical.OR)
    @PostMapping("/historyGuidingPrice")
    @ResponseBody
    public TableDataInfo detail(String carType, String carLen, String startAreaId, String endAreaId, String transType
            , String customerId) {
        startPage();
        Map<String, String> map = new HashMap<>();
        map.put("carType", carType);
        map.put("carLen", carLen);
        map.put("deliAreaId", startAreaId);
        map.put("arriAreaId", endAreaId);
        map.put("customerId", customerId);
        map.put("transType", transType);
        map.put("pricingMethod", "0");
        List<GuidePriceDetail> guidingPriceList = invoiceService.getGuidingPriceList(map);

        return getDataTable(guidingPriceList);
    }

    /**
     * 获取发货单货品单价 根据 客户id、提货方省市、收货方省市、车长、车型、计价方式、货品特性、件数/重量/体积 获取单价
     *
     * @return 单价
     */
    @PostMapping("/getPrice")
    @ResponseBody
    public AjaxResult getPrice(@RequestBody SearchContractPriceVO searchContractPriceVO) {
        return AjaxResult.success("", invoiceService.getPrice(searchContractPriceVO));
    }


    /**
     * 发货单确认
     *
     * @param invoiceIds
     * @param invoiceIds
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:affirm","tms:fleet:invoice:affirm"},logical = Logical.OR)
    @GetMapping("/affirm")
    public String affirm(String invoiceIds, ModelMap map) {

        String[] ids = Convert.toStrArray(invoiceIds);
        Map<String, Boolean> checkMap = invoiceService.affirmProcessCheck(ids); // 判断发货单是否要走议价确认流程

        List<InvoiceAffirmVO> affirmVOList = new ArrayList<>();
        for (String id : ids) {
            InvoiceAffirmVO invoiceAffirmVO = new InvoiceAffirmVO();
            invoiceAffirmVO.setProcess(checkMap.get(id));

            Invoice invoice = invoiceService.selectInvoiceById(id);

            invoiceAffirmVO.setInvoiceId(id);
            invoiceAffirmVO.setVbillno(invoice.getVbillno());
            //提货地址
            String deliAddr = invoice.getDeliProName() + invoice.getDeliCityName();
            deliAddr = CharSequenceUtil.replace(deliAddr, "市辖区", "");
            deliAddr = CharSequenceUtil.replace(deliAddr, "县", "");
            invoiceAffirmVO.setDeliAddr(deliAddr);
            //到货地址
            String arriAddr = invoice.getArriProName() + invoice.getArriCityName();
            arriAddr = CharSequenceUtil.replace(arriAddr, "市辖区", "");
            arriAddr = CharSequenceUtil.replace(arriAddr, "县", "");
            invoiceAffirmVO.setArriAddr(arriAddr);

            String goodsName = null;
            if (StringUtils.isNotEmpty(invoice.getGoodsName())) {
                List<String> gn = new ArrayList<>();
                gn.add(invoice.getNumCount() == null ? "" : invoice.getNumCount() + "件");
                gn.add(invoice.getWeightCount() == null ? "" : invoice.getWeightCount() + "吨");
                gn.add(invoice.getVolumeCount() == null ? "" : invoice.getVolumeCount() + "m³");
                goodsName = invoice.getGoodsName() + gn.stream().filter(Objects::nonNull).collect(Collectors.joining("/"));
            }
            invoiceAffirmVO.setGoodsName(goodsName);
            //计费方式
            if (StringUtils.isNotEmpty(invoice.getBillingMethod())) {
                String context = BillingMethod.getContext(Integer.parseInt(invoice.getBillingMethod()));
                invoiceAffirmVO.setBillingMethodName(context);
                invoiceAffirmVO.setBillingMethod(Integer.parseInt(invoice.getBillingMethod()));
            }

            //总应收
            invoiceAffirmVO.setTotalFee(invoice.getTotalFee());
            //单价
            invoiceAffirmVO.setUnitPrice(invoice.getUnitPrice());

            List<MultipleShippingAddress> multipleShippingAddresses =
                    multipleShippingAddressService.selectAddrAndGoodsByInvoiceId(invoice.getInvoiceId());

            //获取提货区id
            List<String> deliAreaIdList = multipleShippingAddresses.stream()
                    .filter(x -> x.getAddressType() == 0)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());

            //获取到货区id
            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriAddrNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
                if(multipleShippingAddress.getAddressType() == 1){
//                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
//                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getCaArriAddrName());
//                    }else{
//                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getAddrName());
//                    }
                    //改为取 该送后地址
                    arriAreaIdList.add(multipleShippingAddress.getAreaId());
                    arriAddrNameList.add(multipleShippingAddress.getAddrName());

                }
            }


            goodsName = null;
            List<InvPackGoods> invPackGoods = invPackGoodsService.selectByInvoiceId(invoice.getInvoiceId());
            List<String> goodsNames = invPackGoods.stream().map(InvPackGoods::getGoodsName).distinct().collect(Collectors.toList());
            if (goodsNames.size() == 1) {
                goodsName = goodsNames.get(0);
            }
            ClientPopupVO clientPopupVO = clientService.selectClientById(invoice.getCustomerId());

            Map<String, Object> price = autoDispatchConfigService.getPrice(clientPopupVO.getCustomerId()
                    , deliAreaIdList, arriAreaIdList, invoice.getBillingMethod()
                    , invoice.getCarLen(), invoice.getCarType(), invoice.getUnitPrice(), invoice.getCostAmount()
                    , invoice.getNumCount(), invoice.getWeightCount(), invoice.getVolumeCount(), invoice.getMileage()
                    , null, arriAddrNameList, null, goodsName, null
                    , invoice.getOtherFeeType(), invoice.getOtherFee(), invoice.getTransCode(),invoice.getIsRoundTrip());

            if ("1".equals(price.get("type"))) {

                List<Map<String, String>> configDataList = cn.hutool.core.convert.Convert
                        .convert(new TypeReference<List<Map<String, String>>>() {}, price.get("dataList"));


                List<InvoiceAffirmVO.AutoDispatchConfigVO> autoConfigList = new ArrayList<>();
                for (Map<String, String> stringStringMap : configDataList) {
                    InvoiceAffirmVO.AutoDispatchConfigVO configVO = new InvoiceAffirmVO.AutoDispatchConfigVO();

                    configVO.setAutoDispatchConfigId(stringStringMap.get("configId"));
                    configVO.setCarrName(stringStringMap.get("carrName"));
                    configVO.setCarrierId(stringStringMap.get("carrierId"));
                    configVO.setPayUnitPrice(Convert.toBigDecimal(stringStringMap.get("payUnitPrice")));
                    configVO.setPayTotalFee(Convert.toBigDecimal(stringStringMap.get("payTotalFee")));

                    configVO.setHasOnWay(stringStringMap.get("hasOnWay"));
                    configVO.setOtherFee(Convert.toBigDecimal(stringStringMap.get("otherFee")));
                    configVO.setOtherFeeType(stringStringMap.get("otherFeeType"));
                    configVO.setOtherFeeTypeText(stringStringMap.get("otherFeeTypeText"));

                    configVO.setOilAmount(Convert.toBigDecimal(stringStringMap.get("oilAmount")));
                    configVO.setCashAmount(Convert.toBigDecimal(stringStringMap.get("cashAmount")));
                    configVO.setBillingType(stringStringMap.get("billingType"));

                    Carrier carrier = carrierService.selectCarrierById(configVO.getCarrierId());
                    configVO.setRequiredFlag(carrier.getRequiredFlag());

                    autoConfigList.add(configVO);
                }

                invoiceAffirmVO.setAutoConfigList(autoConfigList);
            }

            if (StringUtils.isNotEmpty(clientPopupVO.getAutoSubsectionAddrId())) {
                Address address = addressService.selectAddressById(clientPopupVO.getAutoSubsectionAddrId());
                if (address != null) {
                    invoiceAffirmVO.setAutoSubsectionAddrId(clientPopupVO.getAutoSubsectionAddrId());
                    invoiceAffirmVO.setAutoSubsectionAddrName(address.getAddrName());
                }
            }

            invoiceAffirmVO.setAutoDispatchDeliFee(clientPopupVO.getAutoDispatchDeliFee());
            invoiceAffirmVO.setAutoDispatchPickUpFee(clientPopupVO.getAutoDispatchPickUpFee());

            List<CustMiscFeeVO> ysMiscFee = custMiscFeeConfigService.getYsPriceByInvoiceId(id);
            invoiceAffirmVO.setYsMiscFeeList(ysMiscFee);

            List<CustMiscFeeVO> yfMiscFee = custMiscFeeConfigService.getYfPriceByInvoiceId(id);
            invoiceAffirmVO.setYfMiscFeeList(yfMiscFee);

            Map<String, Object> dtl = receivableReconciliationService.netProfitsInfoA(id, null);
            BigDecimal ys = new BigDecimal(dtl.get("ys").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yf = new BigDecimal(dtl.get("yf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal yfsf = new BigDecimal(dtl.get("yfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsf = new BigDecimal(dtl.get("dsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal dsfsf = new BigDecimal(dtl.get("dsfsf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal ptf = new BigDecimal(dtl.get("ptf").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal profit = NumberUtil.sub(ys, yf, yfsf, dsf, dsfsf, ptf);
            invoiceAffirmVO.setProfit(profit);

            //开票类型
            invoiceAffirmVO.setBillingType(invoice.getBillingType());

            affirmVOList.add(invoiceAffirmVO);
        }

        map.put("affirmVOList", affirmVOList);

        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        map.put("billingMethods",billingMethods );

        return "tms/invoice/affirm";
    }
    /**
     * 发货单确认
     * @param invoice 发货单对象
     * @return
     */
    @RepeatSubmit
    @Log(title = "发货单确认", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:invoice:affirm","tms:fleet:invoice:affirm"},logical = Logical.OR)
    @PostMapping("/affirm")
    @ResponseBody
    public AjaxResult affirm(InvoiceAffirmVO invoice) {
//        String[] invoiceIdArr = invoice.getInvoiceId().split(",");
//        for(String invoiceId : invoiceIdArr) {
//            Invoice invoiceConfirm = new Invoice();
//            invoiceConfirm.setInvoiceId(invoiceId);
//            invoiceConfirm.setCorScrId(pageId);
//            AjaxResult ajaxResult = invoiceService.confirmInvoice(invoiceConfirm, 0);
//            if(ajaxResult.getCode() != 0){
//                return ajaxResult;
//            }
//        }
        // 合同价客户 + 议价发货单 => 审批流程
        final List<InvoiceAffirmVO.AffirmVO> affirmList = invoice.getAffirmList();
        String[] invoiceIds = new String[affirmList.size()];
        StringBuilder errMsg = new StringBuilder();
        for (int i = 0; i < invoiceIds.length; i++) {
            invoiceIds[i] = affirmList.get(i).getInvoiceId();
            Invoice db = invoiceService.selectInvoiceById(invoiceIds[i]);
            try {
                AjaxResult check = invoiceService.confirmInvoiceCheck(db); // 确认前校验
                if (check.getCode() != 0) {
                    if (errMsg.length() > 0) {
                        errMsg.append("<br>");
                    }
                    errMsg.append(db.getVbillno()).append("：").append(check.getMsg());
                }
            } catch (BusinessException e) {
                if (errMsg.length() > 0) {
                    errMsg.append("<br>");
                }
                errMsg.append(db.getVbillno()).append("：").append(e.getMessage());
            }
        }
        if (errMsg.length() > 0) {
            return AjaxResult.error(errMsg.toString());
        }
        Map<String, Boolean> checkMap = invoiceService.affirmProcessCheck(invoiceIds);
        for (int i = 0; i < affirmList.size(); i++) {
            if (checkMap.get(affirmList.get(i).getInvoiceId())) {
                InvoiceAffirmVO.AffirmVO affirmVO = affirmList.get(i);
                boolean b = invoiceService.submitWecomProcess(affirmVO);
                if (b) { // 如果未调教审批，依然走旧的发货单确认
                    affirmList.remove(i);
                    i--;
                }
            }
        }
        if (affirmList.size() == 0) {
            return AjaxResult.success();
        }
        return invoiceService.confirmInvoice(invoice);
    }


    /**
     * 更新合同价
     *
     * @return 单价
     */
    @RepeatSubmit
    @RequiresPermissions(value = {"tms:invoice:updateContractPrice"},logical = Logical.OR)
    @PostMapping("/updateContractPrice")
    @ResponseBody
    public AjaxResult updateContractPrice(String invoiceIds) {
        return invoiceService.updateContractPrice(invoiceIds);
    }

    /**
     * 更新合同价
     *
     * @return 单价
     */
    @RepeatSubmit
    @RequiresPermissions(value = {"tms:invoice:updateContractPrice_1"},logical = Logical.OR)
    @PostMapping("/updateContractPrice_1")
    @ResponseBody
    public AjaxResult updateContractPrice_1() {
        return invoiceService.updateContractPrice_1();
    }


    /**
     *
     *
     * @param invoiceId
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:switchBillingMethod"},logical = Logical.OR)
    @GetMapping("/switchBillingMethod")
    public String switchBillingMethod(String invoiceId, ModelMap map) {
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);
        map.put("invoice", invoice);
        SearchContractPriceVO priceVO = new SearchContractPriceVO();
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService
                .selectAddrAndGoodsByInvoiceId(invoice.getInvoiceId());

        //获取提货区id
        List<String> deliAreaIdList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0
                        && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                .map(MultipleShippingAddress::getAreaId)
                .collect(Collectors.toList());
        priceVO.setDeliAreaIdList(deliAreaIdList);
        //获取提货地址名称
        List<String> deliAddrNameList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0
                        && x.getIsGetContractPrice() != null && x.getIsGetContractPrice() ==1)
                .map(MultipleShippingAddress::getAddrName)
                .collect(Collectors.toList());
        priceVO.setDeliAddrNameList(deliAddrNameList);

        //获取到货区id
        List<String> arriAreaIdList = new ArrayList<>();
        List<String> arriArriNameList = new ArrayList<>();

        for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
            if(multipleShippingAddress.getAddressType() == 1
                    && multipleShippingAddress.getIsGetContractPrice() != null
                    && multipleShippingAddress.getIsGetContractPrice() == 1){
                if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
                    arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
                    arriArriNameList.add(multipleShippingAddress.getCaArriAddrName());
                }else{
                    arriAreaIdList.add(multipleShippingAddress.getAreaId());
                    arriArriNameList.add(multipleShippingAddress.getAddrName());
                }
            }
        }
        priceVO.setArriAreaIdList(arriAreaIdList);
        priceVO.setArriAddrNameList(arriArriNameList);

        List<String> goodsNameList = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType() == 0)
                .flatMap(x -> x.getShippingGoodsList().stream()
                        .map(MultipleShippingGoods::getGoodsName))
                .distinct()
                .collect(Collectors.toList());

        if (goodsNameList.size() == 1) {
            priceVO.setGoodsName(goodsNameList.get(0));
        }
        //客户id
        priceVO.setCustomerId(invoice.getCustomerId());
        //车长
        priceVO.setCarLen(invoice.getCarLen());
        priceVO.setCarType(invoice.getCarType());
        //货品特性
        String goodsCharacter;
        if ("15".equals(invoice.getTransCode()) || "16".equals(invoice.getTransCode())) {
            goodsCharacter = "1";
        } else if ("4".equals(invoice.getTransCode())) {
            goodsCharacter = "2";
        }else {
            goodsCharacter = "0";
        }
        priceVO.setGoodsCharacter(goodsCharacter);

//        String billingMethod = invoice.getBillingMethod().equals("3") ? "4" : "3";
//        priceVO.setBillingMethod(Integer.valueOf(billingMethod));
        priceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));

        priceVO.setNum(BigDecimal.valueOf(invoice.getNumCount()));
        priceVO.setWeight(BigDecimal.valueOf(invoice.getWeightCount()));
        priceVO.setVolume(BigDecimal.valueOf(invoice.getVolumeCount()));
        priceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));

        priceVO.setIsOversize(invoice.getIsOversize());

        priceVO.setTransCode(invoice.getTransCode());

        int isRoundTrip = invoice.getIsRoundTrip() == 0 ? 1 : 0;
        priceVO.setIsRoundTrip(isRoundTrip);

        Map<String, String> price = invoiceService.getPrice(priceVO);
        map.put("price", price);

        SysDictData billingType = dictService.selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
        map.put("taxRate", billingType.getNumVal1());
        map.put("billingType", invoice.getBillingType());

        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        map.put("billingMethods", billingMethods);

        return "tms/invoice/switch_billing_method";
    }

    @RepeatSubmit
    @Log(title = "包车类型转换", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:invoice:switchBillingMethod"},logical = Logical.OR)
    @PostMapping("/switchBillingMethod")
    @ResponseBody
    public AjaxResult switchBillingMethod(String invoiceId) {

        return invoiceService.switchBillingMethod(invoiceId);
    }
    /**
     * 跳转反确认页面
     * @param map
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:back_confirm","tms:fleet:invoice:back_confirm"},logical = Logical.OR)
    @GetMapping("/back_confirm/{id}/{deConfirmation}")
    public String backConfirmPage(ModelMap map, @PathVariable String id, @PathVariable String deConfirmation) {
        map.put("invoiceId", id);
        map.put("deConfirmation", deConfirmation);
        return "tms/invoice/back_confirm";
    }

    /**
     * 反确认操作  合并到back_confirm_pick一个页面操作
     * @param unconfirm 发货单对象
     * @return
     */
    @RepeatSubmit
    @Log(title = "发货单反确认", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:invoice:back_confirm","tms:fleet:invoice:back_confirm"},logical = Logical.OR)
    @PostMapping("/back_confirm")
    @ResponseBody
    public AjaxResult backConfirm(Unconfirm unconfirm) {
        return invoiceService.backConfirmInvoice(unconfirm);
    }

    /**
     * 运单选择
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @RequiresPermissions("tms:car:list")
    @RequestMapping("/selectLot")
    public String selectLot() {
        return prefix + "/selectLot";
    }

    /**
     * 跳转关闭页面
     * @param map
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:close","tms:fleet:invoice:close"},logical = Logical.OR)
    @GetMapping("/close/{id}")
    public String close(ModelMap map, @PathVariable String id) {
        map.put("invoiceId", id);
        return prefix + "/close";
    }

    /**
     * 关闭发货单
     * @param invoice
     * @return
     */
    @Log(title = "发货单关闭", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:invoice:close","tms:fleet:invoice:close"},logical = Logical.OR)
    @PostMapping("/close")
    @ResponseBody
    public AjaxResult close(Invoice invoice,Unconfirm unconfirm) {
        return invoiceService.close(invoice,unconfirm);
    }

    /**
     * 跳转第三方费用录入页面
     * @param map
     * @param id 发货单id
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_entry")
    @GetMapping("/feeEntry/{id}")
    public String feeEntry(ModelMap map, @PathVariable String id) {
        map.put("invoiceId", id);
        return "tms/invoice/fee_entry";
    }


    /**
     * 新添第三方费用
     * @param otherFee 第三方费用
     * @return
     */
    @RepeatSubmit
    @Log(title = "新添第三方费用", businessType = BusinessType.INSERT)
    @RequiresPermissions("tms:invoice:fee_entry")
    @PostMapping("/insertFeeEntry")
    @ResponseBody
    public AjaxResult insertFeeEntry(OtherFee otherFee) {
        return invoiceService.insertFeeEntry(otherFee);
    }

    /**
     * 跳转第三方费用申请页面
     * @param map
     * @param id 发货单id
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply")
    @GetMapping("/fee_apply/{id}/{isClose}")
    public String otherFeeApply(ModelMap map, @PathVariable String id , @PathVariable String isClose) {
        map.put("invoiceId", id);
        map.put("isClose", isClose);
        //第三方费用 状态
        map.put("otherFeeStatusList", OtherFeeStatusEnum.getAllToMap());
        //第三方费用 新建状态
        map.put("otherFeeStatusNew", OtherFeeStatusEnum.NEW.getValue());

        return "tms/invoice/fee_apply";
    }

    /**
     * 第三方费用申请 列表
     * @param otherFee 第三方费用
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply")
    @PostMapping("/fee_apply/list")
    @ResponseBody
    public TableDataInfo feeApply(OtherFee otherFee) {
        startPage();
        List<OtherFee> list = invoiceService.selectOtherFeeApplyListByInvoiceId(otherFee);
        return getDataTable(list);
    }

    /**
     * 跳转第三方费用修改页面
     * @param map
     * @param id 发货单id
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply:edit")
    @GetMapping("/fee_apply/edit/{id}")
    public String otherFeeApplyEdit(ModelMap map, @PathVariable String id) {
        OtherFee otherFee = otherFeeService.selectOtherFeeById(id);
        map.put("otherFee", otherFee);
        return prefix + "/fee_apply_edit";
    }

    /**
     * 第三方费用修改
     * @param otherFee 第三方费用
     * @return
     */
    @RepeatSubmit
    @Log(title = "修改第三方费用", businessType = BusinessType.UPDATE)
    @RequiresPermissions("tms:invoice:fee_apply:edit")
    @PostMapping("/fee_apply/edit")
    @ResponseBody
    public AjaxResult otherFeeApplyEdit(OtherFee otherFee) {
        return invoiceService.updateOtherFee(otherFee);
    }

    /**
     * 跳转第三方费用申请页面
     * @param map
     * @param id 发货单id
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply:apply")
    @GetMapping("/fee_apply/apply/{id}")
    public String feeApplyEdit(ModelMap map, @PathVariable String id) {
        OtherFee otherFee = otherFeeService.selectOtherFeeById(id);
        map.put("otherFee", otherFee);
        map.put("type", "apply");
        if (otherFee.getPayType() == null || otherFee.getPayType().equals(0)) {
            // 现金类处理含税构成
            Map<String, Object> otherFeeTax = otherFeeService.getOtherFeeTax(id);
            String taxTxt = (String) otherFeeTax.get("taxTxt");
            if (taxTxt == null) {
                taxTxt = "6:" + otherFeeTax.get("feeAmount");
            }
            map.put("taxTxt", taxTxt);
            map.put("receiptList", otherFeeService.listInvoiceReceipt(id));
        }
        return "tms/invoice/fee_apply_edit";
    }

    /**
     * 第三方费用申请
     * @param otherFee 第三方费用
     * @return
     */
    @RepeatSubmit
    @Log(title = "第三方费用申请", businessType = BusinessType.OTHER)
    @RequiresPermissions("tms:invoice:fee_apply:apply")
    @PostMapping("/fee_apply/apply")
    @ResponseBody
    public AjaxResult saveFeeApply(OtherFee otherFee) {
        return invoiceService.otherFeeApply(otherFee);
    }

    /**
     * 第三方费用删除
     * @param ids 第三方费用id
     * @return
     */
    @Log(title = "第三方费用删除", businessType = BusinessType.DELETE)
    @RequiresPermissions("tms:invoice:fee_apply:del")
    @PostMapping("/fee_apply/delete")
    @ResponseBody
    public AjaxResult feeApplyDelete(String ids) {
        return invoiceService.deleteOtherFeeApply(ids);
    }

    /**
     * 跳转第三方费用审核记录页面
     * @param map
     * @param otherFeeId 第三方费用id
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply:check_record")
    @GetMapping("/fee_apply/check_record")
    public String checkRecord(ModelMap map, @RequestParam String otherFeeId){
        map.put("otherFeeId", otherFeeId);
        return prefix + "/check_record";
    }

    /**
     * 第三方费用审核记录
     * @param businessCheck 第三方费用
     * @return
     */
    @RequiresPermissions("tms:invoice:fee_apply:check_record")
    @PostMapping("/fee_apply/check_record_list")
    @ResponseBody
    public TableDataInfo checkRecord(BusinessCheck businessCheck) {
        startPage();
        List<BusinessCheck> list = businessCheckService.selectBusinessCheckList(businessCheck);
        return getDataTable(list);
    }

    @RequestMapping("/checkClose")
    @ResponseBody
    public AjaxResult checkClose(String invoiceId){
        //判断发货单状态是为新建 或者对应运段未调度
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);

        if (invoice.getVbillstatus().equals(InvoiceStatusEnum.NEW.getValue()) || 0 == invoice.getSegmentStatus()) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error("只有新建状态或运段未调度才能关闭发货单！");

//            boolean closeFlag = true;
//            //判断发货单下委托单是否都关闭
//            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoiceId);
//            for (Entrust entrust : entrustList) {
//                if (!entrust.getVbillstatus().equals(EntrustStatusEnum.CLOSE.getValue())) {
//                    closeFlag = false;
//                    break;
//                }
//            }
//            if (closeFlag) {
//                return AjaxResult.success();
//            } else {
//                return AjaxResult.error("只有新建状态或运段未调度才能关闭发货单！");
//            }
        }
    }

    /**
     * 跳转指导价页面
     * @param map
     * @param ids 发货单id
     * @return
     */
//    @RequiresPermissions("tms:invoice:getGuidePrice")
//    @GetMapping("/getGuidePrice/{ids}")
//    public String getGuidePrice(ModelMap map, @PathVariable String ids) {
//        BigDecimal pageGuidingPrice = invoiceService.getPageGuidingPrice(ids);
//        map.put("pageGuidingPrice", pageGuidingPrice);
//        map.put("ids", ids);
//        return "tms/invoice/guide_price";
//    }

    /**
     * 保存指导价
     * @param ids 发货单id
     * @return
     */
    @RequiresPermissions("tms:invoice:getGuidePrice")
    @PostMapping("/saveGuidePrice")
    @ResponseBody
    public AjaxResult saveGuidePrice(String ids) {
        return invoiceService.saveGuidePrice(ids);
    }

    /**
     * 验证发货单要求提货日期是否超过指定天数
     * @param invoiceId
     * @return
     */
    @PostMapping("/checkInvoiceOverDate")
    @ResponseBody
    public AjaxResult checkInvoiceOverDate(String invoiceId){
        String[] invoiceIdArr = invoiceId.split(",");
        for(String id : invoiceIdArr){
            Invoice invoice = invoiceService.selectInvoiceById(id);
            if(invoice != null){
                if(DateUtils.checkDateDistinctMoreDays(invoice.getReqDeliDate())){
                    return AjaxResult.error("要求提货日期五天之后收入成本都不允许调整，如需调整必须走调账流程！");
                }
            }else{
                return AjaxResult.error("未查询到发货单！");
            }
        }
        return AjaxResult.success();
    }


    /**
     * 验证发货单要求提货日期是否超过指定天数
     * @param lotIds
     * @return
     */
    @PostMapping("/checkInvoiceOverDateByLotIds")
    @ResponseBody
    public AjaxResult checkInvoiceOverDateByLotIds(String lotIds){
        String[] lotIdArr = lotIds.split(",");
        for(String lotId : lotIdArr){
            List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(lotId);
            for(Entrust entrust : entrusts){
                Invoice invoice = invoiceService.selectInvoiceById(entrust.getOrderno());
                if(invoice != null){
                    if(DateUtils.checkDateDistinctMoreDays(invoice.getReqDeliDate())){
                        return AjaxResult.error("要求提货日期五天之后收入成本都不允许调整，如需调整必须走调账流程！");
                    }
                }else{
                    return AjaxResult.error("未查询到发货单！");
                }
            }
        }
        return AjaxResult.success();
    }


    /**
     * 验证发货单要求提货日期是否超过指定天数
     * @param lotIds
     * @return
     */
    @PostMapping("/checkInvoiceOverDateByPayDetailIds")
    @ResponseBody
    public AjaxResult checkInvoiceOverDateByPayDetailIds(String payDetailIds){
        String[] payDetailIdArr = payDetailIds.split(",");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        for(String payDetailId : payDetailIdArr){
            PayDetail payDetail = payDetailMapper.selectPayDetailById(payDetailId);
            if(payDetail.getIsAdjust() == 1){
                int cnt = closeAccountMapper.selectCloseAccountByYearMonth(sdf.format(payDetail.getRegDate()));
                if(cnt > 0){
                    return AjaxResult.error("调整单创建月份已关账，如需调整必须走调账流程！");
                }
            }else{
                List<Entrust> entrusts = entrustMapper.selectEntrustByLotId(payDetail.getLotId());
                for(Entrust entrust : entrusts){
                    Invoice invoice = invoiceService.selectInvoiceById(entrust.getOrderno());
                    if(invoice != null){
                        int cnt = closeAccountMapper.selectCloseAccountByYearMonth(sdf.format(invoice.getReqDeliDate()));
                        if(cnt > 0){
                            return AjaxResult.error("单据要求提货月份已关账，如需调整必须走调账流程！");
                        }
                    }else{
                        return AjaxResult.error("未查询到发货单！");
                    }
                }
            }
        }
        return AjaxResult.success();
    }

    /**
     * 获取操作历史记录
     * @param map
     * @param id
     * @return
     */
    @RequiresPermissions("tms:invoice:operation_history")
    @GetMapping("/operation_history/{id}")
    public String operationHistory(ModelMap map, @PathVariable String id) {
        Invoice invoice = invoiceService.selectInvoiceById(id);

        /*
         * 发货单
         */
        map.put("invoice", invoice);

        /*
         * 应收
         */
        List<ReceiveDetailVO> receiveDetailVOList = receiveDetailService.selectReceiveByInvoiceId(invoice.getInvoiceId());
        receiveDetailVOList.sort(Comparator.comparing(ReceiveDetailVO::getRegDate));
        map.put("receiveDetailList", receiveDetailVOList);

        /*
         *  委托单
         */
        List<OperationHistoryVO> operationHistoryVOList = new ArrayList<>();
        List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
        for (Entrust entrust : entrustList) {
            OperationHistoryVO operationHistoryVO = new OperationHistoryVO();
            BeanUtils.copyBeanProp(operationHistoryVO, entrust);

            EntrustWork entrustWorkSele = new EntrustWork();
            entrustWorkSele.setEntrustId(entrust.getEntrustId());
            entrustWorkSele.setDelFlag(0);
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSele);
            for (EntrustWork entrustWork : entrustWorks) {
                if ("1".equals(entrustWork.getWorkType())) {
                    SysUser sysUser = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                    //提货
                    operationHistoryVO.setPickUpUserName(sysUser.getUserName());
                    //提货时间
                    operationHistoryVO.setPickUpDate(entrustWork.getRegDate());
                } else if ("2".equals(entrustWork.getWorkType())) {
                    SysUser sysUser = sysUserService.selectUserById(Long.valueOf(entrustWork.getRegUserId()));
                    //到货
                    operationHistoryVO.setArrivalsUserName(sysUser.getUserName());
                    //到货时间
                    operationHistoryVO.setArrivalsDate(entrustWork.getRegDate());
                }
            }

            //应付
            List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(entrust.getLotId());
            payDetailList.sort(Comparator.comparing(PayDetail::getRegDate));
            operationHistoryVO.setPayDetailList(payDetailList);
//            Allocation allocation = new Allocation();
//
//            allocationService.selectAllocationList(allocation);
            //定位信息
            List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrust.getEntrustId());
            carLocusList.sort(Comparator.comparing(CarLocus::getTrackingTime));
            operationHistoryVO.setCarLocusList(carLocusList);

            operationHistoryVOList.add(operationHistoryVO);
        }

        map.put("operationHistoryVOList", operationHistoryVOList);

        return "tms/invoice/operation_history";
    }

    @PostMapping(value = "exportReceipt")
    public String exportReceipt(Invoice param, ModelMap map, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> list = invoiceService.listReceiptForExport(param);
        map.put("list", objectMapper.writeValueAsString(list));
        response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("回单", "UTF8")+".html");
        return prefix + "/receipt_export";
    }

    @GetMapping("otherFeeInput")
    public String otherFeeInput(String invoiceIds,ModelMap modelMap){
        String[] ids = invoiceIds.split(",");
        //根据发货单ID查询数据
        List<Invoice> invoiceList = invoiceMapper.selectInvoiceByIds(ids);
        modelMap.put("invoiceList",invoiceList);

        if(invoiceList != null && invoiceList.size() > 0){
            Invoice invoice = invoiceList.get(0);
            modelMap.put("invoice",invoice);
            modelMap.put("invoiceListSize",invoiceList.size());
        }else{
            modelMap.put("invoiceListSize",0);
        }

        return prefix + "/otherFeeInput";
    }

    @PostMapping("saveOtherFee")
    @ResponseBody
    public AjaxResult saveOtherFee(@RequestBody InvoiceVO vo){

        return invoiceService.saveOtherFee(vo);
    }

    /**
     *
     * @param
     * @param map
     * @return
     */
    @GetMapping("diaInvoice")
    public String diaInvoice(@RequestParam(required = true) String customerId,String invoiceIds, ModelMap map) {
       /* //获取关账时间
        List<CloseAccount> closeAccountList = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", closeAccountList);
        //发货单状态list
        List<Map<String, Object>> invoiceStatusList = InvoiceStatusEnum.getAllToMap();
        map.put("invoiceStatusList", invoiceStatusList);
        //发货单map
        Map<String, Object> value = EnumUtil.getNameFieldMap(InvoiceStatusEnum.class, "value");
        map.put("invoiceStatusMap", value);
        // 运营部
        map.put("salesDept", deptService.selectDeptByParentIds(DeptIdConstant.getSalesDeptId()));
*/




        //是否是车队数据
        map.put("customerId", customerId);
        map.put("invoiceIds",invoiceIds);
        return "tms/invoice/diaInvoice";
    }

    @RequestMapping("/getInvoiceByVbillno")
    @ResponseBody
    public AjaxResult getInvoiceByVbillno(@RequestParam(required = true) String vbillno){
        String[] invoiceArr = new String[1];
        invoiceArr[0] = vbillno;
        List<Invoice> invoices = invoiceService.selectInvoiceByInvoiceVbillnos(invoiceArr);
        if(invoices != null && invoices.size() > 0){
            return AjaxResult.success(invoices.get(0));
        }else{
            return AjaxResult.error("未查询到该发货单");
        }
    }

    private final ExecutorService es = Executors.newFixedThreadPool(10, r -> {
        Thread t = new Thread(r);
        t.setName(Thread.currentThread().getName() + ">>" + t.getName());
        return t;
    });

    @RepeatSubmit
    @PostMapping("importBatch")
    @ResponseBody
    public AjaxResult importBatch(@RequestBody List<Map<String, Object>> list) throws ParseException {
        AjaxResult ajaxResult = invoiceService.importBatch(list, false);
        if (ajaxResult.getCode() == 0) {
            // 最后一步：根据结果，推送消息等后续操作
            List<Invoice> data = (List<Invoice>) ajaxResult.getData();
            for (int i = 0; i < data.size(); i++) {
                final Invoice invoice = data.get(i);
                es.execute(() -> {
                    try {
                        // 0普通 1紧急
                        if ("1".equals(invoice.getUrgentLevel())) {
                            //消息推送tms移动端
                            invoiceService.sendTmsMobile(invoice);
                        }
                    } catch (Exception e) {
                        logger.error("推送tms移动端：", e);
                    }
                    try {
                        //推送企业微信机器人
                        wechatHookService.sendMsgCreateInvoiceData(invoice.getTransLineId(), invoice, invoice.getInvPackGoodsList(), invoice.getShippingAddressList());
                    } catch (Exception e) {
                        logger.error("发货单导入后推送企业微信机器人：", e);
                    }
                });
            }
            ajaxResult.setData(null);
        }
        return ajaxResult;
    }


    /**
     *
     * @param map
     * @return
     */
    @RequiresPermissions("tms:invoice:ltl")
    @GetMapping("/ltl")
    public String invoiceLtlAll(ModelMap map) {
        //发货单状态list
        List<Map<String, Object>> invoiceStatusList = InvoiceStatusEnum.getAllToMap();
        map.put("invoiceStatusList", invoiceStatusList);
        //发货单map
        Map<String, Object> value = EnumUtil.getNameFieldMap(InvoiceStatusEnum.class, "value");
        map.put("invoiceStatusMap", value);

        return "tms/invoice/invoice_ltl";
    }


    /**
     * 查询发货单列表
     * @param invoice 发货单对象
     * @return 发货单list
     */
    @RequiresPermissions("tms:invoice:ltl:list")
    @PostMapping("/ltl/list")
    @ResponseBody
    public TableDataInfo invoiceLtlAll(InvoiceLtlVO invoice) {
        startPage();
        List<InvoiceLtlVO> list = invoiceService.selectInvoiceLtlAllList(invoice);
        return getDataTable(list);
    }

    /**
     * 导出发货单列表
     */
    @RequiresPermissions("tms:invoice:ltl:export")
    @PostMapping("/ltl/export")
    @ResponseBody
    public AjaxResult ltlExport(InvoiceLtlVO invoice) {
        List<InvoiceLtlVO> list = invoiceService.selectInvoiceLtlAllList(invoice);
        ExcelUtil<InvoiceLtlVO> util = new ExcelUtil<>(InvoiceLtlVO.class);
        return util.exportExcel(list, "零担");
    }

    /**
     * 跳转发货单选择列表
     * @param map
     * @return
     */
    @RequiresPermissions("tms:invoice:choose:invoiceList")
    @GetMapping("/toInvoiceReceiveDetailList/{customerId}")
    public String invoiceReceiveDetailList(ModelMap map, @PathVariable("customerId") String customerId) {
        map.put("customerId", customerId);
        map.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());
        return "tms/invoice/invoiceReceiveDetailList";
    }

    /**
     * 发货单列表
     * @param invoice
     * @return
     */
    @RequiresPermissions("tms:invoice:choose:invoiceList")
    @PostMapping("/invoiceReceiveDetailList")
    @ResponseBody
    public TableDataInfo chooseCustomerInivoice(Invoice invoice) {
        startPage();
        List<InvoiceReceiveDetailVO> list = invoiceMapper.selectInvoiceReceiveDetailList(invoice);
        return getDataTable(list);
    }

    @RequestMapping("/handleZJHYApi")
    @ResponseBody
    public AjaxResult handleZJHYApi(@RequestParam(name = "flag", required = false, defaultValue = "false") boolean flag) {
        //Token请求
        String tokenUrl = "https://tms.hengyi.com/login/login";

        //请求参数
        Map<String, Object> sendParams = new HashMap<>();
        sendParams.put("userCode", "990000902");
        sendParams.put("userPassword", "e1a42180caa49f5fabc51836a8bd197d");
        sendParams.put("verificationCode", "");

        String token = null;

        HttpResponse result = HttpRequest.post(tokenUrl).body(JSON.toJSONString(sendParams)).execute();
        Map<String, List<String>> headers = result.headers();
        List<String> cookieList = headers.get("Set-Cookie");
        for(String cookie : cookieList){
            if(cookie.startsWith("RequestToken")){
                token = cookie.split(";")[0].split("=")[1];
            }
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH,-1);

        //列表查询
        Map<String, Object> listSendParams = new HashMap<>();
        List<Long> createTimeList = new ArrayList<>();
        createTimeList.add(cal.getTimeInMillis());
        cal.setTime(new Date());
        createTimeList.add(cal.getTimeInMillis());
        listSendParams.put("createTimeList",createTimeList);

        Map<String,Object> pageParamNewVO = new HashMap<>();
        pageParamNewVO.put("curPage",1);
        pageParamNewVO.put("pageSize",100);
        pageParamNewVO.put("sortDir","");
        pageParamNewVO.put("sortIndx","");
        listSendParams.put("pageParamNewVO",pageParamNewVO);

        listSendParams.put("mainStatus","100");

        List<Integer> approvalStatusList = new ArrayList<>();
        approvalStatusList.add(2);
        approvalStatusList.add(5);
        listSendParams.put("approvalStatusList",approvalStatusList);


        /**
         * [
         * 		"",
         * 		"",
         * 		"T20240411000904", 2 用
         * 		" 未排车", 3
         * 		"", 4
         * 		"福建逸锦", 5
         * 		"福建逸锦短纤装运点", 6 用
         * 		"1", 7
         * 		"厦门东屿行纺织新材料有限公司", 8 用
         * 		"", 9
         * 		"2024-04-11 16:00", 10 用
         * 		"",
         * 		"",
         * 		"0086544199", 13 用
         * 		"江苏铭源物流有限公司", 14
         * 		"",
         * 		"",
         * 		"84", 17 用
         * 		"31.92", 18 用
         * 		"广东省肇庆市鼎湖区桂城街道广东筆庆市鼎湖区创业路", 19 用
         * 		"广东省/肇庆市/鼎湖区/桂城街道", 20 用
         * 		"短纤", 21 用
         * 		"短纤-1.56dtex*38mm-YAAS143801-优等", 22 用
         * 		"17768291989", 23 用
         * 		"胡文涛", 24 用
         * 		"否", 25
         * 		"未出库", 26
         * 		""
         * 	]
         *
         */

        //列表url
        String listUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/waybill/pageProductQueryByCondition";
        String listRes = HttpRequest.post(listUrl).header("Content-Type", "application/json;charset=UTF-8")
                .header("Token", token).body(JSON.toJSONString(listSendParams)).execute().body();

        List<List<String>> paras = new ArrayList<List<String>>();;
        JSONObject listResJson = JSON.parseObject(listRes);
        JSONArray jsonArray = listResJson.getJSONObject("result").getJSONArray("data");
        for(int i = 0; i < jsonArray.size(); i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            List<String> para = new ArrayList<String>();
            para.add("");
            para.add(jsonObject.getString("orderNo"));
            para.add(jsonObject.getString("waybillNo"));
            para.add("");
            para.add("");
            para.add("");
            para.add(jsonObject.getString("extLoadingPoint"));
            para.add("");
            para.add(jsonObject.getString("customerName"));//8
            para.add("");
            para.add(jsonObject.getString("modifiedTime"));//10
            para.add("");
            para.add("");
            para.add(jsonObject.getString("extStrSpaOrder"));//13
            para.add("");
            para.add("");
            para.add("");
            para.add(jsonObject.getString("totalCapacity"));
            para.add(jsonObject.getString("totalWeight"));
            para.add(jsonObject.getString("receivingAreaAddress"));//19
            para.add(jsonObject.getString("receivingAreaName"));//20
            para.add(jsonObject.getString("goodsGroup"));
            para.add(jsonObject.getString("goodsName"));
            para.add(jsonObject.getString("extReceiverPhone"));
            para.add(jsonObject.getString("extReceiver"));
            para.add("");
            para.add("");
            para.add("");
            paras.add(para);
        }
        try {
            return handleZJHYInvoice(paras,flag,token);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return AjaxResult.error("处理异常，请联系管理员");
    }

    @RequestMapping("/handleZJHYInvoice")
    @ResponseBody
    public AjaxResult handleZJHYInvoice(@RequestBody List<List<String>> paras,boolean flag,String token) throws ParseException {
        //获取系统配置信息
        CargoConfig config = cargoConfigMapper.selectByPrimaryKey("zjhy");

        for(List<String> para : paras){
            StringBuffer errorMsg = new StringBuffer();

            String originNo = para.get(2);
            String custOrderNo = para.get(13);
            if(StringUtils.isEmpty(originNo)){
                continue;
            }
            //判断单据是否推送过
            Integer successCnt = invoiceMapper.countSuccessHyInvoiceByOriginNo(originNo);
            if(successCnt > 0){
                continue;
            }


            //判断失败次数 超过3次不执行
            Integer failCnt = invoiceMapper.countFailHyInvoiceByOriginNo(originNo);
            if(failCnt >= 3 && !flag){
                continue;
            }

            InvoiceAddVO invoice = new InvoiceAddVO();
            //货品信息
            String goodsName = para.get(22);
            String goodsNameInsert = para.get(21);
            //客户信息
            ClientPopupVO clientPopupVO ;
            if(goodsName.contains("S")){
                goodsNameInsert = "水刺";
                clientPopupVO = clientService.selectClientById("1da91a2635f147baae86a049d0062e6e");
            }else{
                clientPopupVO = clientService.selectClientById("193b46349f844b75a1d3063335f96684");
            }
            invoice.setCustomerId(clientPopupVO.getCustomerId());
            invoice.setCustName(clientPopupVO.getCustName());
            invoice.setCustAbbr(clientPopupVO.getCustAbbr());
            invoice.setCustCode(clientPopupVO.getCustCode());
            invoice.setBalaCorpId(clientPopupVO.getBalaCorp());
            invoice.setStationDept(clientPopupVO.getStationDept());
            invoice.setStationDeptName(clientPopupVO.getStationDeptName());
            invoice.setBalaDept(clientPopupVO.getBalaDept());
            invoice.setSalesDept(clientPopupVO.getSalesDept());
            invoice.setPsndoc(clientPopupVO.getPsndoc());
            invoice.setBalaType("1");
            invoice.setAppDeliContact(clientPopupVO.getAppDeliContact());
            invoice.setAppDeliMobile(clientPopupVO.getAppDeliMobile());
            invoice.setReferenceRate(clientPopupVO.getReferenceRate());
            //结算客户
            Client custBala = clientService.getDefaultCustBalaByCustomerId(clientPopupVO.getCustomerId());
            if(custBala == null){
                errorMsg.append("未找到结算客户;");
            }else{
                invoice.setBalaName(custBala.getCustName());
                invoice.setBalaCode(custBala.getCustCode());
                invoice.setBalaCustomerId(custBala.getCustomerId());
            }

            SearchContractPriceVO searchContractPriceVO = new SearchContractPriceVO();
            List<MultipleShippingAddress> shippingAddressList = new ArrayList<>();
            //查询提货地址
            String deliCustName = para.get(6).replace("装运点","");
            AddressDTO address = new AddressDTO();
            address.setAddrType("1");
            Map params = new HashMap<String, Object>();
            params.put("addrNameOrContact",deliCustName);
            address.setParams(params);

            //提货地到货地
            String deliAddr = "";
            String arriAddr = "";

            /** 提货地址名称集合*/
            List<String> deliAddrNameList = new ArrayList<>();
            /** 到货地址名称集合*/
            List<String> arriAddrNameList = new ArrayList<>();

            List<CustAddressVO> deliAddrList = addressService.selectAddressListByCustomerId(address, clientPopupVO.getCustomerId(), 1, null);
            if(deliAddrList == null || deliAddrList.size() == 0){
                errorMsg.append("未找到发货方;");
            }else{
                MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();
                multipleShippingAddress.setContact(deliAddrList.get(0).getContact());
                multipleShippingAddress.setMobile(deliAddrList.get(0).getMobile());
                multipleShippingAddress.setDeliveryId(deliAddrList.get(0).getAddressId());
                multipleShippingAddress.setProvinceId(deliAddrList.get(0).getProvinceId());
                multipleShippingAddress.setCityId(deliAddrList.get(0).getCityId());
                multipleShippingAddress.setAreaId(deliAddrList.get(0).getAreaId());
                multipleShippingAddress.setProvinceName(deliAddrList.get(0).getProvinceName());
                multipleShippingAddress.setCityName(deliAddrList.get(0).getCityName());
                multipleShippingAddress.setAreaName(deliAddrList.get(0).getAreaName());
                multipleShippingAddress.setAddrCode(deliAddrList.get(0).getAddrCode());
                multipleShippingAddress.setAddrName(deliAddrList.get(0).getAddrName());
                multipleShippingAddress.setDetailAddr(deliAddrList.get(0).getDetailAddr());
                multipleShippingAddress.setAddressType(0);
                multipleShippingAddress.setIsGetContractPrice(1);

                deliAddrNameList.add(deliAddrList.get(0).getAddrName());

                deliAddr = multipleShippingAddress.getProvinceName() + multipleShippingAddress.getCityName() + multipleShippingAddress.getAreaName() + multipleShippingAddress.getDetailAddr();

                List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                multipleShippingGoods.setCustOrderno(para.get(13));
                multipleShippingGoods.setGoodsName(goodsNameInsert);
                //散装
                multipleShippingGoods.setPackId("9");
                if(StringUtils.isNotBlank(para.get(17))){
                    multipleShippingGoods.setNum(Double.valueOf(para.get(17)));
                }
                if(StringUtils.isNotBlank(para.get(18))){
                    multipleShippingGoods.setWeight(Double.valueOf(para.get(18)));
                }
                multipleShippingGoodsList.add(multipleShippingGoods);
                multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                shippingAddressList.add(multipleShippingAddress);

                //单价查询对象
                List<String> deliAreaIdList = new ArrayList<>();
                deliAreaIdList.add(deliAddrList.get(0).getAreaId());
                searchContractPriceVO.setDeliAreaIdList(deliAreaIdList);
            }

            //查询到货地址
            String detailAddr = para.get(19);
            String detailProCityArea = para.get(20);
            String[] split = detailProCityArea.split("/");
            if(split.length < 3){
                errorMsg.append("到货地址解析异常;");
            }else {
                String provinceName = split[0];
                String provinceCode = provinceMapper.getProvinceCode(provinceName);
                String cityName = split[1];
                String cityCode = provinceMapper.getCityCode(provinceCode,cityName);
                String areaName;
                String areaCode;
                if(provinceName.equals("广东省") && cityName.equals("中山市")){
                    areaName = "中山市";
                    areaCode = "442000";
                }else{
                    areaName = split[2];
                    if(areaName.contains("下辖")){
                        areaName = areaName.replace("下辖","");
                    }
                    areaCode = provinceMapper.getAreaCode(cityCode,areaName);
                }

                /*if(provinceName.equals("广东省")){*/
                //调度组黄成保
                  /*  invoice.setTransLineId("604");
                    invoice.setTransLineName("田清");*/
                invoice.setTransLineId(config.getTransLineId());
                invoice.setTransLineName(config.getTransLineName());

                /*}else{
                    //调度组田清
                    invoice.setTransLineId("604");
                    invoice.setTransLineName("田清");
                }*/
                detailAddr = detailAddr.replace(provinceName,"").replace(cityName,"").replace(areaName,"");
                if(StringUtils.isBlank(provinceCode) || StringUtils.isBlank(cityCode) || StringUtils.isBlank(areaCode)){
                    errorMsg.append("到货地址解析异常;");
                }else{
                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();
                    String contact = para.get(24);
                    if(contact.length() <= 1){
                        contact += "先生";
                    }
                    multipleShippingAddress.setContact(contact);
                    multipleShippingAddress.setMobile(para.get(23).replace(" ",""));

                    multipleShippingAddress.setProvinceId(provinceCode);
                    multipleShippingAddress.setCityId(cityCode);
                    multipleShippingAddress.setAreaId(areaCode);
                    multipleShippingAddress.setProvinceName(provinceName);
                    multipleShippingAddress.setCityName(cityName);
                    multipleShippingAddress.setAreaName(areaName);
                    multipleShippingAddress.setDetailAddr(detailAddr);
                    multipleShippingAddress.setIsGetContractPrice(1);
                    multipleShippingAddress.setAddrName(para.get(8));
                    multipleShippingAddress.setAddressType(1);

                    arriAddrNameList.add(para.get(8));

                    arriAddr = provinceName + cityName + areaName + detailAddr;

                    List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                    MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                    multipleShippingGoods.setCustOrderno(para.get(13));
                    multipleShippingGoods.setGoodsName(goodsNameInsert);
                    //散装
                    multipleShippingGoods.setPackId("9");
                    if(StringUtils.isNotBlank(para.get(17))){
                        multipleShippingGoods.setNum(Double.valueOf(para.get(17)));
                        invoice.setNumCount(Double.valueOf(para.get(17)));
                    }
                    if(StringUtils.isNotBlank(para.get(18))){
                        multipleShippingGoods.setWeight(Double.valueOf(para.get(18)));
                        invoice.setWeightCount(Double.valueOf(para.get(18)));
                    }
                    multipleShippingGoodsList.add(multipleShippingGoods);
                    multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                    shippingAddressList.add(multipleShippingAddress);

                    List<String> arriAreaIdList = new ArrayList<>();
                    arriAreaIdList.add(areaCode);
                    searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
                }

            }
            invoice.setShippingAddressList(shippingAddressList);
            invoice.setVolumeCount(0d);



            //要求提货日期,到货日期
            String fpTimeStr = para.get(10);
            if(StringUtils.isEmpty(fpTimeStr)){
                errorMsg.append("获取分配日期失败;");
            }else{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date fpTime = sdf.parse(fpTimeStr);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(fpTime);
                if(calendar.get(Calendar.HOUR_OF_DAY) >= 13){
                    //超过13点要求提货日期加1天
                    calendar.add(Calendar.DATE,1);
                }
                invoice.setReqDeliDate(calendar.getTime());
                calendar.add(Calendar.DATE,1);
                invoice.setReqArriDate(calendar.getTime());
            }

            //车长车型为 13米平板货车
            invoice.setCarLen(config.getCarLen());
            invoice.setCarLenName(config.getCarLenName());
            invoice.setCarType(config.getCarType());
            invoice.setCarTypeName(config.getCarTypeName());

            //运输方式
            invoice.setTransCode(config.getTransCode());
            invoice.setTransName(config.getTransName());
            //紧急程度普通
            invoice.setUrgentLevel(config.getUrgentLevel());
            //卸货地数量1
            invoice.setUnloadPlaceNum(config.getUnloadPlaceNum());
            invoice.setIsMultiple(0);
            //否大件
            invoice.setIsOversize(config.getIsOversize());
            //是否开票
            invoice.setBillingType(config.getBillingType());

            //查询恒逸议价方式
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.DATE,-10);
            long millis = cal.getTimeInMillis();

            String listUrl = "https://tms.hengyi.com/msapi3/E6-MS-TMS-BUSI-WEB/productDistributionReport/getAllCarrierByPage";
            String listRes = HttpRequest.post(listUrl).header("Content-Type", "application/json;charset=UTF-8")
                    .header("Token", token).body("{\n" +
                            "  \"assignedBTime\": \""+millis+"\",\n" +
                            "  \"assignedETime\": \"\",\n" +
                            "  \"timeList\": [],\n" +
                            "  \"carrierCorpIds\": [],\n" +
                            "  \"goodsIdList\": [],\n" +
                            "  \"extStrSpaOrder\": \""+custOrderNo+"\",\n" +
                            "  \"shippingAreaAddress\": \"\",\n" +
                            "  \"factoryIds\": [],\n" +
                            "  \"customerIds\": [],\n" +
                            "  \"vehicleNos\": [],\n" +
                            "  \"pageParamNewVO\": {\n" +
                            "    \"curPage\": 1,\n" +
                            "    \"pageSize\": 20\n" +
                            "  }\n" +
                            "}").execute().body();

            JSONObject listResJson = JSON.parseObject(listRes);
            JSONArray jsonArray = listResJson.getJSONObject("result").getJSONArray("data");
            if(jsonArray != null && jsonArray.size() > 0){
                String chargeMode = jsonArray.getJSONObject(0).getString("chargeMode");
                if("一口价".equals(chargeMode)){
                    invoice.setBillingMethod("3");
                }else{
                    //结算方式，回单结算
                    invoice.setBillingMethod(config.getBillingMethod());
                }
            }else{
                //结算方式，回单结算
                invoice.setBillingMethod(config.getBillingMethod());
            }

            //合同价
            invoice.setIfBargain(0);

            invoice.setResidentsId("");

            if ("4".equals(invoice.getBillingMethod())) {
                invoice.setIsRoundTrip(1);
            }else {
                invoice.setIsRoundTrip(0);
            }

            //获取单价和合同价
            searchContractPriceVO.setCustomerId(invoice.getCustomerId());
            searchContractPriceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));
            searchContractPriceVO.setGoodsCharacter("0");
            searchContractPriceVO.setNum(new BigDecimal(para.get(17)));
            searchContractPriceVO.setWeight(new BigDecimal(para.get(18)));
            searchContractPriceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));
            searchContractPriceVO.setCarLen(invoice.getCarLen());
            searchContractPriceVO.setCarType(invoice.getCarType());
            searchContractPriceVO.setGoodsName(goodsNameInsert);
            searchContractPriceVO.setIsOversize(config.getIsOversize());
            searchContractPriceVO.setDeliAddrNameList(deliAddrNameList);
            searchContractPriceVO.setArriAddrNameList(arriAddrNameList);
            searchContractPriceVO.setTransCode(invoice.getTransCode());
            searchContractPriceVO.setIsRoundTrip(invoice.getIsRoundTrip());

            Map<String, String> resMap = invoiceService.getPrice(searchContractPriceVO);
            if(resMap.get("type").equals("1")){
                SysDictData billingTypeDictData = sysDictDataMapper
                        .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
                if (billingTypeDictData == null) {
                    return AjaxResult.error("请选择正确的开票类型！");
                }

                //税率
                BigDecimal rate = billingTypeDictData.getNumVal1();

                //是否含税  0不含税  1含税*
                String isIncludeTax = resMap.get("isIncludeTax");

                //总评价
                BigDecimal totalPrice = new BigDecimal(resMap.get("totalPrice"));
                //单价
                BigDecimal price = new BigDecimal(resMap.get("price"));
                //成本价
                String totalCostPrice = resMap.get("totalCostPrice");
                String costBillingType = resMap.get("costBillingType");
                if(StringUtils.isNotBlank(totalCostPrice)){
                    BigDecimal costPrice = new BigDecimal(totalCostPrice);
                    invoice.setCostPrice(costPrice);
                    invoice.setCostBillingType(costBillingType);
                }

                //是否含税  0不含税  1含税*
                if ("0".equals(isIncludeTax)) {
                    if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                        invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                        invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                    }else {
                        invoice.setCostAmount(totalPrice);
                        invoice.setUnitPrice(price);
                    }
                }else {
                    //含税
                    invoice.setCostAmount(totalPrice);
                    invoice.setUnitPrice(price);
                }
            }else{
                errorMsg.append("获取单价/合同价失败;");
            }
            HYInvoiceRecord hyInvoiceRecord = new HYInvoiceRecord();
            hyInvoiceRecord.setId(IdUtil.simpleUUID());
            hyInvoiceRecord.setOriginNo(originNo);
            hyInvoiceRecord.setCustOrderno(custOrderNo);
            hyInvoiceRecord.setRegDate(new Date());
            hyInvoiceRecord.setType(0);

            //是否人为创建标记
            Boolean hasCreateFlag = false;

            //新建发货单返回信息
            Map<String,String> invoiceResMap = null;

            if(errorMsg.length() > 0){
                //判断是否已经人为建单
                List<Invoice> invoices = invoiceMapper.selectInvoiceByCustOrderNo(originNo);
                if(invoices != null && invoices.size() > 0){
                    hasCreateFlag = true;
                    hyInvoiceRecord.setIfSuccess(0);
                    hyInvoiceRecord.setInvoiceId(invoices.get(0).getInvoiceId());
                }else{
                    hyInvoiceRecord.setIfSuccess(1);
                    hyInvoiceRecord.setErrorMsg(errorMsg.toString());
                }
            }else{
                invoice.setCorScrId("uibot");
                invoice.setRegScrId("uibot");

                AjaxResult ajaxResult = invoiceService.insertInvoice(invoice, 0);
                if(ajaxResult.getCode() == 0){
                    hyInvoiceRecord.setIfSuccess(0);
                    invoiceResMap = (Map<String, String>) ajaxResult.getData();
                    String invoiceId = invoiceResMap.get("invoiceId");
                    hyInvoiceRecord.setInvoiceId(invoiceId);
                    //更新发货单创建人
                    Invoice invoiceUp = new Invoice();
                    invoiceUp.setInvoiceId(invoiceId);
                    invoiceUp.setRegUserId("310211");
                    invoiceUp.setRegUserName("RPA01");
                    invoiceMapper.updateInvoice(invoiceUp);
                }else{
                    hyInvoiceRecord.setIfSuccess(1);
                    hyInvoiceRecord.setErrorMsg(ajaxResult.getMsg());
                }
            }
            invoiceMapper.insertHyInvoiceRecord(hyInvoiceRecord);

            if(hasCreateFlag){
                continue;
            }

            //推送琦欣小蜜
            StringBuffer sb = new StringBuffer();
            sb.append("`浙江恒逸`接单提醒").append("\n");
            if(hyInvoiceRecord.getIfSuccess() == 0) {
                sb.append(">下单状态:<font color=\"info\">成功</font>").append("\n");
                sb.append(">恒逸单号:").append(originNo).append("\n");
                sb.append(">发货单号:").append(invoiceResMap.get("invoiceNo")).append("\n");
                sb.append(">SAP单号:").append(custOrderNo).append("\n");
                sb.append(">装货地:").append(deliAddr).append("\n");
                sb.append(">卸货地:").append(arriAddr).append("\n");
            }else{
                sb.append(">下单状态:<font color=\"warning\">失败</font>").append("\n");
                sb.append(">恒逸单号:").append(originNo).append("\n");
                sb.append(">失败原因:").append(hyInvoiceRecord.getErrorMsg()).append("\n");
                sb.append(">SAP单号:").append(custOrderNo).append("\n");
                sb.append(">提醒次数:").append(++failCnt).append("/3(超过三次不再提醒)").append("\n");
            }
            HashSet<Long> userIds = new HashSet<>();
            //包扬
            userIds.add(236177l);
            //石晶
            userIds.add(297453l);
            for(Long id : userIds){
               wecomHandler.pushMarkdown(id, sb.toString());
            }
            //运维销售一组推送
            WechatMessageUtils.sendMarkDownMessage("e37f5d31-a203-43e4-8102-b85e2f7d7e5b",sb.toString());
        }
        return AjaxResult.success();
    }


    /**
     * 中粮
     * @return
     */
    @RequestMapping("/handleZLInvoice")
    @ResponseBody
    public AjaxResult handleZLInvoice() throws ParseException {
        //获取系统配置信息
        CargoConfig config = cargoConfigMapper.selectByPrimaryKey("zllg");

        //获取Token
        String token = asyncJobService.getZLLGToken(false);

        //请求列表
        Map<String, Object> listParams = new HashMap<String, Object>();
        Long[] createTimeList = new Long[2];
        Date date = new Date();
        createTimeList[1] = date.getTime();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH,-1);
        createTimeList[0] = cal.getTimeInMillis();
        listParams.put("createTimeList",createTimeList);

        Map<String,Object> pageParamNewVO = new HashMap<>();
        pageParamNewVO.put("curPage",1);
        pageParamNewVO.put("pageSize",20);
        listParams.put("pageParamNewVO",pageParamNewVO);
        listParams.put("waybillType",1);
        String listRes = HttpRequest.post("https://lgtms.cofco.com/api3/E6-MS-TMS-BUSI-WEB/waybill/pageQueryForManage")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Cookie","SESSIONID=4B4AE3D3A3E1C254A28E4477CAA48AE4; VerificationCodeToken=9c654dd9-e5b8-4c67-b582-9919a5432be8; CustomerKey=; loginURL=https://lgtms.cofco.com/#/login; lang=zh-CN; PWD_EXPIRED_INFO=; RequestToken="+token+"; WebgisCertValue=2njHggGIDHtKg2+fQflEPwl9ZpP4mYhbwHxY0/qD5qvPgV0mU8t/QxfgMsiAaOYHznXFA51FPKnZo6ydk173bryrpE7DLy38; SECKEY_ABVK=mzuODm9MRee89OdNtrQWwxJFc/6zdtFVW+6o4efgDCQ%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj1-9TGqJLbfcywSbdUQrSHPU_AGZhwDYOX1Fc4hvbU93zmfNDR6ByM20tBuVhKcPvPij4MysSn8U9yebQsjSDCFqeMrvJXjz15TBmzLXaSEcC9ZB2rYr5PLFkFswlRw3qemMCdgYKK4PvezfYTyN706stSESWyKKlMDV8n2eSbUpvwbsYFTAVPbRkFCt88xTIA")
                .body(JSON.toJSONString(listParams))
                .execute().body();
        JSONArray listArr = JSONObject.parseObject(listRes).getJSONObject("result").getJSONArray("data");
        for(int i = 0 ; i < listArr.size() ; i++){
            JSONObject para = listArr.getJSONObject(i);

            StringBuffer errorMsg = new StringBuffer();

            String originId = para.getString("id");
            String originNo = para.getString("waybillNo");
            String custOrderNo = para.getString("orderNos");
            if(StringUtils.isEmpty(originNo)){
                continue;
            }
            //判断单据是否推送过
            Integer successCnt = invoiceMapper.countSuccessHyInvoiceByOriginNo(originNo);
            if(successCnt > 0){
                continue;
            }

            //判断失败次数 超过3次不执行
            Integer failCnt = invoiceMapper.countFailHyInvoiceByOriginNo(originNo);
            if(failCnt >= 3){
                continue;
            }

            InvoiceAddVO invoice = new InvoiceAddVO();
            //货品信息
            String goodsNameInsert = "面粉";
            //客户信息
            ClientPopupVO  clientPopupVO = clientService.selectClientById("9f5953245cf24df4b3ecf437381b3012"); ;


            invoice.setCustomerId(clientPopupVO.getCustomerId());
            invoice.setCustName(clientPopupVO.getCustName());
            invoice.setCustAbbr(clientPopupVO.getCustAbbr());
            invoice.setCustCode(clientPopupVO.getCustCode());
            invoice.setBalaCorpId(clientPopupVO.getBalaCorp());
            invoice.setStationDept(clientPopupVO.getStationDept());
            invoice.setStationDeptName(clientPopupVO.getStationDeptName());
            invoice.setBalaDept(clientPopupVO.getBalaDept());
            invoice.setSalesDept(clientPopupVO.getSalesDept());
            invoice.setPsndoc(clientPopupVO.getPsndoc());
            invoice.setBalaType("1");
            invoice.setAppDeliContact(clientPopupVO.getAppDeliContact());
            invoice.setAppDeliMobile(clientPopupVO.getAppDeliMobile());
            invoice.setReferenceRate(clientPopupVO.getReferenceRate());
            //结算客户
            Client custBala = clientService.getDefaultCustBalaByCustomerId(clientPopupVO.getCustomerId());
            if(custBala == null){
                errorMsg.append("未找到结算客户;");
            }else{
                invoice.setBalaName(custBala.getCustName());
                invoice.setBalaCode(custBala.getCustCode());
                invoice.setBalaCustomerId(custBala.getCustomerId());
            }

            SearchContractPriceVO searchContractPriceVO = new SearchContractPriceVO();
            List<MultipleShippingAddress> shippingAddressList = new ArrayList<>();
            //查询提货地址
            String deliCustName = para.getString("factoryNames");
            AddressDTO address = new AddressDTO();
            address.setAddrType("1");
            Map params = new HashMap<String, Object>();
            params.put("addrNameOrContact",deliCustName);
            address.setParams(params);

            //提货地到货地
            String deliAddr = "";
            String arriAddr = "";

            /** 提货地址名称集合*/
            List<String> deliAddrNameList = new ArrayList<>();
            /** 到货地址名称集合*/
            List<String> arriAddrNameList = new ArrayList<>();

            List<CustAddressVO> deliAddrList = addressService.selectAddressListByCustomerId(address, clientPopupVO.getCustomerId(), 1, null);
            if(deliAddrList == null || deliAddrList.size() == 0){
                errorMsg.append("未找到发货方;");
            }else{
                MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();
                multipleShippingAddress.setContact(deliAddrList.get(0).getContact());
                multipleShippingAddress.setMobile(deliAddrList.get(0).getMobile());
                multipleShippingAddress.setDeliveryId(deliAddrList.get(0).getAddressId());
                multipleShippingAddress.setProvinceId(deliAddrList.get(0).getProvinceId());
                multipleShippingAddress.setCityId(deliAddrList.get(0).getCityId());
                multipleShippingAddress.setAreaId(deliAddrList.get(0).getAreaId());
                multipleShippingAddress.setProvinceName(deliAddrList.get(0).getProvinceName());
                multipleShippingAddress.setCityName(deliAddrList.get(0).getCityName());
                multipleShippingAddress.setAreaName(deliAddrList.get(0).getAreaName());
                multipleShippingAddress.setAddrCode(deliAddrList.get(0).getAddrCode());
                multipleShippingAddress.setAddrName(deliAddrList.get(0).getAddrName());
                multipleShippingAddress.setDetailAddr(deliAddrList.get(0).getDetailAddr());
                multipleShippingAddress.setAddressType(0);
                multipleShippingAddress.setIsGetContractPrice(1);

                deliAddrNameList.add(deliAddrList.get(0).getAddrName());

                deliAddr = multipleShippingAddress.getProvinceName() + multipleShippingAddress.getCityName() + multipleShippingAddress.getAreaName() + multipleShippingAddress.getDetailAddr();

                List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                multipleShippingGoods.setCustOrderno(custOrderNo);
                multipleShippingGoods.setGoodsName(goodsNameInsert);
                //散装
                multipleShippingGoods.setPackId("9");

                multipleShippingGoods.setWeight(para.getDouble("dispatchTotalWeight"));

                multipleShippingGoodsList.add(multipleShippingGoods);
                multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                shippingAddressList.add(multipleShippingAddress);

                //单价查询对象
                List<String> deliAreaIdList = new ArrayList<>();
                deliAreaIdList.add(deliAddrList.get(0).getAreaId());
                searchContractPriceVO.setDeliAreaIdList(deliAreaIdList);
            }

            //到货地址解析
            String[] waybillLineNames = para.getString("waybillLineName").split("-");
            String destination = waybillLineNames[1];

            String addrName = para.getString("deliveryPartyDescriptions");

            AddrParseDTO addrParseDTO = addressService.addrParse(destination);

            if(addrParseDTO == null || addrParseDTO.getAddressList() == null || addrParseDTO.getAddressList().size() ==0){
                errorMsg.append("到货地址解析异常;");
            }else{
                MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();

                String consigneePhone = para.getJSONArray("orderInfoPOList").getJSONObject(0).getString("consigneePhone");
                String[] result = ZLUtils.splitReceiverAndPhone(consigneePhone);
                if (result!= null) {
                    multipleShippingAddress.setContact(result[0]);
                    multipleShippingAddress.setMobile(result[1]);
                } else {
                    multipleShippingAddress.setContact("识别失败");
                    multipleShippingAddress.setMobile("88888888");
                }

                multipleShippingAddress.setProvinceId(addrParseDTO.getAddressList().get(0).getProvinceId());
                multipleShippingAddress.setCityId(addrParseDTO.getAddressList().get(0).getCityId());
                multipleShippingAddress.setAreaId(addrParseDTO.getAddressList().get(0).getAreaId());
                multipleShippingAddress.setProvinceName(addrParseDTO.getAddressList().get(0).getProvinceName());
                multipleShippingAddress.setCityName(addrParseDTO.getAddressList().get(0).getCityName());
                multipleShippingAddress.setAreaName(addrParseDTO.getAddressList().get(0).getAreaName());
                multipleShippingAddress.setDetailAddr(addrParseDTO.getAddressList().get(0).getDetailAddr());
                multipleShippingAddress.setIsGetContractPrice(1);
                multipleShippingAddress.setAddrName(addrName);
                multipleShippingAddress.setAddressType(1);

                arriAddrNameList.add(addrName);

                arriAddr = multipleShippingAddress.getProvinceName() + multipleShippingAddress.getCityName() + multipleShippingAddress.getAreaName() + multipleShippingAddress.getDetailAddr();

                List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                multipleShippingGoods.setCustOrderno(custOrderNo);
                multipleShippingGoods.setGoodsName(goodsNameInsert);
                //散装
                multipleShippingGoods.setPackId("9");

                multipleShippingGoods.setWeight(para.getDouble("dispatchTotalWeight"));

                multipleShippingGoodsList.add(multipleShippingGoods);
                multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                shippingAddressList.add(multipleShippingAddress);

                List<String> arriAreaIdList = new ArrayList<>();
                arriAreaIdList.add(addrParseDTO.getAddressList().get(0).getAreaId());
                searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
            }

            invoice.setTransLineId(config.getTransLineId());
            invoice.setTransLineName(config.getTransLineName());

            invoice.setShippingAddressList(shippingAddressList);
            invoice.setVolumeCount(0d);

            //获取备注
            JSONArray orderInfoPOList = para.getJSONArray("orderInfoPOList");
            String memo = "";
            for(int k = 0 ; k < orderInfoPOList.size(); k++){
                JSONObject orderInfo = orderInfoPOList.getJSONObject(k);
                memo += orderInfo.getString("headerTextRemarks");
            }
            invoice.setMemo(memo);

            //要求提货日期,到货日期
            String fpTimeStr = para.getString("sendCarTime");
            if(StringUtils.isEmpty(fpTimeStr)){
                errorMsg.append("获取分配日期失败;");
            }else{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date fpTime = sdf.parse(fpTimeStr);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(fpTime);
                invoice.setReqDeliDate(calendar.getTime());
                calendar.add(Calendar.DATE,1);
                invoice.setReqArriDate(calendar.getTime());
            }

            //根据重量判断车长
            /**
             * X<6吨:4.2
             * 6<=X<=12吨；6.8
             * 12<X<=22吨:9.6
             * X>22吨：13米
             */
            BigDecimal dispatchTotalWeight =  para.getBigDecimal("dispatchTotalWeight");
            if(dispatchTotalWeight.compareTo(new BigDecimal(6)) < 0){
                invoice.setCarLen("8");
                invoice.setCarLenName("4.2");
            }else if(dispatchTotalWeight.compareTo(new BigDecimal(12)) <= 0){
                invoice.setCarLen("12");
                invoice.setCarLenName("6.8");
            }else if(dispatchTotalWeight.compareTo(new BigDecimal(22)) <= 0){
                invoice.setCarLen("16");
                invoice.setCarLenName("9.6");
            }else{
                invoice.setCarLen("1");
                invoice.setCarLenName("13");
            }

            /**
             * 根据要求选择车型
             * 普通车——平板货车
             * 厢车——厢式货车
             * 飞翼车——飞翼车
             */
            String bookVehicleModelDesc = para.getString("bookVehicleModelDesc");
            if("普通车".equals(bookVehicleModelDesc)){
                invoice.setCarType("H05");
                invoice.setCarTypeName("平板货车");
            }else if("厢车".equals(bookVehicleModelDesc)){
                invoice.setCarType("H02");
                invoice.setCarTypeName("厢式货车");
            }else if("飞翼车".equals(bookVehicleModelDesc)){
                invoice.setCarType("f01");
                invoice.setCarTypeName("飞翼车");
            }else{
                invoice.setCarType(config.getCarType());
                invoice.setCarTypeName(config.getCarTypeName());
            }

            //运输方式
            invoice.setTransCode(config.getTransCode());
            invoice.setTransName(config.getTransName());
            //紧急程度普通
            invoice.setUrgentLevel(config.getUrgentLevel());
            //卸货地数量1
            invoice.setUnloadPlaceNum(config.getUnloadPlaceNum());
            invoice.setIsMultiple(0);
            //否大件
            invoice.setIsOversize(config.getIsOversize());
            //是否开票
            invoice.setBillingType(config.getBillingType());
            //结算方式，回单结算
            invoice.setBalaType(config.getBalaType());
            //计价方式
            invoice.setBillingMethod(config.getBillingMethod());

            //合同价
            invoice.setIfBargain(0);

            invoice.setResidentsId("");

            if ("4".equals(invoice.getBillingMethod())) {
                invoice.setIsRoundTrip(1);
            }else {
                invoice.setIsRoundTrip(0);
            }

            //获取单价和合同价
            searchContractPriceVO.setCustomerId(invoice.getCustomerId());
            searchContractPriceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));
            searchContractPriceVO.setGoodsCharacter("0");
            searchContractPriceVO.setWeight(dispatchTotalWeight);
            searchContractPriceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));
            searchContractPriceVO.setCarLen(invoice.getCarLen());
            searchContractPriceVO.setCarType(invoice.getCarType());
            searchContractPriceVO.setGoodsName(goodsNameInsert);
            searchContractPriceVO.setIsOversize(config.getIsOversize());
            searchContractPriceVO.setDeliAddrNameList(deliAddrNameList);
            searchContractPriceVO.setArriAddrNameList(arriAddrNameList);
            searchContractPriceVO.setTransCode(invoice.getTransCode());
            searchContractPriceVO.setIsRoundTrip(invoice.getIsRoundTrip());

            Map<String, String> resMap = invoiceService.getPrice(searchContractPriceVO);
            if(resMap.get("type").equals("1")){
                SysDictData billingTypeDictData = sysDictDataMapper
                        .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
                if (billingTypeDictData == null) {
                    return AjaxResult.error("请选择正确的开票类型！");
                }

                //税率
                BigDecimal rate = billingTypeDictData.getNumVal1();

                //是否含税  0不含税  1含税*
                String isIncludeTax = resMap.get("isIncludeTax");

                //总评价
                BigDecimal totalPrice = new BigDecimal(resMap.get("totalPrice"));
                //单价
                BigDecimal price = new BigDecimal(resMap.get("price"));
                //成本价
                String totalCostPrice = resMap.get("totalCostPrice");
                String costBillingType = resMap.get("costBillingType");
                if(StringUtils.isNotBlank(totalCostPrice)){
                    BigDecimal costPrice = new BigDecimal(totalCostPrice);
                    invoice.setCostPrice(costPrice);
                    invoice.setCostBillingType(costBillingType);
                }

                //是否含税  0不含税  1含税*
                if ("0".equals(isIncludeTax)) {
                    if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                        invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                        invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                    }else {
                        invoice.setCostAmount(totalPrice);
                        invoice.setUnitPrice(price);
                    }
                }else {
                    //含税
                    invoice.setCostAmount(totalPrice);
                    invoice.setUnitPrice(price);
                }
            }else{
                errorMsg.append("获取单价/合同价失败;");
            }
            HYInvoiceRecord hyInvoiceRecord = new HYInvoiceRecord();
            hyInvoiceRecord.setId(IdUtil.simpleUUID());
            hyInvoiceRecord.setOriginId(originId);
            hyInvoiceRecord.setOriginNo(originNo);
            hyInvoiceRecord.setCustOrderno(custOrderNo);
            hyInvoiceRecord.setRegDate(new Date());
            hyInvoiceRecord.setType(1);

            //是否人为创建标记
            Boolean hasCreateFlag = false;

            //新建发货单返回信息
            Map<String,String> invoiceResMap = null;

            //判断是否已经人为建单
            List<Invoice> invoices = invoiceMapper.selectInvoiceByCustOrderNo(custOrderNo);
            if(invoices != null && invoices.size() > 0){
                hasCreateFlag = true;
                hyInvoiceRecord.setIfSuccess(0);
                hyInvoiceRecord.setInvoiceId(invoices.get(0).getInvoiceId());
            }else if(errorMsg.length() > 0){
                hyInvoiceRecord.setIfSuccess(1);
                hyInvoiceRecord.setErrorMsg(errorMsg.toString());
            }else{
                invoice.setCorScrId("uibot");
                invoice.setRegScrId("uibot");

                AjaxResult ajaxResult = invoiceService.insertInvoice(invoice, 0);
                if(ajaxResult.getCode() == 0){
                    hyInvoiceRecord.setIfSuccess(0);
                    invoiceResMap = (Map<String, String>) ajaxResult.getData();
                    String invoiceId = invoiceResMap.get("invoiceId");
                    hyInvoiceRecord.setInvoiceId(invoiceId);
                    //更新发货单创建人
                    Invoice invoiceUp = new Invoice();
                    invoiceUp.setInvoiceId(invoiceId);
                    invoiceUp.setRegUserId("310211");
                    invoiceUp.setRegUserName("RPA01");
                    invoiceMapper.updateInvoice(invoiceUp);
                }else{
                    hyInvoiceRecord.setIfSuccess(1);
                    hyInvoiceRecord.setErrorMsg(ajaxResult.getMsg());
                }
            }
            invoiceMapper.insertHyInvoiceRecord(hyInvoiceRecord);

            if(hasCreateFlag){
                continue;
            }

            //推送琦欣小蜜
            StringBuffer sb = new StringBuffer();
            sb.append("`中粮面业`接单提醒").append("\n");
            if(hyInvoiceRecord.getIfSuccess() == 0) {
                sb.append(">下单状态:<font color=\"info\">成功</font>").append("\n");
                sb.append(">中粮单号:").append(originNo).append("\n");
                sb.append(">发货单号:").append(invoiceResMap.get("invoiceNo")).append("\n");
                sb.append(">SAP单号:").append(custOrderNo).append("\n");
                sb.append(">装货地:").append(deliAddr).append("\n");
                sb.append(">卸货地:").append(arriAddr).append("\n");
            }else{
                sb.append(">下单状态:<font color=\"warning\">失败</font>").append("\n");
                sb.append(">中粮单号:").append(originNo).append("\n");
                sb.append(">失败原因:").append(hyInvoiceRecord.getErrorMsg()).append("\n");
                sb.append(">SAP单号:").append(custOrderNo).append("\n");
                sb.append(">提醒次数:").append(++failCnt).append("/3(超过三次不再提醒)").append("\n");
            }
            HashSet<Long> userIds = new HashSet<>();
            //包扬
            userIds.add(236177l);
            //石晶
            userIds.add(297453l);
            for(Long id : userIds){
                wecomHandler.pushMarkdown(id, sb.toString());
            }
            //运维销售一组推送
            WechatMessageUtils.sendMarkDownMessage("fe3ac231-03a8-4f55-bc0b-b1559d180d5f",sb.toString());
        }
        return AjaxResult.success();
    }

    @RequestMapping("/dispatchZJHYInvoice")
    @ResponseBody
    public AjaxResult dispatchZJHYInvoice(@RequestBody List<List<String>> paras){
        List<Map<String,String>> recordList = new ArrayList<>();
        for(List<String> para : paras) {
            //恒逸单号
            String originNo = para.get(2);
            //查询已经调度的单据
            HYInvoiceRecord hyInvoiceRecord = invoiceMapper.selectSuccessHyInvoiceByOriginNo(originNo);
            if (hyInvoiceRecord != null && StringUtils.isNotBlank(hyInvoiceRecord.getInvoiceId())) {
                String invoiceId = hyInvoiceRecord.getInvoiceId();
                //查询运单信息
                List<EntrustLot> entrustLotList = entrustLotMapper.selectEntrustLotByInvoiceId(invoiceId);
                for(EntrustLot entrustLot : entrustLotList){
                    if(StringUtils.isNotBlank(entrustLot.getCarNo()) && StringUtils.isNotBlank(entrustLot.getDriverName())
                            && StringUtils.isNotBlank(entrustLot.getDriverMobile()) && StringUtils.isNotBlank(entrustLot.getDriverCardId())){
                        Map<String,String> resMap = new HashMap<>();
                        resMap.put("originNo",originNo);
                        resMap.put("carNo",entrustLot.getCarNo());
                        resMap.put("driverName",entrustLot.getDriverName());
                        resMap.put("driverMobile",entrustLot.getDriverMobile());
                        resMap.put("driverCardId",entrustLot.getDriverCardId());
                        recordList.add(resMap);
                    }
                }

            }
        }

       /* Map<String,String> resMap = new HashMap<>();
        resMap.put("originNo","T123");
        resMap.put("carNo","冀A086HU");
        resMap.put("driverName","庄占敏");
        resMap.put("driverMobile","***********");
        resMap.put("driverCardId","320602199608295312");
        recordList.add(resMap);*/

        return AjaxResult.success(recordList);
    }


    /**
     * 项目制
     *
     * @param map
     * @return
     */
    @RequiresPermissions("tms:invoice:project:view")
    @GetMapping("/project")
    public String projectInvoice(ModelMap map) {
        //获取关账时间
        List<CloseAccount> closeAccountList = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", closeAccountList);
        //发货单状态list
        List<Map<String, Object>> invoiceStatusList = InvoiceStatusEnum.getAllToMap();
        map.put("invoiceStatusList", invoiceStatusList);
        //发货单map
        Map<String, Object> value = EnumUtil.getNameFieldMap(InvoiceStatusEnum.class, "value");
        map.put("invoiceStatusMap", value);
        // 运营部
        map.put("salesDept", deptService.selectDeptByParentIds(DeptIdConstant.getSalesDeptId()));

        List<Map<String,String>> provinceList = provinceService.selectProvinces();
        map.put("provinceList", provinceList);

        map.put("billingMethod",BillingMethod.getAllToMap());

        //是否是车队数据
        map.put("isFleet", false);
        return "tms/invoice/project/project_inv";
    }

    /**
     * 查询发货单列表
     * @param invoice 发货单对象
     * @return 发货单list
     */
    @RequiresPermissions("tms:invoice:project:list")
    @PostMapping("/project/list")
    @ResponseBody
    public TableDataInfo projectList(InvoicePageVO invoice) {
        invoice.setDelFlag(0);
        //取非车队的数据
        invoice.setIsFleetData("0");
        //
        invoice.setInvoiceType(1);
        startPage();
        List<InvoicePageVO> list = invoiceService.selectInvoicePageList(invoice);
        return getDataTable(list);
    }

    /**
     * 新增发货单
     * @return 添加页面
     */
    @RequiresPermissions("tms:invoice:project:add")
    @GetMapping("/project/add")
    public String projectAdd(ModelMap map) {
        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        map.addAttribute("billingMethods",billingMethods );

        //调度组

        map.addAttribute("dispatcherDeptList", deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId()));
        //驻场组
//        map.addAttribute("stationDeptList", deptService.selectDeptByParentId(DeptIdConstant.STATION_DEPT_ID));

//        map.put("years", actualCarrierService.getSixMonth());


        //是否是车队数据
        map.put("isFleet", false);
        return "tms/invoice/project/project_inv_add";
    }


    /**
     * 新增保存发货单
     * @param invoice 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:project:add")
    @Log(title = "项目发货单下单", businessType = BusinessType.INSERT)
    @PostMapping("/project/add")
    @ResponseBody
    public AjaxResult projectAdd(@Validated ProjectInvoiceAddVO invoice) {
        return invoiceService.projectAdd(invoice);
    }


    /**
     * 删除发货单(逻辑删除)
     * @param ids id
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:project:remove")
    @Log(title = "项目发货单删除", businessType = BusinessType.DELETE)
    @PostMapping("/project/remove")
    @ResponseBody
    public AjaxResult projectRemove(String ids) {
        return invoiceService.deleteProjectInvoiceByIds(ids);
    }


    /**
     * 修改/拷贝 项目发货单
     *
     * @param invoiceId 发货单id
     * @param type      copy or edit
     * @return
     */
    @RequiresPermissions("tms:invoice:project:edit")
    @GetMapping("/project/edit/{invoiceId}/{type}")
    public String projectEdit(@PathVariable("invoiceId") String invoiceId, @PathVariable("type") String type, ModelMap map) {
        Invoice invoice = invoiceService.selectInvoiceById(invoiceId);

        ProjectInvoiceEditVO projectInvoiceEditVO = new ProjectInvoiceEditVO();
        BeanUtils.copyProperties(invoice, projectInvoiceEditVO);

        List<ProjectInvoiceEditVO.InvoiceDetailEditVO> detailEditList = new ArrayList<>();
        List<InvoiceDetail> invoiceDetailList = invoiceDetailService.selectAllByInvoiceId(invoiceId);
        for (InvoiceDetail invoiceDetail : invoiceDetailList) {

            ProjectInvoiceEditVO.InvoiceDetailEditVO detailEditVO = new ProjectInvoiceEditVO.InvoiceDetailEditVO();
            BeanUtils.copyProperties(invoiceDetail, detailEditVO);

            List<InvoiceDetailAddr> invoiceDetailAddrList = invoiceDetailAddrService
                    .selectAllByInvoiceDetailId(invoiceDetail.getInvoiceDetailId());

            //查询地址
            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> addressList = new ArrayList<>();
            for (InvoiceDetailAddr invoiceDetailAddr : invoiceDetailAddrList) {
                ProjectInvoiceEditVO.InvoiceDetailAddressVO addressVO = new ProjectInvoiceEditVO.InvoiceDetailAddressVO();
                BeanUtils.copyProperties(invoiceDetailAddr, addressVO);
                addressList.add(addressVO);
            }

            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> deilAddressList = addressList.stream()
                    .filter(x -> x.getAddressType() == 0).collect(Collectors.toList());
//            detailEditVO.setDeilAddressVOList(deilAddressList);
            List<ProjectInvoiceEditVO.InvoiceDetailAddressVO> arriAddressList = addressList.stream()
                    .filter(x -> x.getAddressType() == 1).collect(Collectors.toList());
//            detailEditVO.setArriAddressVOList(arriAddressList);

            detailEditVO.setAddressVOList(addressList);

            //提到货地址数量
            detailEditVO.setDeliAddrCnt(deilAddressList.size());
            detailEditVO.setArriAddrCnt(arriAddressList.size());

            InvoiceDetailAddr deliDetailAddr = invoiceDetailAddrList.stream().filter(x -> x.getAddressType() == 0).findFirst().orElse(null);
            InvoiceDetailAddr arriDetailAddr = invoiceDetailAddrList.stream().filter(x -> x.getAddressType() == 1).findFirst().orElse(null);

            if (deliDetailAddr != null) {
                //地址名称
                detailEditVO.setDeliAddrName(deliDetailAddr.getAddrName());
                //详细地址
                detailEditVO.setDeliAddr(deliDetailAddr.getProvinceName() + deliDetailAddr.getCityName()
                        + deliDetailAddr.getAreaName() + deliDetailAddr.getDetailAddr());
            }
            if (arriDetailAddr != null) {
                //地址名称
                detailEditVO.setArriAddrName(arriDetailAddr.getAddrName());
                //详细地址
                detailEditVO.setArriAddr(arriDetailAddr.getProvinceName() + arriDetailAddr.getCityName()
                        + arriDetailAddr.getAreaName() + arriDetailAddr.getDetailAddr());
            }

            detailEditList.add(detailEditVO);
        }

        projectInvoiceEditVO.setInvoiceDetailList(detailEditList);


        map.put("projectInvoiceEditVO", projectInvoiceEditVO);

        map.put("detailSize", invoiceDetailList.size());

        if (StringUtils.isNotEmpty(projectInvoiceEditVO.getProjectFileId())) {
            List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(projectInvoiceEditVO.getProjectFileId());
            map.put("sysUploadFiles", sysUploadFiles);
        }

        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        map.addAttribute("billingMethods", billingMethods);

        //调度组

        map.addAttribute("dispatcherDeptList", deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId()));

        map.put("type", type);
        return "tms/invoice/project/project_inv_edit";
    }

    /**
     * 修改项目制发货单
     * @param editVO 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @RequiresPermissions("tms:invoice:project:edit")
    @Log(title = "修改项目发货单", businessType = BusinessType.UPDATE)
    @PostMapping("/project/edit")
    @ResponseBody
    public AjaxResult projectEdit(@Validated ProjectInvoiceEditVO editVO) {
        return invoiceService.projectEdit(editVO);
    }


    /**
     * 发货单确认
     * @param invoiceIds 发货单id
     * @return
     */
    @RepeatSubmit
    @Log(title = "项目发货单确认", businessType = BusinessType.OTHER)
    @RequiresPermissions("tms:invoice:project:affirm")
    @PostMapping("/project/affirm")
    @ResponseBody
    public AjaxResult projectAffirm(String invoiceIds) {
        return invoiceService.confirmProjectInvoice(invoiceIds);
    }

    /**
     * 跳转反确认页面
     * @param map
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"tms:invoice:project:back_confirm"},logical = Logical.OR)
    @GetMapping("/project/back_confirm/{id}/{deConfirmation}")
    public String projectBackConfirmPage(ModelMap map, @PathVariable String id,
                                         @PathVariable String deConfirmation) {
        map.put("invoiceId", id);
        map.put("deConfirmation", deConfirmation);
        return "tms/invoice/project/back_confirm";
    }

    /**
     * 反确认操作  合并到back_confirm_pick一个页面操作
     * @param unconfirm 发货单对象
     * @return
     */
    @RepeatSubmit
    @Log(title = "项目发货单反确认", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:invoice:project:back_confirm"},logical = Logical.OR)
    @PostMapping("/project/back_confirm")
    @ResponseBody
    public AjaxResult projectPackConfirm(Unconfirm unconfirm) {
        return invoiceService.backConfirmInvoice(unconfirm);
    }


    /**
     * 发货单详情
     * @param mmap
     * @return
     */
    @RequiresPermissions("tms:invoice:project:detail")
    @GetMapping("/project/detail/{id}")
    public String projectDetail(@PathVariable("id") String invoiceId, ModelMap mmap) {
        //发货单货品计价方式
        List<Map<String, Object>> billingMethods = BillingMethod.getAllToMap();
        mmap.addAttribute("billingMethods",billingMethods );

        //调度组
        mmap.addAttribute("dispatcherDeptList", deptService.selectDeptByParentIds(DeptIdConstant.getDispatcherDeptId()));

        ProjectInvoiceDetailVO detailVO = invoiceService.selectProjectInvoiceById(invoiceId);
        mmap.put("detail", detailVO);

        if (StringUtils.isNotEmpty(detailVO.getProjectFileId())) {
            List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(detailVO.getProjectFileId());
            mmap.put("projectFiles", sysUploadFiles);
        }

        return "tms/invoice/project/detail";
    }

    @RequestMapping("/jumpGoodsChange")
    public String jumpGoodsChange(String invoiceId,ModelMap mmap){
        //查询货量更新记录
        List<CargoEditHistory> cargoEditHistories = entrustMapper.selectCargoEditHistoryByInvoiceId(invoiceId);
        mmap.put("cargoEditHistories",cargoEditHistories);
        return "tms/invoice/goods_detail";
    }

    @RequiresPermissions("tms:invoice:ledger")
    @PostMapping("ledgerExport")
    @ResponseBody
    public AjaxResult ledgerExport(Invoice invoice) {
        //取非车队的数据
        invoice.setIsFleetData("0");
        List<Map<String, Object>> list = invoiceService.ledgerList(invoice);
        if (list.size() == 0) {
            return AjaxResult.error("没有可导出数据");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String outName = UUID.randomUUID() + "_" + "台账" + sdf.format(new Date()) + ".xlsx";
        try (OutputStream os = new FileOutputStream(Global.getDownloadPath() + outName)) {
            Map<String, Object> param = new HashMap<>();
            param.put("list", list);
            Function<Segment, String> nwv = segment -> {
                return null;
            };
            param.put("nwv", nwv);
            JxlsUtils.createExcel(Global.getDownloadPath() + "/template/台账.xlsx", os, param);
            return AjaxResult.success((Object) outName);
        } catch (Exception e) {
            logger.error("生成{}时失败：", outName, e);
            logger.error("删除文件{}：{}", Global.getDownloadPath() + outName, new File(Global.getDownloadPath() + outName).delete());
            return AjaxResult.error(e.getMessage() == null ? e.toString() : e.getMessage());
        }
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        // 正则表达式：1开头，后面跟10位数字（总共11位）
        String regex = "^1\\d{10}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }


    /**
     * 获取阳光新能源平台运单信息
     */
    @RequestMapping("/uploadCarPic")
    @ResponseBody
    public AjaxResult uploadCarPic() {
        return asyncJobService.sunshineAcceptCarPicUpload();
    }

    /**
     * 获取阳光新能源平台运单信息
     */
    @RequestMapping("/getSunShineLotInfo")
    @ResponseBody
    public AjaxResult getSunShineLotInfo() throws ParseException, IOException {

        //登录验证
        String token = asyncJobService.getSunshineToken(false);
        if(StringUtils.isNotBlank(token)){

            //抓取提货单和保价金额row.getCell(2) != null  && row.getCel
            asyncJobService.grabPdfAndQuote(token);

            SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH,-3);
            String dayStr = sdfDay.format(cal.getTime());

            //请求列表接口
            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?udfDisplayForqueryGuid=8200007456ygdySCM.TMS7.Model.Models.shipment_SCM.TMS7.Model&defsort=CREATED_DATE%20DESC";
            Map<String,Object> listParams = new HashMap<>();
            listParams.put("_search","true");
            listParams.put("rows","100");
            listParams.put("page","1");
            listParams.put("sord","asc");
            listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"STATUS\",\"op\":\"eq\",\"data\":\"30\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            logger.debug("阳光列表请求结果：{}",listRes);
            JSONObject listJson = JSONObject.parseObject(listRes);
            JSONArray rows = listJson.getJSONArray("rows");
            //统计列名称
            /*JSONArray columns = listJson.getJSONArray("columns");
            Map<String,Integer> columnMap = new HashMap<>();
            for(int j = 0 ; j < columns.size(); j++) {
                String columnName = columns.getJSONObject(j).getString("columnName");
                columnMap.put(columnName,j);
            }*/

            //获取信息
            CargoConfig configYg = cargoConfigMapper.selectByPrimaryKey("ygdy");
            CargoConfig configEts = cargoConfigMapper.selectByPrimaryKey("ygdy-ets");
            //阳光电源（进仓年度标）
            CargoConfig configJc= cargoConfigMapper.selectByPrimaryKey("ygdy-jc");

            for(int i = 0 ; i < rows.size(); i++) {
                JSONObject row = rows.getJSONObject(i);
                String id = row.getString("id");

                JSONArray cells = row.getJSONArray("cell");

                //字段顺序 20250709前
                /*0 客户订单号
                1 运单ID
                2 状态
                3 创建日期
                4 目的地名称
                5 最晚提货日期
                6 要求到货日期
                7 起始地城市
                8 起始地区（县）
                9 收货地址
                10 联系人姓名(D)
                11 联系人电话(D)
                12 客户名称
                13 公里数
                14 实际运输方式ID
                15 设备类型ID
                16 总毛体积
                17 总运费
                18 附加费
                19 标准费用
                20 设备ID
                21 司机名称
                22 偏移距离
                23 基准地址公里数
                24 多提多卸费
                25 区县政府地址
                26 目的地省份
                27 目的地区（县）
                28 目的地城市
                29 总件数
                30 实际承运人
                31 起始地名称
                32 创建人
                33 实际里程
                34 提货件数
                35 提货净重
                36 提货毛重
                37 到货件数
                38 到货净重
                39 到货毛重
                40 到货体积
                41 竞价单ID?
                42 竞价单ID?
                43 原始件数
                44 原始重量
                45 原始净重
                46 原始体积
                47 操作要求
                48 签单返回
                49 是否加急
                50
                51 报价金额
                52 结算设备类型ID
                53 提货预警
                54 是否存在货运轨迹*/

                /*
                *
                0 委托时间
                1 客户订单号
                2 运单ID
                3 状态
                4 创建日期
                5 最晚提货日期
                6 收货地址
                7 联系人姓名
                8 联系人电话
                9 公里数
                10 客户名称
                11 实际运输方式ID
                12 设备类型ID
                13 总毛体积
                14 总运费
                15 附加费
                16 标准费用
                17 设备ID
                18 司机名称
                19 保价金额
                20 物流人员名称
                21 域
                22 创建人
                23 起始地名称
                24 原始件数
                25 原始重量
                26 原始体积
                27 目的地省份
                28 目的地城市
                29 目的地区（县）
                30 目的地名称
                31 要求到货日期
                * 32 起始地城市
                * */



                StringBuffer errorMsg = new StringBuffer();

                CargoConfig config = null;
                String msgTitle = "";

                //判断客户名称是否包含字母
                String custName = cells.get(10).toString();

                //包含英文且不好包含中文
                if(custName.matches(".*[a-zA-Z].*") && !custName.matches(".*[\\u4e00-\\u9fa5].*")){
                    config = configJc;
                    msgTitle = "阳关电源进仓";
                }else{
                    String isEts = cells.get(21).toString();
                    config = StringUtils.isNotEmpty(isEts) && isEts.equals("ETS") ? configEts : configYg;
                    msgTitle = StringUtils.isNotEmpty(isEts) && isEts.equals("ETS") ? "阳光电源ETS" : "阳光电源";
                }


                //运单ID
                String originNo = cells.get(2).toString();
                //客户订单号
                String custOrderNo = cells.get(1).toString();



                if(StringUtils.isEmpty(originNo)){
                    continue;
                }
                //创建人
                String createUserId = cells.get(22).toString();
                if("8200007456".equals(createUserId)){
                    continue;
                }
                //判断单据是否推送过
                Integer successCnt = invoiceMapper.countSuccessHyInvoiceByOriginNo(originNo);
                if(successCnt > 0){
                    continue;
                }

                //判断失败次数 超过3次不执行
                Integer failCnt = invoiceMapper.countFailHyInvoiceByOriginNo(originNo);
                if(failCnt >= 3){
                    continue;
                }

                //同一个地址同一车型，相同运输方式
                String detailAddr = cells.get(6).toString();
                String carLenStr = cells.get(12).toString();
                String transTypeStr = cells.get(11).toString();
                successCnt = invoiceMapper.countSuccessHyInvoiceByTypes(detailAddr, carLenStr, transTypeStr);
                if(successCnt > 0){
                    continue;
                }


                InvoiceAddVO invoice = new InvoiceAddVO();
                //客户信息
                ClientPopupVO clientPopupVO = clientService.selectClientById(config.getCustomerId());

                invoice.setCustomerId(clientPopupVO.getCustomerId());
                invoice.setCustName(clientPopupVO.getCustName());
                invoice.setCustAbbr(clientPopupVO.getCustAbbr());
                invoice.setCustCode(clientPopupVO.getCustCode());
                invoice.setBalaCorpId(clientPopupVO.getBalaCorp());
                invoice.setStationDept(clientPopupVO.getStationDept());
                invoice.setStationDeptName(clientPopupVO.getStationDeptName());
                invoice.setBalaDept(clientPopupVO.getBalaDept());
                invoice.setSalesDept(clientPopupVO.getSalesDept());
                invoice.setPsndoc(clientPopupVO.getPsndoc());
                invoice.setBalaType("1");
                invoice.setAppDeliContact(clientPopupVO.getAppDeliContact());
                invoice.setAppDeliMobile(clientPopupVO.getAppDeliMobile());
                invoice.setReferenceRate(clientPopupVO.getReferenceRate());
                //结算客户
                Client custBala = clientService.getDefaultCustBalaByCustomerId(clientPopupVO.getCustomerId());
                if(custBala == null){
                    errorMsg.append("未找到结算客户;");
                }else{
                    invoice.setBalaName(custBala.getCustName());
                    invoice.setBalaCode(custBala.getCustCode());
                    invoice.setBalaCustomerId(custBala.getCustomerId());
                }

                SearchContractPriceVO searchContractPriceVO = new SearchContractPriceVO();
                List<MultipleShippingAddress> shippingAddressList = new ArrayList<>();
                //查询提货地址
                String deliCustName = cells.get(23).toString();
                AddressDTO address = new AddressDTO();
                address.setAddrType("1");
                Map deliParams = new HashMap<String, Object>();
                if(!"2a011a0da1964c999441c2622ecd582f".equals(clientPopupVO.getCustomerId())){
                    deliCustName = "合肥阳光物流园";
                }
                deliParams.put("addrNameOrContact",deliCustName);
                address.setParams(deliParams);

                String goodsName = "光伏逆变器";

                String deliAreaCode = null;

                /** 提货地址名称集合*/
                List<String> deliAddrNameList = new ArrayList<>();
                /** 到货地址名称集合*/
                List<String> arriAddrNameList = new ArrayList<>();

                List<CustAddressVO> deliAddrList = addressService.selectAddressListByCustomerId(address, clientPopupVO.getCustomerId(), 1, null);
                if(deliAddrList == null || deliAddrList.size() == 0){
                    errorMsg.append("未找到发货方:"+deliCustName+";");
                }else{
                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();
                    multipleShippingAddress.setContact(deliAddrList.get(0).getContact());
                    multipleShippingAddress.setMobile(deliAddrList.get(0).getMobile());
                    multipleShippingAddress.setDeliveryId(deliAddrList.get(0).getAddressId());
                    multipleShippingAddress.setProvinceId(deliAddrList.get(0).getProvinceId());
                    multipleShippingAddress.setCityId(deliAddrList.get(0).getCityId());
                    multipleShippingAddress.setAreaId(deliAddrList.get(0).getAreaId());
                    multipleShippingAddress.setProvinceName(deliAddrList.get(0).getProvinceName());
                    multipleShippingAddress.setCityName(deliAddrList.get(0).getCityName());
                    multipleShippingAddress.setAreaName(deliAddrList.get(0).getAreaName());
                    multipleShippingAddress.setAddrCode(deliAddrList.get(0).getAddrCode());

                    deliAreaCode = deliAddrList.get(0).getAreaId();

                    deliAddrNameList.add(deliAddrList.get(0).getAddrName());

                    multipleShippingAddress.setAddrName(deliAddrList.get(0).getAddrName());
                    multipleShippingAddress.setDetailAddr(deliAddrList.get(0).getDetailAddr());
                    //是否加入获取合同价
                    multipleShippingAddress.setIsGetContractPrice(1);
                    multipleShippingAddress.setAddressType(0);

                    List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                    MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                    multipleShippingGoods.setCustOrderno(custOrderNo);
                    //货品信息
                    multipleShippingGoods.setGoodsName(goodsName);
                    //裸装
                    multipleShippingGoods.setPackId("6");
                    //件数
                    multipleShippingGoods.setNum(Double.valueOf(cells.get(24).toString()));
                    //重量
                    multipleShippingGoods.setWeight(Double.valueOf(cells.get(25).toString()));
                    //体积
                    multipleShippingGoods.setVolume(Double.valueOf(cells.get(26).toString()));
                    multipleShippingGoodsList.add(multipleShippingGoods);
                    multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                    shippingAddressList.add(multipleShippingAddress);

                    //单价查询对象
                    List<String> deliAreaIdList = new ArrayList<>();
                    deliAreaIdList.add(deliAddrList.get(0).getAreaId());
                    searchContractPriceVO.setDeliAreaIdList(deliAreaIdList);
                }


                //联系人联系方式
                String contact = cells.get(7).toString();
                contact = contact.replaceAll(" ","").replaceAll(",","").replaceAll("，","")
                        .replaceAll("-","").replaceAll("#","").replaceAll("~","")
                        .replaceAll(":","").replaceAll("：","").replaceAll("，","").replaceAll("。","").replaceAll("\t", "");
                String phoneNumber = cells.get(8).toString();
                phoneNumber = phoneNumber.replaceAll(" ","").replaceAll(",","").replaceAll("，","")
                        .replaceAll("-","").replaceAll("#","").replaceAll("~","")
                        .replaceAll(":","").replaceAll("：","").replaceAll("，","").replaceAll("。","").replaceAll("\t", "");


                if(isValidPhoneNumber(phoneNumber)){
                    contact = contact.replaceAll("1","").replaceAll("2","").replaceAll("3","").replaceAll("4","")
                            .replaceAll("5","").replaceAll("6","").replaceAll("7","").replaceAll("8","")
                            .replaceAll("9","").replaceAll("0","");
                    if(contact.length() <= 1){
                        contact += "先生";
                    }
                }else{
                    String maybePhone = "";
                    for(int j = 0; j < contact.length(); j++){
                        if(contact.substring(j,j+1).equals("1")){
                            if(j + 11 <= contact.length()) {
                                maybePhone = contact.substring(j, j + 11);
                                break;
                            }
                        }
                    }
                    if(isValidPhoneNumber(maybePhone)){
                        phoneNumber = maybePhone;
                    }
                    contact = contact.replaceAll("1","").replaceAll("2","").replaceAll("3","").replaceAll("4","")
                            .replaceAll("5","").replaceAll("6","").replaceAll("7","").replaceAll("8","")
                            .replaceAll("9","").replaceAll("0","");
                    if(contact.length() <= 1){
                        contact += "先生";
                    }
                    if(StringUtils.isBlank(phoneNumber)){
                        phoneNumber = "1";
                    }
                }


                String provinceName = cells.get(27).toString();
                String provinceCode = provinceMapper.getProvinceCode(provinceName);
                String cityName =  cells.get(28).toString();
                if(provinceName.equals("天津市") || provinceName.equals("北京市")  || provinceName.equals("上海市") ){
                    cityName = "市辖区";
                }
                String cityCode = provinceMapper.getCityCode(provinceCode,cityName);
                String areaName = cells.get(29).toString();
                if(StringUtils.isEmpty(areaName)){
                    areaName = cityName;
                }
                String areaCode = provinceMapper.getAreaCode(cityCode,areaName);

                detailAddr = detailAddr.replace(provinceName,"").replace(cityName,"").replace(areaName,"");

                if(StringUtils.isBlank(provinceCode) || StringUtils.isBlank(cityCode) || StringUtils.isBlank(areaCode)){
                    //根据收货地址二次解析
                    Boolean successFlag = true;
                    AddrParseDTO addrParseDTO = addressService.addrParse(cells.get(6).toString());
                    if(addrParseDTO == null || addrParseDTO.getAddressList() == null || addrParseDTO.getAddressList().size() ==0){
                        addrParseDTO = addressService.addrParse(cells.get(30).toString());
                        if(addrParseDTO == null || addrParseDTO.getAddressList() == null || addrParseDTO.getAddressList().size() ==0){
                            //地址三次解析
                            errorMsg.append("到货地址解析异常;");
                            successFlag = false;
                        }
                    }



                    if(successFlag){
                        MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();


                        multipleShippingAddress.setMobile(phoneNumber);
                        multipleShippingAddress.setContact(contact);

                        multipleShippingAddress.setProvinceId(addrParseDTO.getAddressList().get(0).getProvinceId());
                        multipleShippingAddress.setCityId(addrParseDTO.getAddressList().get(0).getCityId());
                        multipleShippingAddress.setAreaId(addrParseDTO.getAddressList().get(0).getAreaId());
                        multipleShippingAddress.setProvinceName(addrParseDTO.getAddressList().get(0).getProvinceName());
                        multipleShippingAddress.setCityName(addrParseDTO.getAddressList().get(0).getCityName());
                        multipleShippingAddress.setAreaName(addrParseDTO.getAddressList().get(0).getAreaName());
                        multipleShippingAddress.setDetailAddr(addrParseDTO.getAddressList().get(0).getDetailAddr());
                        //TODO 地址名称未找到
                        multipleShippingAddress.setAddrName(addrParseDTO.getAddressList().get(0).getDetailAddr());
                        multipleShippingAddress.setAddressType(1);
                        //是否加入获取合同价
                        multipleShippingAddress.setIsGetContractPrice(1);

                        arriAddrNameList.add(detailAddr);

                        List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                        MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                        multipleShippingGoods.setCustOrderno(custOrderNo);
                        multipleShippingGoods.setGoodsName(goodsName);
                        //裸装
                        multipleShippingGoods.setPackId("6");

                        //件数
                        multipleShippingGoods.setNum(Double.valueOf(cells.get(24).toString()));
                        invoice.setNumCount(Double.valueOf(cells.get(24).toString()));
                        //重量
                        multipleShippingGoods.setWeight(Double.valueOf(cells.get(25).toString()));
                        invoice.setWeightCount(Double.valueOf(cells.get(25).toString()));
                        //体积
                        multipleShippingGoods.setVolume(Double.valueOf(cells.get(26).toString()));
                        invoice.setVolumeCount(Double.valueOf(cells.get(26).toString()));

                        multipleShippingGoodsList.add(multipleShippingGoods);
                        multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                        shippingAddressList.add(multipleShippingAddress);

                        List<String> arriAreaIdList = new ArrayList<>();
                        arriAreaIdList.add(addrParseDTO.getAddressList().get(0).getAreaId());
                        searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
                    }
                }else{
                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();

                    multipleShippingAddress.setMobile(phoneNumber);
                    multipleShippingAddress.setContact(contact);

                    multipleShippingAddress.setProvinceId(provinceCode);
                    multipleShippingAddress.setCityId(cityCode);
                    multipleShippingAddress.setAreaId(areaCode);
                    multipleShippingAddress.setProvinceName(provinceName);
                    multipleShippingAddress.setCityName(cityName);
                    multipleShippingAddress.setAreaName(areaName);
                    multipleShippingAddress.setDetailAddr(detailAddr);
                    //TODO 地址名称未找到
                    multipleShippingAddress.setAddrName(detailAddr);
                    multipleShippingAddress.setAddressType(1);
                    //是否加入获取合同价
                    multipleShippingAddress.setIsGetContractPrice(1);

                    arriAddrNameList.add(detailAddr);

                    List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                    MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                    multipleShippingGoods.setCustOrderno(custOrderNo);
                    multipleShippingGoods.setGoodsName(goodsName);
                    //裸装
                    multipleShippingGoods.setPackId("6");

                    //件数
                    multipleShippingGoods.setNum(Double.valueOf(cells.get(24).toString()));
                    invoice.setNumCount(Double.valueOf(cells.get(24).toString()));
                    //重量
                    multipleShippingGoods.setWeight(Double.valueOf(cells.get(25).toString()));
                    invoice.setWeightCount(Double.valueOf(cells.get(25).toString()));
                    //体积
                    multipleShippingGoods.setVolume(Double.valueOf(cells.get(26).toString()));
                    invoice.setVolumeCount(Double.valueOf(cells.get(26).toString()));

                    multipleShippingGoodsList.add(multipleShippingGoods);
                    multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                    shippingAddressList.add(multipleShippingAddress);

                    List<String> arriAreaIdList = new ArrayList<>();
                    arriAreaIdList.add(areaCode);
                    searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
                }

                if(invoice.getNumCount() == null){
                    invoice.setNumCount(0d);
                }
                if(invoice.getWeightCount() == null){
                    invoice.setWeightCount(0d);
                }
                if(invoice.getVolumeCount() == null){
                    invoice.setVolumeCount(0d);
                }

                invoice.setShippingAddressList(shippingAddressList);


                //附加费
                if(StringUtils.isNotBlank(cells.get(15).toString())){
                    BigDecimal otherFee = new BigDecimal(cells.get(15).toString());
                    if(otherFee != null && otherFee.compareTo(BigDecimal.ZERO) != 0){
                        invoice.setOtherFee(otherFee);
                    }
                }

                //车长车型
                String[] carLenArr = carLenStr.split("M");
                if(carLenArr.length > 0 && StringUtils.isNotBlank(carLenArr[0])){
                    SysDictData sysDictData = sysDictDataMapper.selectDictDataByTypeAndLabel("car_len", carLenArr[0]);
                    if(sysDictData == null){
                        errorMsg.append("车型匹配失败："+carLenStr+";");
                    }else{
                        invoice.setCarLen(sysDictData.getDictValue());
                        invoice.setCarLenName(sysDictData.getDictLabel());
                        if("4.2".equals(sysDictData.getDictLabel())){
                            invoice.setWeightCount(1d);
                        }else if("6.8".equals(sysDictData.getDictLabel())){
                            invoice.setWeightCount(10d);
                        }else if("9.6".equals(sysDictData.getDictLabel())){
                            invoice.setWeightCount(15d);
                        }else if("13".equals(sysDictData.getDictLabel()) || "13.75".equals(sysDictData.getDictLabel()) || "17.5".equals(sysDictData.getDictLabel())){
                            invoice.setWeightCount(30d);
                        }else{
                            invoice.setWeightCount(0.1d);
                        }
                    }
                    if(carLenArr.length > 1){
                        invoice.setMemo("车型要求："+carLenStr);
                    }


                }else{
                    //不限
                    invoice.setCarLen("17");
                    invoice.setCarLenName("不限");

                    invoice.setWeightCount(0.1d);

                }

                invoice.setCarType(config.getCarType());
                invoice.setCarTypeName(config.getCarTypeName());

                if(transTypeStr.contains("零担")){
                    invoice.setTransCode("1");
                    invoice.setTransName("公路零担");

                    invoice.setWeightCount(0.1d);
                    //零担按体积
                    invoice.setBillingMethod("2");
                }else{
                    invoice.setTransCode("0");
                    invoice.setTransName("公路整车");
                    //整车包车
                    invoice.setBillingMethod("3");
                }

                //运输方式
                /**
                 * 阳光电源，抓单分配调度规则调整@包包上神
                 *
                 * 安徽省内，整车+零担，黄成宝
                 * 安徽省外：
                 *     零担 ：资源采购
                 *      整车：黄成宝
                 */
                if("2a011a0da1964c999441c2622ecd582f".equals(clientPopupVO.getCustomerId())){
                    invoice.setTransLineId(config.getTransLineId());
                    invoice.setTransLineName(config.getTransLineName());
                }else if("安徽省".equals(provinceName)){
                    invoice.setTransLineId("981");
                    invoice.setTransLineName("黄成宝");
                }else{
                    if("0".equals(invoice.getTransCode())){
                        invoice.setTransLineId("981");
                        invoice.setTransLineName("黄成宝");
                    }else{
                        //零担资源采购部
                        invoice.setTransLineId("607");
                        invoice.setTransLineName("资源采购组");
                    }
                }

                for(MultipleShippingAddress multipleShippingAddress : invoice.getShippingAddressList()){
                    for(MultipleShippingGoods multipleShippingGood : multipleShippingAddress.getShippingGoodsList()){
                        multipleShippingGood.setWeight(invoice.getWeightCount());
                    }
                }


                //要求提货日期,到货日期
                String deliTimeStr = cells.get(5).toString();
                String arriTimeStr = cells.get(31).toString();

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                invoice.setReqDeliDate(sdf.parse(deliTimeStr));

                //提货日期判断
                if(invoice.getReqDeliDate().compareTo(new Date()) < 0 ){
                    invoice.setReqDeliDate(new Date());
                }

                //根据提货日期和地址算时效
                List<CustTransLine> timeLine = custTransLineMapper.getCustTransLineByCustIdAndArea(invoice.getCustomerId(), deliAreaCode, areaCode, invoice.getTransCode());
                if(timeLine != null && timeLine.size() > 0){
                    // 获取当前时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(invoice.getReqDeliDate());
                    calendar.add(Calendar.HOUR, timeLine.get(0).getTimeLimit().intValue());
                    invoice.setReqArriDate(calendar.getTime());
                }else{
                    invoice.setReqArriDate(sdf.parse(arriTimeStr));
                }

                //到货时间
                if(invoice.getReqArriDate().compareTo(new Date()) < 0 ){
                    // 获取当前时间
                    Date date = new Date();
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    calendar.add(Calendar.DATE, 1);
                    invoice.setReqArriDate(calendar.getTime());
                }

                //紧急程度普通
                invoice.setUrgentLevel(config.getUrgentLevel());
                //卸货地数量1
                invoice.setUnloadPlaceNum(config.getUnloadPlaceNum());
                invoice.setIsMultiple(0);
                //否大件
                invoice.setIsOversize(config.getIsOversize());
                //是否开票
                invoice.setBillingType(config.getBillingType());

                invoice.setResidentsId("");
                //公里数
                invoice.setMileage(Double.valueOf(cells.get(9).toString()));

                //议价
                invoice.setIfBargain(0);
                //invoice.setCostAmount(new BigDecimal(cells.get(columnMap.get("总运费")).toString()));
                //invoice.setBargainMemo("UIBOOT抓取阳光数据");

                if ("4".equals(invoice.getBillingMethod())) {
                    invoice.setIsRoundTrip(1);
                }else {
                    invoice.setIsRoundTrip(0);
                }

                //获取单价和合同价
                searchContractPriceVO.setCustomerId(invoice.getCustomerId());
                searchContractPriceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));
                searchContractPriceVO.setGoodsCharacter("0");
                searchContractPriceVO.setNum(new BigDecimal(invoice.getNumCount()));
                searchContractPriceVO.setWeight(new BigDecimal(invoice.getWeightCount()));
                searchContractPriceVO.setVolume(new BigDecimal(invoice.getVolumeCount()));
                searchContractPriceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));
                searchContractPriceVO.setCarLen(invoice.getCarLen());
                searchContractPriceVO.setCarType(invoice.getCarType());
                searchContractPriceVO.setDeliAddrNameList(deliAddrNameList);
                searchContractPriceVO.setArriAddrNameList(arriAddrNameList);
                searchContractPriceVO.setGoodsName(goodsName);
                searchContractPriceVO.setIsOversize(config.getIsOversize());
                searchContractPriceVO.setTransCode(invoice.getTransCode());
                searchContractPriceVO.setIsRoundTrip(invoice.getIsRoundTrip());

                Map<String, String> resMap = invoiceService.getPrice(searchContractPriceVO);
                if(resMap.get("type").equals("1")){
                    SysDictData billingTypeDictData = sysDictDataMapper
                            .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
                    if (billingTypeDictData == null) {
                        return AjaxResult.error("请选择正确的开票类型！");
                    }

                    //税率
                    BigDecimal rate = billingTypeDictData.getNumVal1();

                    //是否含税  0不含税  1含税*
                    String isIncludeTax = resMap.get("isIncludeTax");

                    //总评价
                    BigDecimal totalPrice = new BigDecimal(resMap.get("totalPrice"));
                    //单价
                    BigDecimal price = new BigDecimal(resMap.get("price"));
                    //成本价
                    String totalCostPrice = resMap.get("totalCostPrice");
                    String costBillingType = resMap.get("costBillingType");
                    if(StringUtils.isNotBlank(totalCostPrice)){
                        BigDecimal costPrice = new BigDecimal(totalCostPrice);
                        invoice.setCostPrice(costPrice);
                        invoice.setCostBillingType(costBillingType);
                    }

                    //是否含税  0不含税  1含税*
                    if ("0".equals(isIncludeTax)) {
                        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                            invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                            invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                        }else {
                            invoice.setCostAmount(totalPrice);
                            invoice.setUnitPrice(price);
                        }
                    }else {
                        //含税
                        invoice.setCostAmount(totalPrice);
                        invoice.setUnitPrice(price);
                    }
                }else{
                    errorMsg.append("获取单价/合同价失败;");
                }

                HYInvoiceRecord hyInvoiceRecord = new HYInvoiceRecord();
                hyInvoiceRecord.setId(IdUtil.simpleUUID());
                hyInvoiceRecord.setOriginNo(originNo);
                hyInvoiceRecord.setCustOrderno(custOrderNo);
                hyInvoiceRecord.setRegDate(new Date());
                hyInvoiceRecord.setArriAddress(cells.get(30).toString());
                hyInvoiceRecord.setCarLen(carLenStr);
                hyInvoiceRecord.setTransType(transTypeStr);
                hyInvoiceRecord.setType(2);

                //新建发货单返回信息
                Map<String,String> invoiceResMap = null;

                if(errorMsg.length() > 0){
                    hyInvoiceRecord.setIfSuccess(1);
                    hyInvoiceRecord.setErrorMsg(errorMsg.toString());
                }else{
                    invoice.setCorScrId("uibot");
                    invoice.setRegScrId("uibot");
                    AjaxResult ajaxResult = invoiceService.insertInvoice(invoice, 0);
                    if(ajaxResult.getCode() == 0){
                        hyInvoiceRecord.setIfSuccess(0);
                        invoiceResMap = (Map<String, String>) ajaxResult.getData();
                        String invoiceId = invoiceResMap.get("invoiceId");
                        hyInvoiceRecord.setInvoiceId(invoiceId);
                        //更新发货单创建人
                        Invoice invoiceUp = new Invoice();
                        invoiceUp.setInvoiceId(invoiceId);
                        invoiceUp.setRegUserId("310211");
                        invoiceUp.setRegUserName("RPA01");
                        invoiceMapper.updateInvoice(invoiceUp);
                    }else{
                        hyInvoiceRecord.setIfSuccess(1);
                        hyInvoiceRecord.setErrorMsg(ajaxResult.getMsg());
                    }
                }
                invoiceMapper.insertHyInvoiceRecord(hyInvoiceRecord);

                //写死
                String deliCity = "合肥市";

                //推送琦欣小蜜
                StringBuffer sb = new StringBuffer();
                sb.append("`" + msgTitle + "`接单提醒").append("\n");
                if(hyInvoiceRecord.getIfSuccess() == 0) {
                    sb.append(">状态:<font color=\"info\">成功</font>").append("\n");
                    sb.append(">提:").append(deliCity).append("\n");
                    sb.append(">到:").append(cells.get(30).toString()).append("\n");
                    //sb.append(">联系方式:").append(cells.get(columnMap.get("联系人姓名(D)")).toString()).append("\n");
                    sb.append(">运输要求:").append(transTypeStr);
                    String carLen = cells.get(12).toString();
                    if(StringUtils.isNotEmpty(carLen)){
                        sb.append("/"+carLen);
                    }
                    if(invoice.getVolumeCount() != 0){
                        sb.append("/"+invoice.getVolumeCount()+"方");
                    }
                    sb.append("\n");
                    //sb.append(">总运费:").append(invoice.getCostAmount()+"元").append("\n");
                    sb.append(">阳光单号:").append(originNo).append("\n");
                    sb.append(">客户单号:").append(custOrderNo).append("\n");
                    sb.append(">发货单号:").append(invoiceResMap.get("invoiceNo")).append("\n");
                }else{
                    sb.append(">状态:<font color=\"warning\">失败</font>").append("\n");
                    sb.append(">提:").append(deliCity).append("\n");
                    sb.append(">到:").append(cells.get(30).toString()).append("\n");
                    sb.append(">联系方式:").append(cells.get(7).toString()).append("\n");
                    sb.append(">运输要求:").append(transTypeStr);
                    String carLen = cells.get(12).toString();
                    if(StringUtils.isNotEmpty(carLen)){
                        sb.append("/"+carLen);
                    }
                    if(invoice.getVolumeCount() != 0){
                        sb.append("/"+invoice.getVolumeCount()+"方");
                    }
                    sb.append("\n");
                    //sb.append(">总运费:").append(invoice.getCostAmount()+"元").append("\n");
                    sb.append(">阳光单号:").append(originNo).append("\n");
                    sb.append(">客户单号:").append(custOrderNo).append("\n");
                    sb.append(">失败原因:").append(hyInvoiceRecord.getErrorMsg()).append("\n");
                    sb.append(">提醒次数:").append(++failCnt).append("/3(超过三次不再提醒)").append("\n");
                }

                HashSet<Long> userIds = new HashSet<>();
                //包扬
                userIds.add(236177l);
                //石晶
                userIds.add(297453l);
                for(Long sendId : userIds){
                    wecomHandler.pushMarkdown(sendId, sb.toString());
                }
                //运维销售一组推送
                WechatMessageUtils.sendMarkDownMessage("bd705e9b-6543-4184-871e-70b554ab16df",sb.toString());
            }
        }
        return AjaxResult.success();
    }

    @PostMapping("downloadReceipt")
    @ResponseBody
    public AjaxResult downloadReceipt(Invoice invoice, String format) {
        String fileName = invoiceService.downloadReceipt(invoice, format);
        return AjaxResult.success((Object) fileName);
    }

    @RepeatSubmit
    @PostMapping("resync")
    @ResponseBody
    public AjaxResult resyncSp(String spNo) throws IOException {
        String invoiceId = wecomService.getBizIdBySpNo(spNo);
        List<Map<String, Object>> recordList = wecomService.listRecord("fhd", invoiceId);
        if (!recordList.get(0).get("spNo").equals(spNo)) {
            // 最新的审批单号与该审批单号不一致
            return AjaxResult.error("当前审批单号不是最新的审批单号，请刷新重试");
        }
        Invoice db = invoiceService.selectInvoiceById(invoiceId);
        if (!db.getVbillstatus().equals(InvoiceStatusEnum.IN_APPROVAL.getValue())) {
            return AjaxResult.error("非审核中单据，不可更新");
        }
        String token = wecomHandler.getSpToken();
        String spDetail = WecomHelper.approvalDetail(spNo, token);
        ApprovalDetail detail = objectMapper.readValue(spDetail, ApprovalDetail.class);
        Integer sp_status = detail.getInfo().getSp_status();
        if (sp_status == 2) {
            String json = (String) recordList.get(0).get("extString");
            InvoiceAffirmVO.AffirmVO affirmVO = objectMapper.readValue(json, InvoiceAffirmVO.AffirmVO.class);
            InvoiceAffirmVO invoice = new InvoiceAffirmVO();
            List<InvoiceAffirmVO.AffirmVO> affirmVOList = new ArrayList<>();
            affirmVOList.add(affirmVO);
            invoice.setAffirmList(affirmVOList);
            try {
                AjaxResult result = invoiceService.confirmInvoice(invoice);
                if (result.getCode() == 500) {
                    result.setCode(600); // 前端对600特殊处理
                }
                return result;
            } catch (Exception e) {
                logger.error("确认异常：", e);
                return new AjaxResult(600, e.getMessage() == null ? e.getClass().getSimpleName() : e.getMessage(), null);
            }
        } else if (sp_status == 3) {
            Invoice target = new Invoice();
            target.setInvoiceId(invoiceId);
            target.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
            invoiceService.updateInvoice(target);
            return AjaxResult.success();
        } else if (sp_status == 1) {
            return AjaxResult.error("审批尚未结束，不可同步");
        } else {
            return AjaxResult.error("不可同步状态：" + sp_status);
        }
    }

    /**
     * 将审批中的发货单重置为新建状态
     *
     * @param invoiceId
     * @return
     */
    @PostMapping("renew")
    @ResponseBody
    public AjaxResult renew(String invoiceId) {
        Invoice db = invoiceService.selectInvoiceById(invoiceId);
        if (!db.getVbillstatus().equals(InvoiceStatusEnum.IN_APPROVAL.getValue())) {
            return AjaxResult.error("非审核中单据，不可重置");
        }
        Invoice target = new Invoice();
        target.setInvoiceId(invoiceId);
        target.setVbillstatus(InvoiceStatusEnum.NEW.getValue());
        invoiceService.updateInvoice(target);
        return AjaxResult.success();
    }

    /**
     *
     *
     * @param
     * @return
     */
    @RequiresPermissions("tms:invoice:list")
    @PostMapping("/get_copy_dis_data")
    @ResponseBody
    public AjaxResult getCopyDisData(String invoiceNo) {

        List<CopyDisDataVO> list = invoiceService.getCopyDisData(invoiceNo);

        Map<String, Object> map = new HashMap<>();

        map.put("list", list);
        map.put("count", list.size());
        return AjaxResult.success(map);
    }

    /**
     *
     *
     * @param
     * @return
     */
    @RequiresPermissions("tms:invoice:list")
    @PostMapping("/copy_dis_data")
    @ResponseBody
    public AjaxResult copyDisData(String invoiceNo) {

        List<CopyDisDataVO> list = invoiceService.copyDisData(invoiceNo);

        Map<String, Object> map = new HashMap<>();

        map.put("list", list);
        map.put("count", list.size());
        return AjaxResult.success(map);
    }

    /**
     * 新增三方
     * @param invoiceId
     * @return
     */
    @RequestMapping("/addOtherFee")
    public String addOtherFee(String invoiceId,ModelMap map){
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
        map.put("invoice", invoice);
        //查询在途 第三方 审核信息
        EntrustCost entrustCost = new EntrustCost();
        entrustCost.setInvoiceId(invoiceId);
        List<EntrustCost> entrustCostList = entrustCostMapper.selectEntrustCostList(entrustCost);
        map.put("entrustCostList", entrustCostList);
        return "tms/invoice/addOtherFee";
    }

    @RequestMapping("/saveOtherFeeCheck")
    @ResponseBody
    public AjaxResult saveOtherFeeCheck(OtherFee otherFee){
        return invoiceService.saveOtherFeeCheck(otherFee);
    }

    @PostMapping("/importTest")
    @ResponseBody
    public AjaxResult importData(MultipartFile file) {
        return AjaxResult.success();
    }


    @GetMapping("/getToken")
    @ResponseBody
    public AjaxResult getToken() {
        asyncJobService.getSunshineToken(false);
        return AjaxResult.success();
    }

    public RfqEnquiryLine.Addr matchAddress(String address){
        RfqEnquiryLine.Addr addr = new RfqEnquiryLine.Addr();
        //根据地址匹配省
        List<Map<String, String>> provinceList = provinceMapper.matchProvinces(address);
        //判断是否匹配省 成功：继续匹配市和区。 失败：全部地址添到详细地址
        if( provinceList != null && provinceList.size() > 0 ) {
            //省编码，名称
            String proCode = provinceList.get(0).get("PROVINCE_CODE");
            String proName = provinceList.get(0).get("PROVINCE_NAME");
            addr.setProvinceId(proCode);
            addr.setProvinceName(proName);
            //移除省字符串
            address = address.replace(proName, "");

            //判断省是否为直辖市
            if ("北京市".equals(proName) || "天津市".equals(proName) || "上海市".equals(proName) || "重庆市".equals(proName)) {
                //直接匹配区
                address = address.replace("市辖区", "");
                List<Map<String, String>> areaList = provinceMapper.matchAreas(address);
                if (areaList != null && areaList.size() > 0) {
                    String areaCode = areaList.get(0).get("AREA_CODE");
                    String areaName = areaList.get(0).get("AREA_NAME");
                    String cityCode = areaList.get(0).get("CITY_ID");
                    String cityName = areaList.get(0).get("CITY_NAME");
                    addr.setCityId(cityCode);
                    addr.setCityName(cityName);
                    addr.setAreaId(areaCode);
                    addr.setAreaName(areaName);
                    address = address.replace(areaName, "");
                }else{
                    return null;
                }
            } else {
                //匹配省
                List<Map<String, String>> cityList = provinceMapper.matchCitiesByCode(address, proCode);
                if (cityList != null && cityList.size() > 0) {
                    String cityCode = cityList.get(0).get("CITY_CODE");
                    String cityName = cityList.get(0).get("CITY_NAME");
                    addr.setCityId(cityCode);
                    addr.setCityName(cityName);
                    //移除市字符串
                    address = address.replace(cityName, "");

                    //匹配区
                    List<Map<String, String>> areaList = provinceMapper.matchAreasByCode(address, cityCode);
                    if (areaList != null && areaList.size() > 0) {
                        String areaCode = areaList.get(0).get("AREA_CODE");
                        String areaName = areaList.get(0).get("AREA_NAME");
                        addr.setAreaId(areaCode);
                        addr.setAreaName(areaName);
                        address = address.replace(areaName, "");
                    }
                }else{
                    return null;
                }
            }
        }else{
            return null;
        }
        addr.setDetailAddr(address);
        return addr;
    }

    @RequestMapping("fix-invoice-amount")
    @ResponseBody
    public String fixInvoiceAmount(String invoiceId) {
        invoiceService.synInvoiceCostAmount(invoiceId, "fix-invoice-amount");
        return "ok";
    }

    /**
     * 中粮
     * @return
     */
    @RequestMapping("/handle66KCInvoice")
    @ResponseBody
    public AjaxResult handle66KCInvoice() throws ParseException {
        //获取系统配置信息
        CargoConfig config = cargoConfigMapper.selectByPrimaryKey("66kc");

        HttpResponse execute = HttpRequest.post("https://sso.66yunlian.cn/api/sso/user/keySecret/v1/transferSsoApi?source=https://truck.66yunlian.cn/&targetPath=22")
                .body("{\n" +
                        "  \"username\": \"18806293737\",\n" +
                        "  \"password\": \"xk97EAjr4wq3N/Ob1elG5Q==\",\n" +
                        "  \"captcha\": \"\",\n" +
                        "  \"captchaId\": \"\",\n" +
                        "  \"rsaEncryptKey\": \"aERTMZo9sRqQdfu/WvH0E+fux8oGdUNBwXyKVCjAHyuSUUarC9c3VFTghugQUav4JDHzxxBJhojEnUCEQdVwSO8+n4pXT66UHsRgLUbwkMmhEBdsLchNcYGn1a6RKb5ygzD2OMrYnkrnw3qgGpa87aH0z7ACwADnBY2Xp2zE/Uo=\"\n" +
                        "}")
                .execute();
        System.out.println(execute.body());
        Map<String, List<String>> headers = execute.headers();
        List<String> cookieList = headers.get("Set-Cookie");
        String token = null;
        for(String cookie : cookieList){
            if(cookie.startsWith("token")){
                token = cookie.split(";")[0].split("=")[1];
            }
        }


        Map<String, String> headersPost = new HashMap<>();

        headersPost.put("Accept", "*/*");
        headersPost.put("Accept-encoding", "gzip, deflate, br, zstd");
        headersPost.put("Accept-language", "zh-CN,zh;q=0.9");
        headersPost.put("Bt", "FLEET+CONSIGNER");
        headersPost.put("Content-length", "211");
        headersPost.put("Content-type", "application/json;charset=UTF-8");
        headersPost.put("Cookie", "_bl_uid=eRmjw3b0cj6atqwhywF4kp7lyUvm; acw_tc=0bdd34ba17337049680141884ec003d1da2ae945e6adc88da0a5942475bf3d; userType=; token=e29869c5680b417f8fca61db1d6797af; SECKEY_ABVK=mzuODm9MRee89OdNtrQWw9fmYhe6j4b0y4SPBXZ2+XY%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjwK8DHsBjl_rE3WeBg4mUEBudViMzneuZUUxXpP-rK4166DmUZE1PxCmMfUgHyiu5KCUzmrwdlIZfko-mo1t2cs-Mmnh9fiM3jAfSilgQhboGeKjU5XXFcNPc0-q_KyPnSOOSIxfEPPMTXZl4whEYF2tJbjA35qWudGZ21DT-CJpAMhXxSzocueyGozvEuIItg");
        headersPost.put("eagleeye-pappname", "null");
        headersPost.put("eagleeye-sessionid", "sOm9g4Xzgj7bmI78agIvbs7kFFFI");
        headersPost.put("eagleeye-traceid", "386930bc17337050218741019fb557");
        headersPost.put("origin", "https://truck.66yunlian.cn");
        headersPost.put("os", "PC");
        headersPost.put("priority", "u=1, i");
        headersPost.put("random", "17337050218745623");
        headersPost.put("referer", "https://truck.66yunlian.cn/orderTicket?status=W004");
        headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
        headersPost.put("sec-ch-ua-mobile", "?0");
        headersPost.put("sec-ch-ua-platform", "\"Windows\"");
        headersPost.put("sec-fetch-dest", "empty");
        headersPost.put("sec-fetch-mode", "cors");
        headersPost.put("sec-fetch-site", "same-origin");
        headersPost.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");
        headersPost.put("version", "next");

        HttpResponse executeAfter = HttpRequest.post("https://truck.66yunlian.cn/api/ucenter/passport/afterLogin")
                .headerMap(headersPost, true)
                .body("{\n" +
                        "  \"globalToken\": \""+token+"\"\n" +
                        "}")
                .execute();
        JSONObject executeAfterJson = JSONObject.parseObject(executeAfter.body());
        String accessToken = executeAfterJson.getJSONObject("data").getString("accessToken");
        headersPost.put("authorization",accessToken);

        HttpRequest request = HttpRequest.post("https://truck.66yunlian.cn/api/billfacade/entrusts/list")
                .headerMap(headersPost, true)
                .body("{\n" +
                        "  \"consigNo\": \"\",\n" +
                        "  \"endDate\": \"\",\n" +
                        "  \"entrustNo\": \"\",\n" +
                        "  \"goodsName\": \"\",\n" +
                        "  \"pageSize\": 50,\n" +
                        "  \"recvCompany\": \"\",\n" +
                        "  \"sendCompany\": \"\",\n" +
                        "  \"start\": 1,\n" +
                        "  \"startDate\": \"\",\n" +
                        "  \"status\": \"W004\",\n" +
                        "  \"consigName\": \"\",\n" +
                        "  \"name\": \"\",\n" +
                        "  \"customNum1\": \"\",\n" +
                        "  \"completeType\": \"W00W\"\n" +
                        "}");

        String body = request.execute().body();
        JSONObject listJson = JSONObject.parseObject(body);
        JSONObject data = listJson.getJSONObject("data");
        if(data.getInteger("size") > 0){
            JSONArray list = data.getJSONArray("list");
            for(int i = 0; i < list.size(); i++){
                String entrustId = list.getJSONObject(i).getString("entrustId");

                String detailRequest = HttpRequest.post("https://truck.66yunlian.cn/api/billfacade/entrusts/queryData")
                        .headerMap(headersPost, true)
                        .body("{\n" +
                                "  \"entrustId\": \""+entrustId+"\"\n" +
                                "}").execute().body();
                JSONObject detailJson = JSONObject.parseObject(detailRequest);
                //明细数据
                JSONObject para = detailJson.getJSONObject("data");

                StringBuffer errorMsg = new StringBuffer();

                String originId = para.getString("id");
                String originNo = para.getString("entrustNo");
                String expectPickTime = para.getString("expectPickTime");
                String expectArriveTime = para.getString("expectArriveTime");
                //客户单号
                String custOrderNo = para.getString("consigNo");
                if(StringUtils.isEmpty(originNo)){
                    continue;
                }
                //判断单据是否推送过
                Integer successCnt = invoiceMapper.countSuccessHyInvoiceByOriginNo(originNo);
                if(successCnt > 0){
                    continue;
                }

               /* InvoiceAddVO invoice = new InvoiceAddVO();
                //TODO 货品信息
                String goodsNameInsert = "";
                //客户信息
                ClientPopupVO  clientPopupVO = clientService.selectClientById("a0d160dca2054499b534b95e7a4a020b");

                invoice.setCustomerId(clientPopupVO.getCustomerId());
                invoice.setCustName(clientPopupVO.getCustName());
                invoice.setCustAbbr(clientPopupVO.getCustAbbr());
                invoice.setCustCode(clientPopupVO.getCustCode());
                invoice.setBalaCorpId(clientPopupVO.getBalaCorp());
                invoice.setStationDept(clientPopupVO.getStationDept());
                invoice.setStationDeptName(clientPopupVO.getStationDeptName());
                invoice.setBalaDept(clientPopupVO.getBalaDept());
                invoice.setSalesDept(clientPopupVO.getSalesDept());
                invoice.setPsndoc(clientPopupVO.getPsndoc());
                invoice.setBalaType("1");
                invoice.setAppDeliContact(clientPopupVO.getAppDeliContact());
                invoice.setAppDeliMobile(clientPopupVO.getAppDeliMobile());
                invoice.setReferenceRate(clientPopupVO.getReferenceRate());
                //结算客户
                Client custBala = clientService.getDefaultCustBalaByCustomerId(clientPopupVO.getCustomerId());
                if(custBala == null){
                    errorMsg.append("未找到结算客户;");
                }else{
                    invoice.setBalaName(custBala.getCustName());
                    invoice.setBalaCode(custBala.getCustCode());
                    invoice.setBalaCustomerId(custBala.getCustomerId());
                }

                SearchContractPriceVO searchContractPriceVO = new SearchContractPriceVO();
                List<MultipleShippingAddress> shippingAddressList = new ArrayList<>();
                //查询提货地址
                String deliCustName = para.getString("sendCompany");
                AddressDTO address = new AddressDTO();
                address.setAddrType("1");
                Map params = new HashMap<String, Object>();
                params.put("addrNameOrContact",deliCustName);
                address.setParams(params);

                //提货地到货地
                String deliAddr = "";
                String arriAddr = "";

                *//** 提货地址名称集合*//*
                List<String> deliAddrNameList = new ArrayList<>();
                *//** 到货地址名称集合*//*
                List<String> arriAddrNameList = new ArrayList<>();

                List<CustAddressVO> deliAddrList = addressService.selectAddressListByCustomerId(address, clientPopupVO.getCustomerId(), 1, null);
                if(deliAddrList == null || deliAddrList.size() == 0){
                    errorMsg.append("未找到发货方;");
                }else{
                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();
                    multipleShippingAddress.setContact(deliAddrList.get(0).getContact());
                    multipleShippingAddress.setMobile(deliAddrList.get(0).getMobile());
                    multipleShippingAddress.setDeliveryId(deliAddrList.get(0).getAddressId());
                    multipleShippingAddress.setProvinceId(deliAddrList.get(0).getProvinceId());
                    multipleShippingAddress.setCityId(deliAddrList.get(0).getCityId());
                    multipleShippingAddress.setAreaId(deliAddrList.get(0).getAreaId());
                    multipleShippingAddress.setProvinceName(deliAddrList.get(0).getProvinceName());
                    multipleShippingAddress.setCityName(deliAddrList.get(0).getCityName());
                    multipleShippingAddress.setAreaName(deliAddrList.get(0).getAreaName());
                    multipleShippingAddress.setAddrCode(deliAddrList.get(0).getAddrCode());
                    multipleShippingAddress.setAddrName(deliAddrList.get(0).getAddrName());
                    multipleShippingAddress.setDetailAddr(deliAddrList.get(0).getDetailAddr());
                    multipleShippingAddress.setAddressType(0);
                    multipleShippingAddress.setIsGetContractPrice(1);

                    deliAddrNameList.add(deliAddrList.get(0).getAddrName());

                    deliAddr = multipleShippingAddress.getProvinceName() + multipleShippingAddress.getCityName() + multipleShippingAddress.getAreaName() + multipleShippingAddress.getDetailAddr();

                    List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                    //TODO循环货品
                    JSONArray goodsList = para.getJSONArray("goodsList");
                    for(int k = 0 ; k < goodsList.size(); k++) {
                        JSONObject goods = goodsList.getJSONObject(k);
                        MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                        multipleShippingGoods.setCustOrderno(custOrderNo);
                        multipleShippingGoods.setGoodsName(goods.getString("goodsName").split("（")[0]);
                        //散装
                        multipleShippingGoods.setPackId("9");

                        multipleShippingGoods.setWeight(goods.getDouble("weight"));

                        multipleShippingGoodsList.add(multipleShippingGoods);
                        multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                        shippingAddressList.add(multipleShippingAddress);
                    }

                    //单价查询对象
                    List<String> deliAreaIdList = new ArrayList<>();
                    deliAreaIdList.add(deliAddrList.get(0).getAreaId());
                    searchContractPriceVO.setDeliAreaIdList(deliAreaIdList);
                }

                //到货地址解析
                String[] waybillLineNames = para.getString("waybillLineName").split("-");
                String destination = waybillLineNames[1];

                String addrName = para.getString("deliveryPartyDescriptions");

                AddrParseDTO addrParseDTO = addressService.addrParse(destination);

                if(addrParseDTO == null || addrParseDTO.getAddressList() == null || addrParseDTO.getAddressList().size() ==0){
                    errorMsg.append("到货地址解析异常;");
                }else{
                    MultipleShippingAddress multipleShippingAddress = new MultipleShippingAddress();

                    String consigneePhone = para.getJSONArray("orderInfoPOList").getJSONObject(0).getString("consigneePhone");
                    String[] result = ZLUtils.splitReceiverAndPhone(consigneePhone);
                    if (result!= null) {
                        multipleShippingAddress.setContact(result[0]);
                        multipleShippingAddress.setMobile(result[1]);
                    } else {
                        multipleShippingAddress.setContact("识别失败");
                        multipleShippingAddress.setMobile("88888888");
                    }

                    multipleShippingAddress.setProvinceId(addrParseDTO.getAddressList().get(0).getProvinceId());
                    multipleShippingAddress.setCityId(addrParseDTO.getAddressList().get(0).getCityId());
                    multipleShippingAddress.setAreaId(addrParseDTO.getAddressList().get(0).getAreaId());
                    multipleShippingAddress.setProvinceName(addrParseDTO.getAddressList().get(0).getProvinceName());
                    multipleShippingAddress.setCityName(addrParseDTO.getAddressList().get(0).getCityName());
                    multipleShippingAddress.setAreaName(addrParseDTO.getAddressList().get(0).getAreaName());
                    multipleShippingAddress.setDetailAddr(addrParseDTO.getAddressList().get(0).getDetailAddr());
                    multipleShippingAddress.setIsGetContractPrice(1);
                    multipleShippingAddress.setAddrName(addrName);
                    multipleShippingAddress.setAddressType(1);

                    arriAddrNameList.add(addrName);

                    arriAddr = multipleShippingAddress.getProvinceName() + multipleShippingAddress.getCityName() + multipleShippingAddress.getAreaName() + multipleShippingAddress.getDetailAddr();

                    List<MultipleShippingGoods> multipleShippingGoodsList = new ArrayList<>();
                    MultipleShippingGoods multipleShippingGoods = new MultipleShippingGoods();
                    multipleShippingGoods.setCustOrderno(custOrderNo);
                    multipleShippingGoods.setGoodsName(goodsNameInsert);
                    //散装
                    multipleShippingGoods.setPackId("9");

                    multipleShippingGoods.setWeight(para.getDouble("dispatchTotalWeight"));

                    multipleShippingGoodsList.add(multipleShippingGoods);
                    multipleShippingAddress.setShippingGoodsList(multipleShippingGoodsList);

                    shippingAddressList.add(multipleShippingAddress);

                    List<String> arriAreaIdList = new ArrayList<>();
                    arriAreaIdList.add(addrParseDTO.getAddressList().get(0).getAreaId());
                    searchContractPriceVO.setArriAreaIdList(arriAreaIdList);
                }

                invoice.setTransLineId(config.getTransLineId());
                invoice.setTransLineName(config.getTransLineName());

                invoice.setShippingAddressList(shippingAddressList);
                invoice.setVolumeCount(0d);


                //要求提货日期,到货日期
                String fpTimeStr = para.getString("sendCarTime");
                if(StringUtils.isEmpty(fpTimeStr)){
                    errorMsg.append("获取分配日期失败;");
                }else{
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    Date fpTime = sdf.parse(fpTimeStr);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(fpTime);
                    invoice.setReqDeliDate(calendar.getTime());
                    calendar.add(Calendar.DATE,1);
                    invoice.setReqArriDate(calendar.getTime());
                }

                //根据重量判断车长
                *//**
                 * X<6吨:4.2
                 * 6<=X<=12吨；6.8
                 * 12<X<=22吨:9.6
                 * X>22吨：13米
                 *//*
                BigDecimal dispatchTotalWeight =  para.getBigDecimal("dispatchTotalWeight");
                if(dispatchTotalWeight.compareTo(new BigDecimal(6)) < 0){
                    invoice.setCarLen("8");
                    invoice.setCarLenName("4.2");
                }else if(dispatchTotalWeight.compareTo(new BigDecimal(12)) <= 0){
                    invoice.setCarLen("12");
                    invoice.setCarLenName("6.8");
                }else if(dispatchTotalWeight.compareTo(new BigDecimal(22)) <= 0){
                    invoice.setCarLen("16");
                    invoice.setCarLenName("9.6");
                }else{
                    invoice.setCarLen("1");
                    invoice.setCarLenName("13");
                }

                *//**
                 * 根据要求选择车型
                 * 普通车——平板货车
                 * 厢车——厢式货车
                 * 飞翼车——飞翼车
                 *//*
                String bookVehicleModelDesc = para.getString("bookVehicleModelDesc");
                if("普通车".equals(bookVehicleModelDesc)){
                    invoice.setCarType("H05");
                    invoice.setCarTypeName("平板货车");
                }else if("厢车".equals(bookVehicleModelDesc)){
                    invoice.setCarType("H02");
                    invoice.setCarTypeName("厢式货车");
                }else if("飞翼车".equals(bookVehicleModelDesc)){
                    invoice.setCarType("f01");
                    invoice.setCarTypeName("飞翼车");
                }else{
                    invoice.setCarType(config.getCarType());
                    invoice.setCarTypeName(config.getCarTypeName());
                }

                //运输方式
                invoice.setTransCode(config.getTransCode());
                invoice.setTransName(config.getTransName());
                //紧急程度普通
                invoice.setUrgentLevel(config.getUrgentLevel());
                //卸货地数量1
                invoice.setUnloadPlaceNum(config.getUnloadPlaceNum());
                invoice.setIsMultiple(0);
                //否大件
                invoice.setIsOversize(config.getIsOversize());
                //是否开票
                invoice.setBillingType(config.getBillingType());
                //结算方式，回单结算
                invoice.setBalaType(config.getBalaType());
                //计价方式
                invoice.setBillingMethod(config.getBillingMethod());

                //合同价
                invoice.setIfBargain(0);

                invoice.setResidentsId("");

                //获取单价和合同价
                searchContractPriceVO.setCustomerId(invoice.getCustomerId());
                searchContractPriceVO.setBillingMethod(Integer.valueOf(invoice.getBillingMethod()));
                searchContractPriceVO.setGoodsCharacter("0");
                searchContractPriceVO.setWeight(dispatchTotalWeight);
                searchContractPriceVO.setMileage(invoice.getMileage() == null ? BigDecimal.ZERO : BigDecimal.valueOf(invoice.getMileage()));
                searchContractPriceVO.setCarLen(invoice.getCarLen());
                searchContractPriceVO.setCarType(invoice.getCarType());
                searchContractPriceVO.setGoodsName(goodsNameInsert);
                searchContractPriceVO.setIsOversize(config.getIsOversize());
                searchContractPriceVO.setDeliAddrNameList(deliAddrNameList);
                searchContractPriceVO.setArriAddrNameList(arriAddrNameList);

                Map<String, String> resMap = invoiceService.getPrice(searchContractPriceVO);
                if(resMap.get("type").equals("1")){
                    SysDictData billingTypeDictData = sysDictDataMapper
                            .selectDictDataByTypeAndValue("billing_type", invoice.getBillingType());
                    if (billingTypeDictData == null) {
                        return AjaxResult.error("请选择正确的开票类型！");
                    }

                    //税率
                    BigDecimal rate = billingTypeDictData.getNumVal1();

                    //是否含税  0不含税  1含税*
                    String isIncludeTax = resMap.get("isIncludeTax");

                    //总评价
                    BigDecimal totalPrice = new BigDecimal(resMap.get("totalPrice"));
                    //单价
                    BigDecimal price = new BigDecimal(resMap.get("price"));
                    //成本价
                    String totalCostPrice = resMap.get("totalCostPrice");
                    if(StringUtils.isNotBlank(totalCostPrice)){
                        BigDecimal costPrice = new BigDecimal(totalCostPrice);
                        invoice.setCostPrice(costPrice);
                    }

                    //是否含税  0不含税  1含税*
                    if ("0".equals(isIncludeTax)) {
                        if (rate != null && rate.compareTo(BigDecimal.ZERO) != 0) {
                            invoice.setCostAmount(NumberUtil.round(NumberUtil.mul(totalPrice, rate), 2));
                            invoice.setUnitPrice(NumberUtil.round(NumberUtil.mul(price, rate), 2));
                        }else {
                            invoice.setCostAmount(totalPrice);
                            invoice.setUnitPrice(price);
                        }
                    }else {
                        //含税
                        invoice.setCostAmount(totalPrice);
                        invoice.setUnitPrice(price);
                    }
                }else{
                    errorMsg.append("获取单价/合同价失败;");
                }


                //是否人为创建标记
                Boolean hasCreateFlag = false;

                //新建发货单返回信息
                Map<String,String> invoiceResMap = null;

                //判断是否已经人为建单
                List<Invoice> invoices = invoiceMapper.selectInvoiceByCustOrderNo(custOrderNo);
                if(invoices != null && invoices.size() > 0){
                    hasCreateFlag = true;
                    hyInvoiceRecord.setIfSuccess(0);
                    hyInvoiceRecord.setInvoiceId(invoices.get(0).getInvoiceId());
                }else if(errorMsg.length() > 0){
                    hyInvoiceRecord.setIfSuccess(1);
                    hyInvoiceRecord.setErrorMsg(errorMsg.toString());
                }else{
                    invoice.setCorScrId("uibot");
                    invoice.setRegScrId("uibot");

                    AjaxResult ajaxResult = invoiceService.insertInvoice(invoice, 0);
                    if(ajaxResult.getCode() == 0){
                        hyInvoiceRecord.setIfSuccess(0);
                        invoiceResMap = (Map<String, String>) ajaxResult.getData();
                        String invoiceId = invoiceResMap.get("invoiceId");
                        hyInvoiceRecord.setInvoiceId(invoiceId);
                        //更新发货单创建人
                        Invoice invoiceUp = new Invoice();
                        invoiceUp.setInvoiceId(invoiceId);
                        invoiceUp.setRegUserId("310211");
                        invoiceUp.setRegUserName("RPA01");
                        invoiceMapper.updateInvoice(invoiceUp);
                    }else{
                        hyInvoiceRecord.setIfSuccess(1);
                        hyInvoiceRecord.setErrorMsg(ajaxResult.getMsg());
                    }
                }
                invoiceMapper.insertHyInvoiceRecord(hyInvoiceRecord);

                if(hasCreateFlag){
                    continue;
                }

                //推送琦欣小蜜
                StringBuffer sb = new StringBuffer();
                sb.append("`中粮面业`接单提醒").append("\n");
                if(hyInvoiceRecord.getIfSuccess() == 0) {
                    sb.append(">下单状态:<font color=\"info\">成功</font>").append("\n");
                    sb.append(">中粮单号:").append(originNo).append("\n");
                    sb.append(">发货单号:").append(invoiceResMap.get("invoiceNo")).append("\n");
                    sb.append(">SAP单号:").append(custOrderNo).append("\n");
                    sb.append(">装货地:").append(deliAddr).append("\n");
                    sb.append(">卸货地:").append(arriAddr).append("\n");
                }else{
                    sb.append(">下单状态:<font color=\"warning\">失败</font>").append("\n");
                    sb.append(">中粮单号:").append(originNo).append("\n");
                    sb.append(">失败原因:").append(hyInvoiceRecord.getErrorMsg()).append("\n");
                    sb.append(">SAP单号:").append(custOrderNo).append("\n");
                }
                HashSet<Long> userIds = new HashSet<>();
                //包扬
                userIds.add(236177l);
                //石晶
                userIds.add(297453l);
                for(Long id : userIds){
                    wecomHandler.pushMarkdown(id, sb.toString());
                }*/


                HYInvoiceRecord hyInvoiceRecord = new HYInvoiceRecord();
                hyInvoiceRecord.setId(IdUtil.simpleUUID());
                hyInvoiceRecord.setOriginId(originId);
                hyInvoiceRecord.setOriginNo(originNo);
                hyInvoiceRecord.setCustOrderno(custOrderNo);
                hyInvoiceRecord.setRegDate(new Date());
                hyInvoiceRecord.setType(3);
                hyInvoiceRecord.setIfSuccess(0);
                hyInvoiceRecord.setArriAddress(para.getString("recvAddress")+para.getString("recvCompany"));
                invoiceMapper.insertHyInvoiceRecord(hyInvoiceRecord);

                String deliAddr = para.getString("sendProvince")+para.getString("sendCity")+para.getString("sendDistrict")+para.getString("sendCompany");
                String arriAddr = para.getString("recvProvince")+para.getString("recvCity")+para.getString("recvDistrict")+para.getString("recvCompany");

                Double weight = 0d;
                JSONArray goodsList = para.getJSONArray("goodsList");
                for(int k = 0 ; k < goodsList.size(); k++) {
                    JSONObject goods = goodsList.getJSONObject(k);
                    weight = weight+goods.getDouble("weight");
                }

                StringBuffer sb = new StringBuffer();
                sb.append("`山东圣奥`接单提醒").append("\n");
                sb.append(">平台单号:").append(originNo).append("\n");
                sb.append(">SAP单号:").append(custOrderNo).append("\n");
                sb.append(">装货地:").append(deliAddr).append("\n");
                sb.append(">卸货地:").append(arriAddr).append("\n");
                sb.append(">重量:").append(weight).append("吨").append("\n");
                sb.append(">要求提货日期:").append(expectPickTime).append("\n");
                sb.append(">要求到货日期:").append(expectArriveTime).append("\n");
                //运维销售一组推送
                WechatMessageUtils.sendMarkDownMessage("f2cda68b-6717-4d5e-a75f-f570c08fadd2",sb.toString());
            }
        }


        return AjaxResult.success();
    }

    @RequestMapping("/frChangeGuidePrice")
    @ResponseBody
    @Transactional
    public AjaxResult frChangeGuidePrice(@RequestParam Map<String, String> params) {
        StringBuffer message = new StringBuffer();

        //特殊情况：大件/三超、报关、往返包车、多装多卸、零担 不更新指导价库
        Integer integer = segmentService.updateSegmentGuidePriceByFrConfig(params);
        if(integer != 0){
            message.append("单据指导价已更新;");
        }else{
            message.append("无单据可更新;");
        }

        if(!params.get("isOversize").equals("1")
                && !params.get("isCustomsClearance").equals("1")
                && !params.get("billingMethod").equals("4")
                && params.get("loadPlaceNum").equals("1")
                && params.get("unloadPlaceNum").equals("1")
                && (params.get("transCode").equals("0") || params.get("transCode").equals("4") || params.get("transCode").equals("15"))
        ){

            Integer priceType = null;
            if (params.get("transCode").equals("0")) {
                priceType = ReferencePriceTypeConstant.PRICE_BASIC;
            } else if (params.get("transCode").equals("15")) {
                priceType = ReferencePriceTypeConstant.PRICE_DANGEROUS_GOODS;
            } else if (params.get("transCode").equals("4")) {
                priceType = ReferencePriceTypeConstant.PRICE_COLD_CHAIN;
            }


            ReferencePrice price = new ReferencePrice();
            price.setCarLen(params.get("carLen"));
            price.setCarType(params.get("carType"));
            price.setDeliAreaId(params.get("deliAreaId"));
            price.setArriAreaId(params.get("arriAreaId"));
            int i = referencePriceService.insertPriceAndHistory(price, new BigDecimal(params.get("guidePrice")), priceType, "fineReport");

            if(integer != 0){
                message.append("指导价库已更新;");
            }
        }else{
            message.append("大件/三超、报关、往返包车、多装多卸、零担不更新指导价库;");
        }

        return AjaxResult.success(message.toString());

    }


    @RequestMapping("/frChangeLotGuidePrice")
    @ResponseBody
    @Transactional
    public AjaxResult frChangeLotGuidePrice(@RequestParam Map<String, String> params) {
        StringBuffer message = new StringBuffer();

        //特殊情况：大件/三超、报关、往返包车、多装多卸、零担 不更新指导价库
        Integer integer = segmentService.updateLotGuidePriceByFrConfig(params);
        if(integer != 0){
            message.append("单据指导价已更新;");
        }else{
            message.append("无单据可更新;");
        }

        if(!params.get("isOversize").equals("1")
                && !params.get("isCustomsClearance").equals("1")
                && !params.get("billingMethod").equals("7")
                && params.get("loadPlaceNum").equals("1")
                && params.get("unloadPlaceNum").equals("1")
                && (params.get("transCode").equals("0") || params.get("transCode").equals("4") || params.get("transCode").equals("15"))
        ){

            Integer priceType = null;
            if (params.get("transCode").equals("0")) {
                priceType = ReferencePriceTypeConstant.PRICE_BASIC;
            } else if (params.get("transCode").equals("15")) {
                priceType = ReferencePriceTypeConstant.PRICE_DANGEROUS_GOODS;
            } else if (params.get("transCode").equals("4")) {
                priceType = ReferencePriceTypeConstant.PRICE_COLD_CHAIN;
            }


            ReferencePrice price = new ReferencePrice();
            price.setCarLen(params.get("carLen"));
            price.setCarType(params.get("carType"));
            price.setDeliAreaId(params.get("deliAreaId"));
            price.setArriAreaId(params.get("arriAreaId"));
            int i = referencePriceService.insertPriceAndHistory(price, new BigDecimal(params.get("guidePrice")), priceType, "fineReport");

            if(integer != 0){
                message.append("指导价库已更新;");
            }
        }else{
            message.append("大件/三超、报关、往返包车、多装多卸、零担不更新指导价库;");
        }

        return AjaxResult.success(message.toString());

    }
}
