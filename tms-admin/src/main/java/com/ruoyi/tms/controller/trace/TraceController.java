package com.ruoyi.tms.controller.trace;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.ruoyi.framework.util.hw.CallUtils;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.system.domain.SysDept;
import com.ruoyi.system.domain.SysDictData;
import com.ruoyi.system.domain.SysUploadFile;
import com.ruoyi.system.service.*;
import com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum;
import com.ruoyi.tms.constant.carrier.EntrustPicEnum;
import com.ruoyi.tms.constant.carrier.EntrustStatusEnum;
import com.ruoyi.tms.constant.dept.DeptIdConstant;
import com.ruoyi.tms.constant.entrust.EntrustExpStatusEnum;
import com.ruoyi.tms.constant.entrust.EntrustWorkStatusEnum;
import com.ruoyi.tms.constant.entrust.EntrustWorkTypeEnum;
import com.ruoyi.tms.constant.finance.FreeTypeEnum;
import com.ruoyi.tms.constant.finance.PayDetailStatusEnum;
import com.ruoyi.tms.constant.finance.ReceiveDetailStatusEnum;
import com.ruoyi.tms.domain.basic.Car;
import com.ruoyi.tms.domain.basic.CarDriver;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.basic.Driver;
import com.ruoyi.tms.domain.call.CallUser;
import com.ruoyi.tms.domain.carrier.*;
import com.ruoyi.tms.domain.check.EntrustCostCheckDetail;
import com.ruoyi.tms.domain.entrustException.*;
import com.ruoyi.tms.domain.finance.*;
import com.ruoyi.tms.domain.invoice.*;
import com.ruoyi.tms.domain.segment.SegPackGoods;
import com.ruoyi.tms.domain.trace.*;
import com.ruoyi.tms.mapper.basic.CallUserMapper;
import com.ruoyi.tms.mapper.basic.CarrierPeriodMapper;
import com.ruoyi.tms.mapper.carrier.EntrustExpMapper;
import com.ruoyi.tms.mapper.carrier.EntrustLotMapper;
import com.ruoyi.tms.mapper.carrier.EntrustMapper;
import com.ruoyi.tms.mapper.entrustException.*;
import com.ruoyi.tms.mapper.finance.OtherFeeMapper;
import com.ruoyi.tms.mapper.finance.PayDetailMapper;
import com.ruoyi.tms.mapper.finance.PayRecordMapper;
import com.ruoyi.tms.mapper.finance.ReceivableReconciliationMapper;
import com.ruoyi.tms.mapper.invoice.InvoiceMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingAddressMapper;
import com.ruoyi.tms.mapper.invoice.MultipleShippingGoodsMapper;
import com.ruoyi.tms.mapper.segment.SegPackGoodsMapper;
import com.ruoyi.tms.mapper.trace.EntrustWorkMapper;
import com.ruoyi.tms.mapper.trace.MultipleGoodsConnMapper;
import com.ruoyi.tms.mapper.trace.TEntrustExpMapper;
import com.ruoyi.tms.mapper.trace.TraceMapper;
import com.ruoyi.tms.service.basic.*;
import com.ruoyi.tms.service.business.ICustOtherFeeService;
import com.ruoyi.tms.service.carrier.*;
import com.ruoyi.tms.service.client.ClientService;
import com.ruoyi.tms.service.client.IAutoDispatchConfigService;
import com.ruoyi.tms.service.entrustException.IEntrustExceptionMoreService;
import com.ruoyi.tms.service.entrustException.IInsurancePolicyService;
import com.ruoyi.tms.service.finance.*;
import com.ruoyi.tms.service.invoice.IInvoiceService;
import com.ruoyi.tms.service.invoice.IMultipleShippingAddressService;
import com.ruoyi.tms.service.message.IWechatHookService;
import com.ruoyi.tms.service.nfp.INfpSyncService;
import com.ruoyi.tms.service.trace.*;
import com.ruoyi.tms.service.trace.impl.AsyncJobService;
import com.ruoyi.tms.service.wecom.IWecomService;
import com.ruoyi.tms.vo.basic.CarrierPeriodDTO;
import com.ruoyi.tms.vo.business.CustOtherFeeVO;
import com.ruoyi.tms.vo.client.ClientPopupVO;
import com.ruoyi.tms.vo.finance.DriverCollectionAddVO;
import com.ruoyi.tms.vo.finance.ReceivableReconciliationVO;
import com.ruoyi.tms.vo.finance.ReceiveDetailVO;
import com.ruoyi.tms.vo.invoice.InvoiceReceiveDetailVO;
import com.ruoyi.tms.vo.trace.*;
import com.ruoyi.util.LocationUtils;
import com.ruoyi.util.QRCodeUtil;
import com.ruoyi.util.ShiroUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.StringUtils.getBigDecimal;

/**
 * 在途跟踪
 *
 * @Author: baoy
 * @Date: 2019/9/26
 */
@Controller
@RequestMapping("/trace")
public class TraceController extends BaseController {
    private static String PREFIX = "tms/trace";
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private ITraceService traceService;
    @Autowired
    private IEntrustService entrustService;
    @Autowired
    private ISysUploadFileService uploadFileService;
    @Autowired
    private ICloseAccountService closeAccountService;
    @Autowired
    private IEntrustLotCarDriverService entrustLotCarDriverService;
    @Autowired
    private ICarLocusService carLocusService;
    @Autowired
    private ICarService carService;
    @Autowired
    private ISysUploadFileService sysUploadFileService;
    @Autowired
    private IEntPackGoodsService entPackGoodsService;
    @Autowired
    private IEntrustExpService entrustExpService;
    @Autowired
    private IPayCheckSheetService payCheckSheetService;
    @Autowired
    private IPayDetailService payDetailService;
    @Autowired
    private IEntrustLotService entrustLotService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private ICarrierService carrierService;
    @Autowired
    private ICarDriverService carDriverService;
    @Autowired
    private IDriverService driverService;
    @Autowired
    private IInvoiceService invoiceService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IReceiveDetailService receiveDetailService;
    @Autowired
    private IEntrustExpResultService entrustExpResultServiceService;
    @Autowired
    private IDriverCollectionService driverCollectionService;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private DictService dictService;
    @Autowired
    private TEntrustExpMapper tEntrustExpMapper;
    @Autowired
    private TExpEntrustMoreMapper tExpEntrustMoreMapper;
    @Autowired
    private TInsuranceMapper insuranceMapper;
    @Autowired
    private TExceptionTrackMapper tExceptionTrackMapper;
    @Autowired
    private INfpSyncService nfpSyncService;
    @Autowired
    private TraceMapper traceMapper;
    @Autowired
    private PayDetailMapper payDetailMapper;
    @Autowired
    private EntrustLotMapper entrustLotMapper;
    @Autowired
    private IEntrustCostService entrustCostService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private PayRecordMapper payRecordMapper;
    @Autowired
    private TExpInvoiceMapper expInvoiceMapper;
    @Autowired
    private ResidualValueTrackingMapper residualValueTrackingMapper;
    @Autowired
    private IEntrustExceptionMoreService entrustExceptionMoreService;
    @Resource
    private IWechatHookService wechatHookService;
    @Autowired
    private IAdvancePayService advancePayService;
    @Autowired
    private CallUserMapper callUserMapper;
    @Autowired
    private EntrustExpMapper entrustExpMapper;
    @Autowired
    private EntrustMapper entrustMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private CarrierPeriodMapper carrierPeriodMapper;
    @Autowired
    private ICustOtherFeeService custOtherFeeService;
    @Resource
    private IWecomService wecomService;
    @Autowired
    private IBookingSendService bookingSendService;
    @Resource
    private IAutoDispatchConfigService autoDispatchConfigService;
    @Autowired
    private IMultipleShippingAddressService multipleShippingAddressService;
    @Autowired
    private MultipleShippingGoodsMapper shippingGoodsMapper;
    @Resource
    private INewDepositService newDepositService;
    @Autowired
    private MultipleGoodsConnMapper multipleGoodsConnMapper;
    @Autowired
    private EntrustWorkMapper entrustWorkMapper;
    @Autowired
    private AsyncJobService asyncJobService;
    @Resource
    private ICarrierPeriodService carrierPeriodService;
    @Autowired
    private SegPackGoodsMapper segPackGoodsMapper;
    @Autowired
    private ReceivableReconciliationMapper receivableReconciliationMapper;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private OtherFeeMapper otherFeeMapper;
    @Autowired
    private MultipleShippingAddressMapper multipleShippingAddressMapper;
    @Autowired
    private IInsurancePolicyService insurancePolicyService;

    /**
     * 跳转在途跟踪页面
     * @param vbillstatus
     * @param needTraceToday
     * @param receiptStatus
     * @param ifReceipt
     * @param pickStartDate       要求提货开始日
     * @param pickEndDate         要求提货结束日
     * @param reqArriDateEnd      要求到货结束日
     * @param combinedState    0：回单上传已超时
     *                         1:回单上传即将超时
     *                         2:正本回单已超时
     *                         3:正本回单即将超时
     * @param isTimeLimit      时间限制  1：近三个月
     *                                 2:近三个月不包含今天
     * @param map
     * @return
     */
    @RequiresPermissions("tms:trace:view")
    @GetMapping()
    public String trace(@RequestParam(required = false) String vbillstatus
            , @RequestParam(required = false) String needTraceToday
            , @RequestParam(required = false) String receiptStatus
            , @RequestParam(required = false) String ifReceipt
            , @RequestParam(required = false) String combinedState
            , @RequestParam(required = false) String pickStartDate
            , @RequestParam(required = false) String pickEndDate
            , @RequestParam(required = false) String reqArriDateEnd
            , @RequestParam(required = false) Integer isTimeLimit
            , ModelMap map) {
        map.put("entrustStatusEnum", EntrustStatusEnum.getAllToMap());
        //获取关账时间
        List<CloseAccount> list = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", list);
        //委托单待确认状态
        String toAffirmstatus = EntrustStatusEnum.TO_AFFIRM.getValue();
        map.put("toAffirmstatus", toAffirmstatus);
        //委托单已确认状态
        String affirmStatus = EntrustStatusEnum.AFFIRM.getValue();
        map.put("affirmStatus", affirmStatus);
        // 运营部
        map.put("salesDept", deptService.selectDeptByParentIds(DeptIdConstant.getSalesDeptId()));
        //是否车队数据 0否 1是
        map.put("isFleetData", 0);
        //map.put("isFleetAssgin",0);


        if (StringUtils.isNotEmpty(vbillstatus)) {
            map.put("vbillstatus", vbillstatus);
        }
        if (StringUtils.isNotEmpty(needTraceToday)) {
            map.put("needTraceToday", needTraceToday);
        }
        if (StringUtils.isNotEmpty(receiptStatus)) {
            map.put("receiptStatus", receiptStatus);
        }
        if (StringUtils.isNotEmpty(ifReceipt)) {
            map.put("ifReceipt", ifReceipt);
        }
        if (StringUtils.isNotEmpty(combinedState)) {
            map.put("combinedState", combinedState);
            map.put("pickStartDate", "");
        }

        if (pickStartDate != null) {
            map.put("pickStartDate", pickStartDate);
        }
        if (pickEndDate != null) {
            map.put("pickEndDate", pickEndDate);
        }
        if (reqArriDateEnd != null) {
            map.put("reqArriDateEnd", reqArriDateEnd);
        }

        if (isTimeLimit != null) {
            if (isTimeLimit == 1) {
                map.put("pickStartDate", DateUtil.format(DateUtil.offsetMonth(new Date(), -3), "yyyy-MM-dd"));
                map.put("pickEndDate", DateUtil.format(new Date(), "yyyy-MM-dd"));
            } else if (isTimeLimit == 2) {
                map.put("pickStartDate", DateUtil.format(DateUtil.offsetMonth(new Date(), -3), "yyyy-MM-dd"));
                map.put("pickEndDate", DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd"));
            }
        }

        return "tms/trace/trace";
    }

    /**
     * 新增异常跟踪 委托单列表(该承运商对应的)
     * @param map
     * @return
     */
    @RequiresPermissions("tms:trace:view")
    @GetMapping(value = "/entrustlist")
    public String entrustList(@RequestParam(value = "carrierId", required = false) String carrierId,
                              @RequestParam(value = "lotNo", required = false) String lotNo, ModelMap map) {
        map.put("entrustStatusEnum", EntrustStatusEnum.getAllToMap());
        //获取关账时间
        List<CloseAccount> list = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", list);
        //委托单待确认状态
        String toAffirmstatus = EntrustStatusEnum.TO_AFFIRM.getValue();
        map.put("toAffirmstatus", toAffirmstatus);
        //委托单已确认状态
        String affirmStatus = EntrustStatusEnum.AFFIRM.getValue();
        map.put("affirmStatus", affirmStatus);
        //是否车队数据 0否 1是
        map.put("isFleetData",0);
        //map.put("isFleetAssgin",0);
        //所属承运商
        map.put("carrierId",carrierId);
        //原始运单号
        map.put("lotno", lotNo);
        return PREFIX + "/entrustlist";
    }


    /**
     * 删除异常跟踪
     *
     * @param ids
     * @return
     */
    @PostMapping("/remove")
    @Log(title = "异常跟踪", businessType = BusinessType.DELETE)
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(traceService.deleteExpByIds(ids));
    }

    /**
     * 跳转在途导出页面
     * @param map
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:export"})
    @GetMapping(value = "/toExport")
    public String backConfirmPickPage(ModelMap map,@RequestParam(required = false) String entrustIds) {
        map.put("entrustStatusEnum", EntrustStatusEnum.getAllToMap());
        map.put("nowDate", DateUtil.format(new Date(), "yyyy-MM-dd"));
        map.put("entrustIds",entrustIds);
        if(StringUtils.isNotBlank(entrustIds)){
            String invoiceVbillno = "";
            for(String entrustId : entrustIds.split(",")){
                Entrust entrust = entrustMapper.selectEntrustById(entrustId);
                invoiceVbillno += entrust.getInvoiceVbillno() + " ";
            }
            map.put("invoiceVbillno",invoiceVbillno);
        }
        return "tms/trace/trace_export";
    }

    /**
     * 在途跟踪导出
     * @param
     * @return
     */
    @Log(title = "在途跟踪列表数量")
    @RequiresPermissions("tms:trace:export")
    @PostMapping("/count_export")
    @ResponseBody
    public AjaxResult countExport(EntrustTraceExcel entrustTraceExcel) {
        List<EntrustTraceExcel> list = traceMapper.selectTraceListForExport(entrustTraceExcel);
        return AjaxResult.success(list.size());
    }

    /**
     * 在途跟踪导出
     * @param
     * @return
     */
    @Log(title = "在途跟踪列表导出", businessType = BusinessType.EXPORT)
    @RequiresPermissions("tms:trace:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(EntrustTraceExcel entrustTraceExcel) {
        List<EntrustTraceExcel> list = traceMapper.selectTraceListForExport(entrustTraceExcel);
        ExcelUtil<EntrustTraceExcel> util = new ExcelUtil<>(EntrustTraceExcel.class);
        if(StringUtils.isNotEmpty(entrustTraceExcel.getTraceExportColumn())) {
            List<String> nochoseColumn = new ArrayList<>();

            Map<String, String> trace_export_column = dictService.getDictValueLabelMap("trace_export_column");//所有列的map
            Set<String> set1 = trace_export_column.keySet();//所有列的集合

            String traceExportColumn = entrustTraceExcel.getTraceExportColumn();
            String[] split = traceExportColumn.split(",");//已选中列的数组
            List<String> strings1 = Arrays.asList(split);
            Set set2 = new HashSet();
            set2.addAll(strings1);

            boolean b1 = set1.removeAll(set2);


            /*for (int i = 0; i < split.length; i++) {
                String s = split[i];
                boolean b = trace_export_column.containsKey(s);
                if(!b) {
                    nochoseColumn.add(s);
                }
            }*/

            /*List<String> allChoose = Arrays.asList(split);
            if(!allChoose.contains("客户订单号")) {
                nochoseColumn.add("custOrderNo");
            }
            if(!allChoose.contains("发货日期")) {
                nochoseColumn.add("reqDeliDate");
            }
            if(!allChoose.contains("发货单号")) {
                nochoseColumn.add("invoiceVbillNo");
            }
            if(!allChoose.contains("客户名称")) {
                nochoseColumn.add("custAbbr");
            }
            if(!allChoose.contains("发货地址")) {
                nochoseColumn.add("shippingAddress");
            }
            if(!allChoose.contains("发货人")) {
                nochoseColumn.add("deliContact");
            }
            if(!allChoose.contains("收货单位")) {
                nochoseColumn.add("addrName");
            }
            if(!allChoose.contains("收货地址")) {
                nochoseColumn.add("receivingAddress");
            }
            if(!allChoose.contains("收货人")) {
                nochoseColumn.add("arriContact");
            }
            if(!allChoose.contains("货品名称")) {
                nochoseColumn.add("goodsName");
            }
            if(!allChoose.contains("件数")) {
                nochoseColumn.add("goodsNum");
            }
            if(!allChoose.contains("重量(吨)")) {
                nochoseColumn.add("goodsWeight");
            }
            if(!allChoose.contains("体积(m3)")) {
                nochoseColumn.add("goodsVolume");
            }
            if(!allChoose.contains("跟踪信息")) {
                nochoseColumn.add("carLocus");
            }
            if(!allChoose.contains("发车时间")) {
                nochoseColumn.add("actDeliDate");
            }
            if(!allChoose.contains("预计到达时间")) {
                nochoseColumn.add("estimatedArrivalTime");
            }
            if(!allChoose.contains("签收日期")) {
                nochoseColumn.add("actArriDate");
            }
            if(!allChoose.contains("状态")) {
                nochoseColumn.add("vbillstatus");
            }
            if(!allChoose.contains("车型")) {
                nochoseColumn.add("carTypeName");
            }
            if(!allChoose.contains("车号")) {
                nochoseColumn.add("carno");
            }*/

            // 将新集合转化为新数组，输出
            String[] toArray = set1.toArray(new String[set1.size()]);
            util.hideColumn(toArray);
        }
//        //测试多项
//        if(true) {
//            String [] arr = new String[]{"custOrderNo", "reqDeliDate", "invoiceVbillNo", "custAbbr"};
//            util.hideColumn(arr);
//        }
        Map<String, Object> dataBase = new HashMap<>();
        dataBase.put("vbillstatus", dictService.getDictValueLabelMap("vbillstatus"));
        return util.exportExcel(list, "跟踪列表",dataBase);
    }

    /**
     * 查看在途跟踪列表
     *
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(EntrustDto entrustDto) {
        startPage();
        entrustDto.setIsFleetData("0"); //0:不是车队数据
        List<EntrustDto> list = traceService.selectTraceList(entrustDto);
        return getDataTable(list);
    }


    @PostMapping("/pickAll")
    @ResponseBody
    public AjaxResult pickAll(EntrustDto entrustDto) throws ParseException{
        entrustDto.setVbillstatus(EntrustStatusEnum.AFFIRM.getValue());
        entrustDto.setIsFleetData("0"); //0:不是车队数据
        return traceService.pickAll(entrustDto);
    }

    @PostMapping("/arrivalAll")
    @ResponseBody
    public AjaxResult arrivalAll(EntrustDto entrustDto) throws ParseException{
        entrustDto.setVbillstatus(EntrustStatusEnum.PICK_UP.getValue());
        entrustDto.setIsFleetData("0"); //0:不是车队数据
        return traceService.arrivalAll(entrustDto);
    }

    /**
     * 异常跟踪
     *
     * @param map
     * @return
     */
    @RequiresPermissions("tms:trace:abnormal:view")
    @GetMapping("/abnormal/{entrustId}")
    public String abnormal(ModelMap map, @PathVariable("entrustId") String entrustId) {
        map.put("entrustId", entrustId);
        //查询运营部调度组 责任组
        List<SysDept> deptDispatherList = deptService.selectDeptByParentIds(DeptIdConstant.getSalesDispatcherDeptId());
        map.put("deptDispatherList", deptDispatherList);
        //待处理状态
        map.put("driversignStatusValue", EntrustExpStatusEnum.DRIVERSIGN.getValue());
        //待一级审核状态
        map.put("firstCheckStatusValue", EntrustExpStatusEnum.FIRST_CHECK.getValue());
        return PREFIX + "/abnormalTracking";
    }

    /**
     * 查看异常跟踪列表
     *
     * @return
     */
    @PostMapping("/adnormalList")
    @ResponseBody
    public TableDataInfo adnormalList(EntrustExpVo entrustExpVo) {
        startPage();
        List<EntrustExpVo> entrustExp = traceService.selectEntrustExp(entrustExpVo);
        return getDataTable(entrustExp);
    }



    /**
     * 异常跟踪修改
     *
     * @param entrustExp
     * @return
     */
    /*@RepeatSubmit
    @PostMapping("/updateAbnormal")
    @Log(title = "异常跟踪", businessType = BusinessType.UPDATE)
    @ResponseBody
    public AjaxResult updateAbnormal(@RequestBody EntrustExp entrustExp) {
        EntrustExp tableExp = entrustExpService.selectEntrustExpById(entrustExp.getEntrustExpId());
        if (!entrustExp.getCorDate().equals(tableExp.getCorDate())) {
            return AjaxResult.error("该数据已被修改，请刷新页面！");
        }
        return traceService.editEntrustExp(entrustExp);
    }*/

    /**
     * 推送紧急单
     * @param entrustId
     * @return
     */
    @PostMapping("/push_urgent")
    @RequiresPermissions("tms:trace:pushUrgent")
    @ResponseBody
    public AjaxResult pushUrgent(@RequestParam("entrustId") String entrustId) {
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        Invoice invoice = invoiceService.selectInvoiceById(entrust.getOrderno());
        invoice.setMemo(entrust.getMemo());

        wechatHookService.sendMsgUrgentData(invoice, shiroUtils.getUserId().toString(), shiroUtils.getSysUser().getUserName());

        return AjaxResult.success();
    }


    /**
     * 锁定应付
     * @param lotId
     * @return
     */
    @PostMapping("/lockPaydetail")
    @ResponseBody
    public AjaxResult lockPaydetail(@RequestParam("lotId") String lotId,@RequestParam("singleLock") String singleLock) {
        EntrustLot entrustLot = new EntrustLot();
        entrustLot.setEntrustLotId(lotId);
        entrustLot.setSingleLock(singleLock);
        return toAjax(entrustLotService.updateEntrustLot(entrustLot));
    }

    @PostMapping("/lockPaydetailMemo")
    @ResponseBody
    public AjaxResult lockPaydetail(EntrustLot entrustLot) {
        return toAjax(entrustLotService.updateEntrustLot(entrustLot));
    }


    /**
     * 跳转新增异常跟踪页面
     * @param map
     * @param entrustId
     * @return
     */
    @RequiresPermissions(value = {"tms:fleet:trace:abnormal:add","tms:trace:abnormal:add"},logical = Logical.OR)
    @GetMapping("/addAbnormal/{entrustId}")
    public String addAbnormal(ModelMap map, @PathVariable("entrustId") String entrustId) {
        //主异常  1:异常主信息 2:损失收入信息list 3:追踪信息
        //发货单: 1:发货单基础信息 2:客户(运营部+业务员) 3:应收信息
        //运单:  1:运单基础信息 2:承运商信息 3:应付信息(多条付款信息)
        //委托单
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust", entrust);
        //发货单信息
        Invoice invoiceSearch = new Invoice();
        invoiceSearch.setDelFlag(0);
        invoiceSearch.setInvoiceId(entrust.getOrderno());
        InvoiceReceiveDetailVO invoiceReceiveDetailVO = invoiceMapper.selectInvoiceForException(invoiceSearch);
        map.put("invoiceReceiveDetail", invoiceReceiveDetailVO);
        map.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());

        //运单信息
        EntrustLot lotSearch = new EntrustLot();
        lotSearch.setEntrustLotId(entrust.getLotId());
        lotSearch.setDelFlag(0);
        EntrustLotPayDetailPayRecordVO entrustLotPayDetailPayRecordVOS = entrustLotMapper.selectLotPayDetailRecordVO(lotSearch);
        map.put("lotPayDetailPayRecord", entrustLotPayDetailPayRecordVOS);
        map.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());

        //提前生成ID
        map.put("entrustExpId",IdUtil.simpleUUID());
        return "tms/trace/abnormal_temp";
    }

    /**
     * 获取三方
     *
     * @param invoiceIds
     * @return
     */
    @PostMapping("/getOtherFee")
    @RequiresPermissions(value = {"tms:fleet:trace:abnormal:add","tms:trace:abnormal:add"},logical = Logical.OR)
    @ResponseBody
    public AjaxResult getOtherFee(@RequestParam("invoiceIds") String invoiceIds) {
        List<CustOtherFeeVO> list = custOtherFeeService.selectCustOtherFeeListByLotId(Convert.toStrArray(invoiceIds));
        list = list.stream().sorted(Comparator.comparing(CustOtherFeeVO::getInvoiceVbillno)).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 跳转修改异常跟踪页面
     *
     * @param map
     * @param entrustExpId
     * @return
     */
    @RequiresPermissions("tms:trace:abnormaledit")
    @GetMapping("/editAbnormal/{entrustExpId}")
    public String editAbnormal(ModelMap map, @PathVariable("entrustExpId") String entrustExpId) {
        map.put("entrustExpId", entrustExpId);
        //主异常
        TEntrustExp tEntrustExp = tEntrustExpMapper.selectByPrimaryKey(entrustExpId);
        map.put("entrustExp", tEntrustExp);
        //查询异常的明细
        List<EntrustExpLossRecord> entrustExpLossRecords = entrustExpMapper.selectEntrustExpLossRecordByEntrustExpId(entrustExpId);
        map.put("entrustExpLossRecords", entrustExpLossRecords);

        if(tEntrustExp.getIsCustomerException() == 1) {
            if (StringUtils.isNotEmpty(tEntrustExp.getAppendixId())) {
                List<SysUploadFile> files = uploadFileService.selectSysUploadFileByTid(tEntrustExp.getAppendixId());
                map.put("files", files);
            }
            if (StringUtils.isNotEmpty(tEntrustExp.getAppendixIdInsurance())) {
                List<SysUploadFile> filesInsurance = uploadFileService.selectSysUploadFileByTid(tEntrustExp.getAppendixIdInsurance());
                map.put("filesInsurance", filesInsurance);
            }
            if (StringUtils.isNotEmpty(tEntrustExp.getAppendixIdReceiptImage())) {
                List<SysUploadFile> filesReceipt = uploadFileService.selectSysUploadFileByTid(tEntrustExp.getAppendixIdReceiptImage());
                map.put("filesReceipt", filesReceipt);
            }
            //损失&收入  (通过主异常id exp_id)
            TInsuranceExample tInsuranceExample = new TInsuranceExample();
            tInsuranceExample.createCriteria().andExpIdEqualTo(tEntrustExp.getEntrustExpId());
            List<TInsurance> tInsurances = insuranceMapper.selectByExample(tInsuranceExample);
            List<TInsurance> lossList = new ArrayList<>();
            List<TInsurance> incomneList = new ArrayList<>();
            //将损失和收入分开展示
            for (int i = 0; i < tInsurances.size(); i++) {
                if(tInsurances.get(i).getInsuranceType().equals("1")) {
                    lossList.add(tInsurances.get(i));
                }else {
                    incomneList.add(tInsurances.get(i));
                }
            }

            InsurancePolicy insurancePolicy = insurancePolicyService.selectByPrimaryKey(tEntrustExp.getInsurancePolicyId());
            map.put("insurancePolicy", insurancePolicy);

            map.put("lossList", lossList);
            map.put("incomeList", incomneList);
            //跟踪记录  (通过主异常id exp_id)
            TExceptionTrackExample tExceptionTrackExample = new TExceptionTrackExample();
            tExceptionTrackExample.setOrderByClause("tracking_date");
            tExceptionTrackExample.createCriteria().andExpIdEqualTo(tEntrustExp.getEntrustExpId());
            List<TExceptionTrack> tExceptionTracks = tExceptionTrackMapper.selectByExample(tExceptionTrackExample);
            map.put("exceptionTrackList",tExceptionTracks);
            //残值跟踪记录
            ResidualValueTrackingExample residualValueTrackingExample = new ResidualValueTrackingExample();
            residualValueTrackingExample.setOrderByClause("reg_date");
            residualValueTrackingExample.createCriteria().andExpIdEqualTo(tEntrustExp.getEntrustExpId());
            List<ResidualValueTracking> residualValueTrackings = residualValueTrackingMapper.selectByExample(residualValueTrackingExample);
            map.put("residualValueTrackingList", residualValueTrackings);
            map.put("residualValueTrackingCount", ObjectUtil.isNotEmpty(residualValueTrackings)?residualValueTrackings.size() : 0);
            //查询委托单信息
            Entrust entrust = entrustService.selectEntrustById(tEntrustExp.getEntrustId());
            map.put("entrust", entrust);
            //发货单信息 初始
            Invoice invoiceSearch = new Invoice();
            invoiceSearch.setDelFlag(0);
            invoiceSearch.setInvoiceId(entrust.getOrderno());
            InvoiceReceiveDetailVO invoiceReceiveDetailVO = invoiceMapper.selectInvoiceForException(invoiceSearch);
            map.put("invoiceReceiveDetail", invoiceReceiveDetailVO);
            map.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());
            //查询客户发货单对应的应收明细
            //客户1 -> 发货单1 -> 应收明细1
            //客户发货单一对多  发货单和应收明细一对多  存在赔款异常的发货单
            List<InvoiceReceiveDetailVO> invoiceReceiveDetailVOS = expInvoiceMapper.selectInvoiceException(entrustExpId, entrust.getOrderno());
            map.put("invoiceReceiveDetailList", invoiceReceiveDetailVOS);
            //承运商 -> 运单 -> 应付明细 -> 付款信息
            //运单信息  运单对应的应付信息
            //运单信息
            EntrustLot lotSearch = new EntrustLot();
            lotSearch.setEntrustLotId(entrust.getLotId());
            lotSearch.setDelFlag(0);
            EntrustLotPayDetailPayRecordVO entrustLotPayDetailPayRecordVO = entrustLotMapper.selectLotPayDetailRecordVO(lotSearch);
            map.put("lotPayDetailPayRecord", entrustLotPayDetailPayRecordVO);
            map.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());

            EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrust.getLotId());
            map.put("entrustLot", entrustLot);
            List<CarrierEntrustLotPayDetailPayRecordVO> entrustLotPayDetailPayRecordVOS = tExpEntrustMoreMapper.selectExceptionLotPayDetailRecordList(entrustExpId, entrust.getLotId());
            map.put("lotPayDetailPayRecordList", entrustLotPayDetailPayRecordVOS);
            return  "tms/trace/abnormalEdit_temp";
        }else {
            //查询客户
            ClientPopupVO clientPopupVO = clientService.selectClientById(tEntrustExp.getCustomerId());
            map.put("customer", clientPopupVO);
            //附件
            if (StringUtils.isNotEmpty(tEntrustExp.getAppendixId())) {
                List<SysUploadFile> files = uploadFileService.selectSysUploadFileByTid(tEntrustExp.getAppendixId());
                map.put("files", files);
            }
            //跟踪记录  (通过主异常id exp_id)
            TExceptionTrackExample tExceptionTrackExample = new TExceptionTrackExample();
            tExceptionTrackExample.setOrderByClause("tracking_date");
            tExceptionTrackExample.createCriteria().andExpIdEqualTo(tEntrustExp.getEntrustExpId());
            List<TExceptionTrack> tExceptionTracks = tExceptionTrackMapper.selectByExample(tExceptionTrackExample);
            map.put("exceptionTrackList",tExceptionTracks);
            return "tms/trace/edit_customer_exception";
        }
    }

    /*public static Map mapCombine(List<Map<String, List<EntrustLotAndPaydetail>>> list) {
        Map<Object, List> map = new HashMap<>();
        for (Map m : list) {
            Iterator<Object> it = m.keySet().iterator();
            while (it.hasNext()) {
                Object key = it.next();
                if (!map.containsKey(key)) {
                    List newList = new ArrayList<>();
                    newList.add(m.get(key));
                    map.put(key, newList);
                } else {
                    map.get(key).add(m.get(key));
                }
            }
        }
        return map;
    }*/

    @GetMapping("/residualValueTracking/{expId}")
    public String toResidualValueTracking(ModelMap map, @PathVariable("expId") String expId) {
        map.put("expId", expId);
        List<ResidualValueTracking> residualValueTrackingPage = entrustExceptionMoreService.residualValueTrackingPage(expId);
        map.put("residualValuetrackingList", residualValueTrackingPage);
        return PREFIX + "/residualValueTracking";
    }

    @GetMapping("/residualValueTrackingDetail/{expId}")
    public String toResidualValueTrackingDetail(ModelMap map, @PathVariable("expId") String expId) {
        map.put("expId", expId);
        List<ResidualValueTracking> residualValueTrackingPage = entrustExceptionMoreService.residualValueTrackingPage(expId);
        map.put("residualValuetrackingList", residualValueTrackingPage);
        return PREFIX + "/residualValueTrackingDetail";
    }

    /**
     * 异常跟踪保存
     * @param entrustExp
     * @return
     */
    @RepeatSubmit
    @PostMapping("/saveAbnormal")
    @Log(title = "异常跟踪", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult saveAbnormal(@RequestBody EntrustExp entrustExp) {
        //关账判断
       /* EntrustDto entrust = traceService.selectEntrustWorkById(entrustExp.getEntrustId());
        if ("1".equals(entrust.getIsClose())) {
            return AjaxResult.error("该月委托单已关账！");
        }*/
        return traceService.saveEntrustExp(entrustExp);
    }

    /**
     * 位置跟踪
     *
     * @param map
     * @return
     */
    @RequiresPermissions("tms:trace:location")
    @GetMapping("/place")
    public String place(ModelMap map) {
        return PREFIX + "/placeTracking";
    }

    /**
     * 获取车辆位置点信息
     * @return
     */
    @RequiresPermissions("tms:trace:tacking")
    @GetMapping("/getLocation")
    public String getLocation() {
        return PREFIX + "/tacking_location";
    }

    /**
     * 获取车辆位置点信息
     * @return
     */
    /*    @RequiresPermissions("tms:trace:tacking")*/
    @PostMapping("/getCarLocation")
    @ResponseBody
    public AjaxResult getCarLocation(String carNo) {
        Map<String, Object> locationMap = LocationUtils.getVehicleByCarNo(carNo);
        if(locationMap == null){
            return AjaxResult.error();
        }else{
            Map map = new HashMap();
            BigDecimal lon =  getBigDecimal(locationMap.get("lngitude"));
            BigDecimal lat =  getBigDecimal(locationMap.get("latitude"));
            //根据经纬度获取省市区
            String lonLat = lon.toString()+","+lat.toString();
            //String address = AMapUtils.getAddress(lonLat);
            map.put("lonLat",lonLat);
            map.put("address",locationMap.get("address"));
            map.put("getlocationtime",locationMap.get("getlocationtime"));
            map.put("mile",locationMap.get("mile"));
            map.put("speed",locationMap.get("speed"));

            //保存单击记录
            GetLocationHistory getLocationHistory = new GetLocationHistory();
            getLocationHistory.setId(IdUtil.simpleUUID());
            getLocationHistory.setCarNo(carNo);
            carLocusService.insertLocationHistory(getLocationHistory);

            return AjaxResult.success(map);
        }
    }

    /**
     * 回单
     *
     * @param map
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:receipt","trace:receipt:month:add"}, logical = Logical.OR)
    @GetMapping("/receipt/{entrustIds}")
    public String receipt(ModelMap map, @PathVariable("entrustIds") String entrustIds,@RequestParam String monthFlag
            ,@RequestParam String isRefresh) {
        map.put("isRefresh", "1".equals(isRefresh));

        //委托单ID
        map.put("entrustIds", entrustIds);
        map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片

        //判断委托单号为一个切已回单则跳转详情页面
        if(!entrustIds.contains(",")){

            //获取委托单明细
            EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustIds);
            //获取提货信息 1:提货 2:到货
            List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustIds, "2");
            pickArrivalCommon(entrustWorkList,map);
            if ("1".equals(entrustDto.getIfReceipt())) {
                map.put("entrustDto", entrustDto);
                //查询回单图片
                if(StringUtils.isNotBlank(entrustDto.getReceiptAppendixId())){
                    List<SysUploadFile> uploadFileList = uploadFileService.selectSysUploadFileByTid(entrustDto.getReceiptAppendixId());
                    map.put("uploadFileList", uploadFileList);
                }
                //获取货品明细
                MultipleGoodsConn multipleGoodsConn = new MultipleGoodsConn();
                multipleGoodsConn.setEntrustId(entrustIds);
                List<MultipleGoodsConn> multipleGoodsConns = multipleGoodsConnMapper.selectMultipleGoodsConnList(multipleGoodsConn);
                map.put("entPackGoodsList", multipleGoodsConns);
                return "tms/trace/receiptDetail";
            }
        }


        map.put("receiptMan", shiroUtils.getSysUser().getUserName());
        //保存回单页面
        //所有勾选的委托单号
        String[] entrustIdList = entrustIds.split(",");
        List<Map<String,Object>> infoList = new ArrayList<>();
        for(String entrustId : entrustIdList){
            //存放该委托单的所有信息
            Map<String,Object> entrustInfoMap = new HashMap<>();
            EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
            entrustInfoMap.put("entrustDto", entrustDto);

            //获取货品明细
            MultipleGoodsConn multipleGoodsConn = new MultipleGoodsConn();
            multipleGoodsConn.setEntrustId(entrustId);
            List<MultipleGoodsConn> multipleGoodsConns = multipleGoodsConnMapper.selectMultipleGoodsConnList(multipleGoodsConn);
            entrustInfoMap.put("entPackGoodsList", multipleGoodsConns);
            //获取提货信息 1:提货 2:到货
            List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "2");
            if( entrustWorkList != null && entrustWorkList.size() > 0 ){
                EntrustWork entrustWork = entrustWorkList.get(0);
                entrustInfoMap.put("entrustWork", entrustWork);
                //提货图片
                List<EntrustWorkPic> picList = traceService.selectEntrustWorkPicById(entrustWork.getEntrustWorkId());
                entrustInfoMap.put("picList", picList);
            }else{
                entrustInfoMap.put("entrustWork", new EntrustWork());
                entrustInfoMap.put("picList", new ArrayList<EntrustWorkPic>());
            }
            infoList.add(entrustInfoMap);
        }
        map.put("infoList",infoList);
        if("1".equals(monthFlag)){
            return  "tms/trace/receipt_month_add";
        }else{
            return "tms/trace/receipt";
        }

      /*  //判断是否已回单
        if ("1".equals(entrustDto.getIfReceipt())) {
            return PREFIX + "/receiptDetail";
        } else {
            map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片
            map.put("receiptMan", shiroUtils.getSysUser().getUserName());
            if(entrustDto.getReceiptDate() == null)
                entrustDto.setReceiptDate(new Date());
            //获取提货信息 1:提货 2:到货
            List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "2");
            pickArrivalCommon(entrustWorkList,map);

        }*/

    }


    /**
     * 回单
     *
     * @param map
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @return
     */
    @RequiresPermissions("tms:trace:receipt:confirm")
    @GetMapping("/receiptConfirm/{entrustId}/{isFleetData}/{isFleetAssign}/{isRefresh}")
    public String receiptConfirm(ModelMap map, @PathVariable("entrustId") String entrustId
            ,@PathVariable("isFleetData") String isFleetData,@PathVariable("isFleetAssign") String isFleetAssign
            ,@PathVariable("isRefresh") String isRefresh) {
        map.put("isRefresh", "1".equals(isRefresh));

        //委托单ID
        map.put("entrustId", entrustId);
        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        map.put("entrustDto", entrustDto);
        map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片

        //获取提货信息 1:提货 2:到货
        List<EntrustWork> entrustWorkPickList = traceService.selectEntrustWorkInfoById(entrustId, "1");
        if( entrustWorkPickList != null && entrustWorkPickList.size() > 0 ){
            map.put("entrustPickWork", entrustWorkPickList.get(0));
            //提货图片
            List<EntrustWorkPic> pickPicList = traceService.selectEntrustWorkPicById(entrustWorkPickList.get(0).getEntrustWorkId());
            map.put("pickPicList", pickPicList);
        }else{
            map.put("entrustPickWork", new EntrustWork());
            map.put("pickPicList", new ArrayList<EntrustWorkPic>());
        }
        List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "2");
        if( entrustWorkList != null && entrustWorkList.size() > 0 ){
            EntrustWork entrustWork = entrustWorkList.get(0);
            map.put("entrustWork", entrustWork);
            //到货图片
            List<EntrustWorkPic> picList = traceService.selectEntrustWorkPicById(entrustWork.getEntrustWorkId());
            map.put("picList", picList);
            /*
             * todo: 上传对单照片 APP改好了撤销
             */
            List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid("");
            String tid = "";
            if(!picList.isEmpty()){
                for(EntrustWorkPic entrustWorkPic:picList){
                    if("3".equals(entrustWorkPic.getWorkAppendixType())){
                        sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(entrustWorkPic.getAppendixId());
                        tid = entrustWorkPic.getAppendixId();
                    }
                }
            }
            map.put("sysUploadFiles",sysUploadFiles);
            map.put("tid",tid);

            //判断是否要回单且是否有到货作业上传回单照片
            //3为中转站不需要回单
            if("3".equals(entrustDto.getAddrType())){
                map.put("receiptErrorFlag","0");
            }else{
                //判断是否存在回单照片
                String receiptErrorFlag = "1";
                for(EntrustWorkPic entrustWorkPic : picList){
                    if(EntrustPicEnum.CERTPIC.getValue().equals(entrustWorkPic.getWorkAppendixType())){
                        receiptErrorFlag = "0";
                    }
                }
                map.put("receiptErrorFlag",receiptErrorFlag);
            }
        }else{
            map.put("entrustWork", new EntrustWork());
            map.put("picList", new ArrayList<EntrustWorkPic>());
            if("3".equals(entrustDto.getAddrType())){
                map.put("receiptErrorFlag","0");
            }else{
                map.put("receiptErrorFlag","1");
            }
        }
        //获取货品明细
        MultipleGoodsConn multipleGoodsConn = new MultipleGoodsConn();
        multipleGoodsConn.setEntrustId(entrustId);
        List<MultipleGoodsConn> multipleGoodsConns = multipleGoodsConnMapper.selectMultipleGoodsConnList(multipleGoodsConn);
        map.put("entPackGoodsList", multipleGoodsConns);

        //查询应付
        List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrustDto.getLotId());
        map.put("payDetailList", payDetailList);
        //查询应收
        List<ReceiveDetailVO> receiveDetailList = receiveDetailService.selectReceiveByInvoiceId(entrustDto.getOrderno());
        map.put("receiveDetailList", receiveDetailList);
        map.put("isFleetData",isFleetData);
        map.put("isFleetAssign",isFleetAssign);
        return "tms/trace/receiptConfirm";
    }


    /**
     * 回单
     *
     * @param map
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @return
     */
    @RequiresPermissions("tms:trace:receipt:confirm")
    @GetMapping("/receiptConfirmMultiple/{entrustIds}/{isFleetData}/{isFleetAssign}/{isRefresh}")
    public String receiptConfirmMultiple(ModelMap map, @PathVariable("entrustIds") String entrustIds
            ,@PathVariable("isFleetData") String isFleetData,@PathVariable("isFleetAssign") String isFleetAssign
            ,@PathVariable("isRefresh") String isRefresh) {
        map.put("isRefresh", "1".equals(isRefresh));

        //委托单ID
        map.put("entrustIds", entrustIds);

        List<PayDetail> payDetailList = new ArrayList<PayDetail>();
        List<ReceiveDetailVO> receiveDetailList = new ArrayList<>();
        String[] split = entrustIds.split(",");
        Set<String> lotIdSet = new HashSet<>();
        Set<String> invoiceIdSet = new HashSet<>();
        for(String entrustId : split){
            //获取委托单明细
            EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
            lotIdSet.add(entrustDto.getLotId());
            invoiceIdSet.add(entrustDto.getOrderno());
        }

        for(String lotId : lotIdSet){
            //查询应付
            List<PayDetail> payDetailListTwo = payDetailService.selectPayDetailListByLotId(lotId);
            payDetailList.addAll(payDetailListTwo);
        }

        for(String invoiceId : invoiceIdSet){
            //查询应收
            List<ReceiveDetailVO> receiveDetailListTwo = receiveDetailService.selectReceiveByInvoiceId(invoiceId);
            receiveDetailList.addAll(receiveDetailListTwo);
        }

        map.put("payDetailList", payDetailList);
        map.put("receiveDetailList", receiveDetailList);

        map.put("isFleetData",isFleetData);
        map.put("isFleetAssign",isFleetAssign);
        return "tms/trace/receiptConfirmMutiple";
    }

    /**
     * 回单保存
     *
     * @param entrustGoodsDtoList
     * @return
     */
    @RepeatSubmit
    @PostMapping("/addReceipt")
    @Log(title = "回单", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addReceipt(@RequestBody ArrayList<EntrustGoodsDto> entrustGoodsDtoList) {
        //关账判断
        /*for(EntrustGoodsDto entrustGoodsDto : entrustGoodsDtoList){
            EntrustDto entrust = traceService.selectEntrustWorkById(entrustGoodsDto.getEntrustId());
            if ("1".equals(entrust.getIsClose())) {
                return AjaxResult.error("该月委托单已关账！");
            }
        }*/
        return traceService.saveEntrustGoods(entrustGoodsDtoList);
    }

    /**
     * 回单确认保存
     *
     * @param entrustGoodsDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/addReceiptConfirm")
    @Log(title = "回单确认", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addReceiptConfirm(@RequestBody EntrustGoodsDto entrustGoodsDto) {
        EntrustDto entrust = traceService.selectEntrustWorkById(entrustGoodsDto.getEntrustId());
        /*if ("1".equals(entrust.getIsClose())) {
            return AjaxResult.error("该月委托单已关账！");
        }*/
        List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrust.getLotId());
        for(PayDetail payDetail:payDetailList){
            if(PayDetailStatusEnum.NEW.getValue() == payDetail.getVbillstatus()){
                return AjaxResult.error("应付单号：【"+payDetail.getVbillno()+"】为新建状态，不能进行回单确认");
            }
        }
        return traceService.addReceiptConfirm(entrustGoodsDto,false);
    }

    /**
     * 回单确认保存
     *
     * @param entrustGoodsDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/addReceiptConfirmMutiple")
    @Log(title = "回单确认", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addReceiptConfirmMutiple(@RequestBody EntrustGoodsDto entrustGoodsDto) {
        String entrustIds = entrustGoodsDto.getEntrustIds();
        String[] split = entrustIds.split(",");
        for(String entrustId :split){
            EntrustDto entrust = traceService.selectEntrustWorkById(entrustId);

            List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrust.getLotId());

            List<String> list = new ArrayList<>();
            for(PayDetail payDetail:payDetailList){
                //更新为已确认
                if(PayDetailStatusEnum.NEW.getValue() == payDetail.getVbillstatus()){
                    list.add(payDetail.getPayDetailId());
                }
            }
            if(list.size() > 0){
                payDetailService.updatePayDetailStatusByList(list);
            }

            EntrustGoodsDto dto = new EntrustGoodsDto();
            dto.setEntrustId(entrustId);

            //获取货品明细
            EntPackGoods entPackGoods = new EntPackGoods();
            entPackGoods.setEntrustId(entrustId);
            entPackGoods.setDelFlag(0);
            List<EntPackGoods> entPackGoodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
            for(EntPackGoods goods : entPackGoodsList){
                goods.setReceiptNum(goods.getNum());
                goods.setReceiptWeight(goods.getWeight());
                goods.setReceiptVolume(goods.getVolume());
                goods.setGoodsId(goods.getEntPackGoodsId());
            }
            dto.setGoodsList(entPackGoodsList);

            traceService.addReceiptConfirm(dto,false);
        }
        return AjaxResult.success();
    }



    /**
     * 保存回单照片
     *
     * @param entrustGoodsDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/savePic")
    @ResponseBody
    public AjaxResult savePic(EntrustGoodsDto entrustGoodsDto) {
        return traceService.savePic(entrustGoodsDto);
    }

    @RepeatSubmit
    @PostMapping("/confirmAll")
    @ResponseBody
    public AjaxResult confirmAll(EntrustGoodsDto entrustGoodsDto) {
        return traceService.confirmAll(entrustGoodsDto);
    }

    /**
     * 删除回单照片
     *
     * @param entrustGoodsDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/removePic")
    @ResponseBody
    public AjaxResult removePic(EntrustGoodsDto entrustGoodsDto) {
        return traceService.removePic(entrustGoodsDto);
    }



    /**
     * 费用登记
     *
     * @param map
     * @param entrustIds    委托单id
     * @param isRefresh     保存完是否刷新
     * @return
     */
    @RequiresPermissions("tms:trace:register")
    @GetMapping("/register/{entrustIds}/{isRefresh}")
    public String register(ModelMap map, @PathVariable("entrustIds") String entrustIds
            ,@PathVariable("isRefresh") String isRefresh) {
        map.put("entrustIds", entrustIds);//委托单ID多条addRegister

        map.put("isRefresh", "1".equals(isRefresh));

        //查询勾选的委托单
        List<EntrustDto> entrustDtoList = traceService.selectEntrustWorkByIdList(entrustIds);//获取委托单明细
        map.put("entrustDtoList", entrustDtoList);
        map.put("fleetsign",entrustDtoList.get(0).getIsFleetAssign());

        //第一个发货单信息
        //Invoice invoiceOne = invoiceMapper.selectInvoiceById(entrustDtoList.get(0).getOrderno());
        //map.put("billingType", invoiceOne.getBillingType());

        //查询运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDtoList.get(0).getLotId());
        map.put("entrustLot", entrustLot);
        String lotId = entrustLot.getEntrustLotId();
        //查询委托单对应运单下司机信息
       /* EntrustLotCarDriver entrustLotCarDriver = new EntrustLotCarDriver();
        entrustLotCarDriver.setLotId(entrustLot.getEntrustLotId());
        List<EntrustLotCarDriver> entrustLotCarDriverList = entrustLotCarDriverService.selectEntrustLotCarDriverList(entrustLotCarDriver);
        //运单下对应司机ID，司机名称循环拼接
        String driverIds = "";
        String driverNames = "";
        String driverPhones = "";
        for (int i = 0; i < entrustLotCarDriverList.size(); i++) {
            EntrustLotCarDriver lotCarDriver = entrustLotCarDriverList.get(i);
            driverIds += lotCarDriver.getDriverId();
            driverNames += lotCarDriver.getDriverName();
            if(StringUtils.isNotEmpty(lotCarDriver.getDriverId())){
                Driver driver = driverService.selectDriverById(lotCarDriver.getDriverId());
                driverPhones += driver.getPhone();
                //除了最后拼接逗号
                if (i != entrustLotCarDriverList.size() - 1) {
                    driverIds += ",";
                    driverNames += ",";
                    driverPhones += ",";
                }
            }
        }
        map.put("driverIds", driverIds);
        map.put("driverNames", driverNames);
        map.put("driverPhones", driverPhones);*/
        //查询是否存在费用登记信息(在途费用记录)  根据委托单id和IS_LOT_FEE(是否运费)
        List<EntrustCost> entrustCostList = traceService.selectEntrustCostById(entrustIds);
        map.put("entrustCostList", entrustCostList);
        // 计算提货送货费 总价
        BigDecimal sumCost = entrustCostList.stream().filter(x -> "1".equals(x.getBudgetType())
                && ("2".equals(x.getCostType()) || "22".equals(x.getCostType())))
                .map(EntrustCost::getCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("sumCost", sumCost);
        //查询是否存在运费等级信息(运费记录)  根据运单id和is_lot_fee = 1
/*        List<EntrustCost> entrustCostLotList = traceService.entrustCostLotListByLotId(entrustLot.getEntrustLotId());
        map.put("entrustCostLotList", entrustCostLotList);*/
        //费用类型
        //map.put("freeTypeList", FreeTypeEnum.getAllToMap());
        //查询所有运单下的委托单，用于费用登记
        List<EntrustDto> entrustDtoByLot = traceService.selectEntrustWorkByLotId(entrustLot.getEntrustLotId());
        map.put("entrustDtoByLot", entrustDtoByLot);

        /**
         * 2021-11-16 Zhangf 修改，查询发货单的总成本
         */
        //获取委托单对应的发货单号，去重
        String[] invoiceVbillnos = entrustDtoByLot.stream().map(EntrustDto::getInvoiceVbillno).distinct().toArray(String[]::new);
        //发货单信息
        List<Invoice> invoiceList = invoiceMapper.selectInvoiceByInvoiceVbillnos(invoiceVbillnos);
        map.put("invoiceList", invoiceList);

        map.put("customerId",invoiceList.get(0).getCustomerId());

        List<EntrustDto> allEntrustDtoList = new ArrayList<>();

        List<Map<String,Object>> netList = new ArrayList<>();

        //根据发货单查询运单
        for(Invoice invoice : invoiceList){
            List<EntrustDto> entrustDtos = traceMapper.selectAllEntrustByInvoiceId(invoice.getInvoiceId());
            allEntrustDtoList.addAll(entrustDtos);

            ReceivableReconciliationDTO receivableReconciliation = new ReceivableReconciliationDTO();
            receivableReconciliation.setVbillno(invoiceList.get(0).getVbillno());
            List<ReceivableReconciliationVO> list = receivableReconciliationMapper.listReceivableReconciliation(receivableReconciliation);
            Map<String,Object> netMap = new HashMap<>();
            netMap.put("invoiceId",invoice.getInvoiceId());
            netMap.put("invoiceVbillno",invoice.getVbillno());
            netMap.put("billingType",invoice.getBillingType());
            if(list != null && list.size() == 1){
                BigDecimal netProfits = list.get(0).getNetProfits();
                netMap.put("netProfits", netProfits);
                netMap.put("profitsFlag", 1);
            }else{
                netMap.put("profitsFlag", 0);
            }
            netList.add(netMap);
        }
        map.put("netList",netList);

        //现金计算税率
        String cash_tax_rate = configService.selectConfigByKey("cash_tax_rate");
        map.put("cash_tax_rate", cash_tax_rate);

        String[] entrustLotIds = allEntrustDtoList.stream().map(EntrustDto::getLotId).distinct().toArray(String[]::new);
        List<EntrustLot> entrustLotList = entrustLotMapper.selectEntrustLotByIds(entrustLotIds);
        map.put("entrustLotList", entrustLotList);

        //发货单总金额
        BigDecimal sum = invoiceList.stream().map(Invoice::getCostAmount).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //发货单金额 与 总金额 的占比  key：发货单号  v：比率
        Map<String, BigDecimal> invoiceRate = invoiceList.stream().filter(x -> x.getCostAmount() != null)
                .collect(Collectors.toMap(Invoice::getVbillno, x -> sum.compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : NumberUtil.div(x.getCostAmount(), sum)));

        /*
         * 计算后的 委托单分摊占比  key：委托单id  v：比率
         */
        Map<String, BigDecimal> entrustRate = new HashMap<>();
        for (EntrustDto entrust : entrustDtoByLot) {
            //发货单相同的委托单个数
            long count = entrustDtoByLot.stream().filter(x -> x.getInvoiceVbillno().equals(entrust.getInvoiceVbillno())).count();

            //所属发货单比率/同一发货单个数 = 该委托单金额比
            BigDecimal bigDecimal = invoiceRate.get(entrust.getInvoiceVbillno());

            BigDecimal div = BigDecimal.valueOf(0);
            //不为空则计算
            if (bigDecimal != null) {
                div = NumberUtil.div(bigDecimal, count);
            }
            entrustRate.put(entrust.getEntrustId(), div);
        }
        map.put("rateMap",entrustRate);

        //2021-11-23 Zhangf 费用登记->费用记录
       /* BigDecimal sumReceive = BigDecimal.ZERO;
        BigDecimal sumReceive1 = BigDecimal.ZERO;

        BigDecimal sumPay     = BigDecimal.ZERO;
        BigDecimal sumPay1    = BigDecimal.ZERO;
        BigDecimal sumPay1Exp = BigDecimal.ZERO;



        int receive0Cnt = 1;
        int receive1Cnt = 1;

        int pay0Cnt = 1;
        int pay1Cnt = 1;
        int pay1ExpCnt = 1;
        int payCnt = 3;*/

        //应收--运费
        //List<ReceiveDetail> receiveDetailList = traceService.sumReceiveDetailList(invoiceVbillnos);
        /*if(null ==receiveDetailList || 0 == receiveDetailList.size() ){
            receiveDetailList = new ArrayList<>();
            ReceiveDetail tmp = new ReceiveDetail();
            tmp.setTransFeeCount(BigDecimal.ZERO);
            receiveDetailList.add(tmp);
        }else{
            sumReceive = receiveDetailList.stream().map(ReceiveDetail::getTransFeeCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            receive0Cnt = receiveDetailList.size();
        }*/
        //map.put("receiveDetailList",receiveDetailList);
        //map.put("receive0Cnt",receive0Cnt);

        //应收--在途
        //List<ReceiveDetail> receiveDetailList1 = traceService.sumReceiveDetailList1(invoiceVbillnos);
        /*if(null ==receiveDetailList1 || 0 == receiveDetailList1.size() ){
            receiveDetailList1 = new ArrayList<>();
            ReceiveDetail tmp = new ReceiveDetail();
            tmp.setTransFeeCount(BigDecimal.ZERO);
            receiveDetailList1.add(tmp);
        }else{
            sumReceive1 = receiveDetailList1.stream().map(ReceiveDetail::getTransFeeCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            receive1Cnt = receiveDetailList1.size();
        }
        map.put("receiveDetailList1",receiveDetailList1);
        map.put("receive1Cnt",receive1Cnt);
        map.put("receiveCnt",receive0Cnt+receive1Cnt);
        sumReceive = sumReceive.add(sumReceive1);*/

        //应付--运费
        /*List<PayDetail> payDetailList = traceService.sumPayDetailList(lotId);
        if(null == payDetailList || 0 == payDetailList.size()){
            payDetailList = new ArrayList<>();
            PayDetail tmp = new PayDetail();
            tmp.setTransFeeCount(BigDecimal.ZERO);
            payDetailList.add(tmp);
        }else{
            sumPay = payDetailList.stream().map(PayDetail::getTransFeeCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            pay0Cnt = payDetailList.size();
        }
        map.put("pay0Cnt",pay0Cnt);
        map.put("payDetailList0",payDetailList);*/
        //应付--在途--在途费用
       /* List<PayDetail> payDetailList1 = traceService.sumPayDetailList1(lotId);
        if(null == payDetailList1 || 0 == payDetailList1.size()){
            payDetailList1 = new ArrayList<>();
            PayDetail tmp = new PayDetail();
            tmp.setTransFeeCount(BigDecimal.ZERO);
            payDetailList1.add(tmp);
        }else{
            sumPay1 = payDetailList1.stream().map(PayDetail::getTransFeeCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            pay1Cnt = payDetailList1.size();
        }
        map.put("pay1Cnt",pay1Cnt);
        map.put("payDetailList1",payDetailList1);*/

        //应付--在途--异常费用
        /*List<PayDetail> payDetailList1Exp = traceService.sumPayDetailList1Exp(lotId);
        if(null == payDetailList1Exp || 0 == payDetailList1Exp.size()){
            payDetailList1Exp = new ArrayList<>();
            PayDetail tmp = new PayDetail();
            tmp.setTransFeeCount(BigDecimal.ZERO);
            payDetailList1Exp.add(tmp);
        }else{
            sumPay1Exp = payDetailList1Exp.stream().map(PayDetail::getTransFeeCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            pay1ExpCnt = payDetailList1Exp.size();
        }
        map.put("pay1ExpCnt",pay1ExpCnt);
        map.put("payDetailList1Exp",payDetailList1Exp);
        map.put("payCnt",pay0Cnt+pay1Cnt+pay1ExpCnt);*/


        //List<OtherFee> otherFeeList = new ArrayList<>();
        //其他费用
       /* for(Invoice invoice : invoiceList){
            otherFeeList.addAll(traceService.sumOtherFeeList(invoice.getInvoiceId()));
        }

        BigDecimal sumOther = otherFeeList.stream().map(OtherFee::getFeeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);

        map.put("otherFeeList",otherFeeList);
        map.put("sumOther",sumOther);*/

        //毛利
        /*BigDecimal benefit = sumReceive.subtract(sumPay).subtract(sumPay1).subtract(sumPay1Exp).subtract(sumOther);
        map.put("benefit",benefit);*/

        //获取异常费用最大限制
        String exception_money_max = sysConfigService.selectConfigByKey("exception_money_max");
        map.put("exception_money_max",exception_money_max);

        // 计价方式 List
        //map.put("pricingMethodEnumList", CarrierProtocolPricingMethodEnum.toMap());
        return PREFIX + "/register";
    }


    /**
     * 费用登记
     *
     * @param map
     * @param entrustIds    委托单id
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:register","tms:fleet:trace:register"},logical = Logical.OR)
    @GetMapping("/adjust/{entrustIds}")
    public String adjust(ModelMap map, @PathVariable("entrustIds") String entrustIds) {
        map.put("entrustIds", entrustIds);//委托单ID多条addRegister

        //查询勾选的委托单
        List<EntrustDto> entrustDtoList = traceService.selectEntrustWorkByIdList(entrustIds);//获取委托单明细
        map.put("entrustDtoList", entrustDtoList);
        map.put("fleetsign",entrustDtoList.get(0).getIsFleetAssign());
        //查询运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDtoList.get(0).getLotId());
        map.put("entrustLot", entrustLot);
        String lotId = entrustLot.getEntrustLotId();
        //查询委托单对应运单下司机信息
        EntrustLotCarDriver entrustLotCarDriver = new EntrustLotCarDriver();
        entrustLotCarDriver.setLotId(entrustLot.getEntrustLotId());
        List<EntrustLotCarDriver> entrustLotCarDriverList = entrustLotCarDriverService.selectEntrustLotCarDriverList(entrustLotCarDriver);
        //运单下对应司机ID，司机名称循环拼接
        String driverIds = "";
        String driverNames = "";
        String driverPhones = "";
        for (int i = 0; i < entrustLotCarDriverList.size(); i++) {
            EntrustLotCarDriver lotCarDriver = entrustLotCarDriverList.get(i);
            driverIds += lotCarDriver.getDriverId();
            driverNames += lotCarDriver.getDriverName();
            if(StringUtils.isNotEmpty(lotCarDriver.getDriverId())){
                Driver driver = driverService.selectDriverById(lotCarDriver.getDriverId());
                driverPhones += driver.getPhone();
                //除了最后拼接逗号
                if (i != entrustLotCarDriverList.size() - 1) {
                    driverIds += ",";
                    driverNames += ",";
                    driverPhones += ",";
                }
            }

        }
        map.put("driverIds", driverIds);
        map.put("driverNames", entrustLot.getDriverName());
        map.put("driverPhones", driverPhones);
        //查询是否存在费用登记信息(在途费用记录)  根据委托单id和IS_LOT_FEE(是否运费)
        List<EntrustCost> entrustCostList = traceService.selectEntrustCostById(entrustIds);
        map.put("entrustCostList", entrustCostList);
        // 计算提货送货费 总价
        BigDecimal sumCost = entrustCostList.stream().filter(x -> "1".equals(x.getBudgetType())
                && ("2".equals(x.getCostType()) || "22".equals(x.getCostType())))
                .map(EntrustCost::getCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("sumCost", sumCost);
        //查询是否存在运费等级信息(运费记录)  根据运单id和is_lot_fee = 1
        List<EntrustCost> entrustCostLotList = traceService.entrustCostLotListByLotId(entrustLot.getEntrustLotId());
        map.put("entrustCostLotList", entrustCostLotList);
        //费用类型
        map.put("freeTypeList", FreeTypeEnum.getAllToMap());
        //查询所有运单下的委托单，用于费用登记
        List<EntrustDto> entrustDtoByLot = traceService.selectEntrustWorkByLotId(entrustLot.getEntrustLotId());
        map.put("entrustDtoByLot", entrustDtoByLot);

        /**
         * 2021-11-16 Zhangf 修改，查询发货单的总成本
         */
        //获取委托单对应的发货单号，去重
        String[] invoiceVbillnos = entrustDtoByLot.stream().map(EntrustDto::getInvoiceVbillno).distinct().toArray(String[]::new);
        //发货单信息
        List<Invoice> invoiceList = invoiceMapper.selectInvoiceByInvoiceVbillnos(invoiceVbillnos);
        map.put("invoiceList", invoiceList);
        //发货单总金额
        BigDecimal sum = invoiceList.stream().map(Invoice::getCostAmount).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //发货单金额 与 总金额 的占比  key：发货单号  v：比率
        Map<String, BigDecimal> invoiceRate = invoiceList.stream().filter(x -> x.getCostAmount() != null)
                .collect(Collectors.toMap(Invoice::getVbillno, x -> sum.compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : NumberUtil.div(x.getCostAmount(), sum)));

        /*
         * 计算后的 委托单分摊占比  key：委托单id  v：比率
         */
        Map<String, BigDecimal> entrustRate = new HashMap<>();
        for (EntrustDto entrust : entrustDtoByLot) {
            //发货单相同的委托单个数
            long count = entrustDtoByLot.stream().filter(x -> x.getInvoiceVbillno().equals(entrust.getInvoiceVbillno())).count();

            //所属发货单比率/同一发货单个数 = 该委托单金额比
            BigDecimal bigDecimal = invoiceRate.get(entrust.getInvoiceVbillno());

            BigDecimal div = BigDecimal.valueOf(0);
            //不为空则计算
            if (bigDecimal != null) {
                div = NumberUtil.div(bigDecimal, count);
            }
            entrustRate.put(entrust.getEntrustId(), div);
        }
        map.put("rateMap",entrustRate);


        //应收 方便前端根据发货单号展示，拆分查询
        List<Map<String,Object>> receiveDetailList = new ArrayList<>();
        for(String invoiceVbillno : invoiceVbillnos) {
            String[] ids = new String[1];
            ids[0] = invoiceVbillno;
            List<ReceiveDetail> receiveDetails = traceService.sumReceiveDetailListByInvoiceIds(ids);
            Map<String,Object> maps = new HashMap<>();
            maps.put("invoiceVbillno",invoiceVbillno);
            maps.put("receiveDetails",receiveDetails);
            Invoice invoice = invoiceMapper.selectInvoiceByInvoiceNo(invoiceVbillno);
            maps.put("invoiceLotId",invoice.getInvoiceId());
            //查询是否有待审核
            Integer cnt = payDetailMapper.selectReceiveDetailUncheckCntByInvoiceNo(invoice.getVbillno());
            maps.put("waitCheckCnt",cnt);

            //查询最近审核记录
            AdjustRecordVO adjustRecord = payDetailMapper.selectReceiveDetailUncheckInfoByInvoiceNo(invoice.getVbillno());
            maps.put("adjustRecord",adjustRecord);

            receiveDetailList.add(maps);

        }
        map.put("receiveDetailList",receiveDetailList);

        //应付
        List<PayDetail> payDetailList = traceMapper.sumPayDetailListByLotId(lotId);
        map.put("payDetailList",payDetailList);
        //查询应付审核记录
        //验证是否存在待审核的运单信息
        Integer payDetailCnt = payDetailMapper.selectPayDetailUncheckCntByLot(entrustLot.getLot());
        map.put("waitCheckCnt",payDetailCnt);

        AdjustRecordVO payDetailAdjustRecord = payDetailMapper.selectPayDetailUncheckInfoByLot(entrustLot.getLot());
        map.put("adjustRecord",payDetailAdjustRecord);


        //第三方 方便前端根据发货单号展示，拆分查询
        List<Map<String,Object>> otherFeeList = new ArrayList<>();
        for(String invoiceVbillno : invoiceVbillnos) {
            String[] ids = new String[1];
            ids[0] = invoiceVbillno;
            List<OtherFee> otherFees = traceMapper.sumOtherFeeListByInvoiceVbillno(ids);
            Map<String,Object> maps = new HashMap<>();
            maps.put("invoiceVbillno",invoiceVbillno);
            maps.put("otherFees",otherFees);

            Invoice invoice = invoiceMapper.selectInvoiceByInvoiceNo(invoiceVbillno);
            maps.put("invoiceLotId",invoice.getInvoiceId());

            //验证是否存在待审核的运单信息
            Integer cnt = payDetailMapper.selectOtherFeeUncheckCntByInvoiceNo(invoice.getVbillno());
            maps.put("waitCheckCnt",cnt);

            //查询审核记录
            AdjustRecordVO adjustRecord = payDetailMapper.selectOtherFeeUncheckInfoByInvoiceNo(invoice.getVbillno());
            maps.put("adjustRecord",adjustRecord);
            otherFeeList.add(maps);
        }
        map.put("otherFeeList",otherFeeList);

        return PREFIX + "/adjust";
    }

    /**
     * 费用保存
     *
     * @param addRegisterDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/addRegister")
    @Log(title = "费用登记", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addRegister(@RequestBody AddRegisterDto addRegisterDto) {
        List<EntrustCost> params = addRegisterDto.getParams();
        for (EntrustCost entrustCost : params) {
            //附件id
            entrustCost.setAppendixId(addRegisterDto.getAppendixId());

            EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(entrustCost.getEntrustLotId());
            entrustCost.setLot(entrustLot.getLot());

            //获取委托单
            List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceIdAndLotId(entrustCost.getInvoiceId(), entrustCost.getEntrustLotId());
            entrustCost.setEntrustId(entrusts.get(0).getEntrustId());
            entrustCost.setEntrustNo(entrusts.get(0).getVbillno());

            //发货单号
            Invoice invoice = invoiceMapper.selectInvoiceById(entrustCost.getInvoiceId());
            entrustCost.setInvoiceVbillno(invoice.getVbillno());

            //在途
            entrustCost.setIsLotFee(0);
        }

        List<OtherFee> otherFeeList = addRegisterDto.getOtherFeeList();

        for (OtherFee otherFee : otherFeeList) {
            EntrustCost entrustCost = new EntrustCost();
            //附件id
            entrustCost.setAppendixId(addRegisterDto.getAppendixId());

            entrustCost.setEntrustId(addRegisterDto.getEntrustId());
            entrustCost.setEntrustNo(addRegisterDto.getEntrustNo());
            entrustCost.setEntrustLotId(addRegisterDto.getEntrustLotId());
            entrustCost.setLot(addRegisterDto.getLot());
            entrustCost.setBillingType(otherFee.getBillingType());

            //1第三方费用
            entrustCost.setIsLotFee(1);
            //发货单id
            entrustCost.setInvoiceId(otherFee.getLotId());

            //发货单号
            Invoice invoice = invoiceMapper.selectInvoiceById(otherFee.getLotId());
            entrustCost.setInvoiceVbillno(invoice.getVbillno());

            //总金额
            entrustCost.setFeeAmount(otherFee.getFeeAmount());
            //费用类型
            entrustCost.setFeeType(otherFee.getFeeType());
            //备注
            entrustCost.setMemo(otherFee.getMemo());
            entrustCost.setRecBank(otherFee.getRecBank());
            entrustCost.setRecAccount(otherFee.getRecAccount());
            entrustCost.setRecCardNo(otherFee.getRecCardNo());
            //单据状态
            entrustCost.setSingleFlag(otherFee.getSingleFlag());

            params.add(entrustCost);
        }

        return traceService.saveEntrustCostAudit(params);

//        return traceService.saveEntrustCost(params,addRegisterDto.getOtherFeeList(),true);
    }

    /**
     * 根据entrustCostId 查询 审核记录列表
     *
     * @param
     * @return
     */
    @PostMapping("/entrust_cost_check/list")
    @ResponseBody
    public List<EntrustCostCheckDetail> list(String entrustCostId) {
        List<EntrustCostCheckDetail> list = entrustCostService.selectCheckList(entrustCostId);
        list.sort(Comparator.comparing(EntrustCostCheckDetail::getCheckDate));
        return list;
    }


    /**
     * 跟踪
     *
     * @param map
     * @param entrustId     委托单id
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @return
     */
    @RequiresPermissions("tms:trace:tacking")
    @GetMapping("/tacking/{entrustId}/{isRefresh}/{vbillstatus}")
    public String tacking(ModelMap map, @PathVariable("entrustId") String entrustId,@PathVariable("isRefresh") String isRefresh, @PathVariable("vbillstatus")String vbillstatus) {
        map.put("isRefresh", "1".equals(isRefresh));
        map.put("vbillstatus", vbillstatus);

        //获取车辆轨迹
        List<CarLocus> carLocusList = carLocusService.selectCarLocusByEntrustId(entrustId);
        map.put("carLocusList", carLocusList);
        //车辆信息
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        String carId = StringUtils.isNotBlank(entrust.getCarnoId()) ? entrust.getCarnoId() : "-1";
        Car car = carService.selectCarById(carId);
        if(car == null){
            car = new Car();
        }
        map.put("car", car);
        //委托单ID
        map.put("entrustId", entrustId);

        String carno = StringUtils.isNotBlank(car.getCarno()) ? car.getCarno():"-1";
        //查看车辆当前定位
        //Map<String, Object> locationMap = LocationUtils.getVehicleByCarNo(carno);
        //if(locationMap == null ){
        map.put("lonLat","");
        map.put("address","");
        /*}else{
            BigDecimal lon =  getBigDecimal(locationMap.get("lngitude"));
            BigDecimal lat =  getBigDecimal(locationMap.get("latitude"));
            //根据经纬度获取省市区
            String lonLat = lon.toString()+","+lat.toString();
            String address = AMapUtils.getAddress(lonLat);
            map.put("lonLat",lonLat);
            map.put("address",address);
        }*/
        return "tms/trace/tacking";
    }

    /**
     * 追踪记录详情
     * @return
     */
    @RequiresPermissions("tms:trace:tacking")
    @GetMapping("/tacking/detail/{id}")
    public String tackingDetail(@PathVariable String id, ModelMap map) {
        CarLocus carLocus = carLocusService.selectCarLocusById(id);
        map.put("carLocus", carLocus);
        //获取图片
        String tid = carLocus.getAppendixId() == null ? "" : carLocus.getAppendixId();
        List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(tid);
        map.put("sysUploadFiles", sysUploadFiles);

        return PREFIX + "/tacking_detail";
    }

    /**
     * 跟踪保存
     *
     * @param
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions("tms:trace:tacking")
    @Log(title = "跟踪", businessType = BusinessType.INSERT)
    @PostMapping("/saveTacking")
    @ResponseBody
    public AjaxResult saveTacking(CarLocus carLocus,@RequestParam Map<String,String> params) {
        //关账判断
//        EntrustDto entrust = traceService.selectEntrustWorkById(carLocus.getEntrustId());
//        if ("1".equals(entrust.getIsClose())) {
//            return AjaxResult.error("该月委托单已关账！");
//        }
        return carLocusService.saveCarLocus(carLocus,params);
    }

    /**
     * 提货
     *
     * @param map
     * @param entrustId     委托单id
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @param openType      打开方式         tab新开页面   其余为弹窗
     * @return
     */
    @RequiresPermissions(value ={"tms:trace:pick","carrier:entrust:pick"},logical = Logical.OR)
    @GetMapping("/pick/{entrustId}")
    public String pick(ModelMap map, @PathVariable("entrustId") String entrustId
            ,@RequestParam("isRefresh") String isRefresh,@RequestParam("openType") String openType) {
        map.put("isRefresh", "1".equals(isRefresh));
        map.put("openTab", "tab".equals(openType));


        //获取货品明细
        EntPackGoods entPackGoods = new EntPackGoods();
        entPackGoods.setEntrustId(entrustId);
        entPackGoods.setDelFlag(0);
        List<EntPackGoods> entPackGoodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
        map.put("entPackGoodsList", entPackGoodsList);//货品

        //货品合计
        Double numSum = 0d;
        Double weightSum = 0d;
        Double volumeSum = 0d;
        for(EntPackGoods entPackGoods1 : entPackGoodsList){
            numSum += entPackGoods1.getNum();
            weightSum += entPackGoods1.getWeight();
            volumeSum += entPackGoods1.getVolume();
        }
        map.put("numSum", numSum);
        map.put("weightSum", weightSum);
        map.put("volumeSum", volumeSum);


        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        map.put("entrustDto", entrustDto);//货品
        //要求提货日期
        map.put("reqDeliDate", DateFormatUtils.format(entrustDto.getReqDeliDate(),"yyyy-MM-dd"));
        map.put("entrustId", entrustId);//委托单ID
        //查询委托单对应运单下司机信息
        //2022-08-19 Zhangf修改，运单司机只有一个，已经保存在运单表中了
        /*EntrustLotCarDriver entrustLotCarDriver = new EntrustLotCarDriver();
        entrustLotCarDriver.setLotId(entrustDto.getLotId());
        List<EntrustLotCarDriver> entrustLotCarDriverList = entrustLotCarDriverService.selectEntrustLotCarDriverList(entrustLotCarDriver);
        //运单下对应司机ID，司机名称循环拼接
        String driverIds = "";
        String driverNames = "";
        for (int i = 0; i < entrustLotCarDriverList.size(); i++) {
            EntrustLotCarDriver lotCarDriver = entrustLotCarDriverList.get(i);
            driverIds += lotCarDriver.getDriverId();
            driverNames += lotCarDriver.getDriverName();
            //除了最后拼接逗号
            if (i != entrustLotCarDriverList.size() - 1) {
                driverIds += ",";
                driverNames += ",";
            }
        }*/

        //获取运单下运费信息
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustDto.getLotId());
        payDetail.setDelFlag(0);
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetail);
        map.put("payDetailList", payDetailList);
        //计算合计
        BigDecimal payDetailTransFeeSum = payDetailList.stream().map(PayDetail::getTransFeeCount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal payDetailGotSum = payDetailList.stream().map(PayDetail::getGotAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        map.put("payDetailTransFeeSum", payDetailTransFeeSum);
        map.put("payDetailGotSum", payDetailGotSum);

        //应付单状态
        List<Map<String, Object>> payDetailStatusEnum  = PayDetailStatusEnum.getAllToMap();
        map.put("payDetailStatusEnum",payDetailStatusEnum );
        //运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDto.getLotId());
        map.put("entrustLot",entrustLot);
        map.put("driverIds", entrustLot.getDriverId());
        map.put("driverNames", entrustLot.getDriverName());

        //判断APP是否进行提货且委托单提货状态没改变
        //获取提货信息 1:提货 2:到货
        List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "1");
        //查询承运商信息
        Carrier carrier = carrierService.selectCarrierById(entrustDto.getCarrierId());
        map.put("carrier",carrier);
        if( entrustWorkList != null && entrustWorkList.size() > 0 ){
            EntrustWork entrustWork = entrustWorkList.get(0);
            map.put("entrustWork", entrustWork);
            //提货图片
            List<EntrustWorkPic> picList = traceService.selectEntrustWorkPicById(entrustWork.getEntrustWorkId());
            map.put("picList", picList);
            //根据dictValue整理图片
            //提货作业图片字典表遍历
            List<SysDictData> dictData = sysDictDataService.selectDictDataByType("pick_pic_type");
            //根据dictData中的dictValue对应carPicList的picType进行整合
            Map<String, Object> picMap = new HashMap<>();
            for (SysDictData sysDictData : dictData) {
                String dictValue = sysDictData.getDictValue();//字典值
                List<EntrustWorkPic> entrustWorkPicMap = new ArrayList<>();
                for (EntrustWorkPic pic : picList) {
                    //判断dictValue和picType相等就存入List中
                    if (dictValue.equals(pic.getWorkAppendixType())) {
                        entrustWorkPicMap.add(pic);
                    }
                }
                picMap.put(dictValue, entrustWorkPicMap);
            }
            map.put("pickPicList", picMap);
            return "tms/trace/pickEdit";
        }else{
            map.put("entrustWork", new EntrustWork());
            map.put("picList", new ArrayList<EntrustWorkPic>());
            return "tms/trace/pick";
        }
    }

    /**
     * 提货多个同时
     *
     * @param map
     * @param entrustIds     委托单id
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @param openType      打开方式         tab新开页面   其余为弹窗
     * @return
     */
    @RequiresPermissions(value ={"tms:trace:pick","carrier:entrust:pick"},logical = Logical.OR)
    @GetMapping("/pickMany/{entrustIds}")
    public String pickMany(ModelMap map, @PathVariable("entrustIds") String entrustIds
            ,@RequestParam("isRefresh") String isRefresh,@RequestParam("openType") String openType) {
        map.put("isRefresh", "1".equals(isRefresh));
        map.put("openTab", "tab".equals(openType));

        //获得委托单数组
        String[] entrustIdList  = entrustIds.split(",");
        List<EntPackGoods> entPackGoodsList = new ArrayList<>();
        //获取委托单货品
        for(String entrustId : entrustIdList){
            EntPackGoods entPackGoods = new EntPackGoods();
            entPackGoods.setEntrustId(entrustId);
            entPackGoods.setDelFlag(0);
            List<EntPackGoods> goodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
            entPackGoodsList.addAll(goodsList);

            //获取提货信息 1:提货 2:到货
            List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "1");
            if( entrustWorkList != null && entrustWorkList.size() > 0 ){
                throw new BusinessException("App存在部分提货信息，Pc无法进行提货作业");
            }
        }
        map.put("entPackGoodsList", entPackGoodsList);
        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustIdList[0]);
        map.put("entrustDto", entrustDto);//货品
        //要求提货日期
        map.put("reqDeliDate", DateFormatUtils.format(entrustDto.getReqDeliDate(),"yyyy-MM-dd"));
        map.put("entrustId", entrustIds);//委托单ID
        //查询委托单对应运单下司机信息
        EntrustLotCarDriver entrustLotCarDriver = new EntrustLotCarDriver();
        entrustLotCarDriver.setLotId(entrustDto.getLotId());
        List<EntrustLotCarDriver> entrustLotCarDriverList = entrustLotCarDriverService.selectEntrustLotCarDriverList(entrustLotCarDriver);
        //运单下对应司机ID，司机名称循环拼接
      /*  String driverIds = "";
        String driverNames = "";
        for (int i = 0; i < entrustLotCarDriverList.size(); i++) {
            EntrustLotCarDriver lotCarDriver = entrustLotCarDriverList.get(i);
            driverIds += lotCarDriver.getDriverId();
            driverNames += lotCarDriver.getDriverName();
            //除了最后拼接逗号
            if (i != entrustLotCarDriverList.size() - 1) {
                driverIds += ",";
                driverNames += ",";
            }
        }
        map.put("driverIds", driverIds);
        map.put("driverNames", driverNames);*/
        //获取运单下运费信息
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustDto.getLotId());
        payDetail.setDelFlag(0);
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetail);
        map.put("payDetailList", payDetailList);
        //应付单状态
        List<Map<String, Object>> payDetailStatusEnum  = PayDetailStatusEnum.getAllToMap();
        map.put("payDetailStatusEnum",payDetailStatusEnum );
        //运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDto.getLotId());
        map.put("entrustLot",entrustLot);
        //查询承运商信息
        Carrier carrier = carrierService.selectCarrierById(entrustDto.getCarrierId());
        map.put("carrier",carrier);
        map.put("entrustWork", new EntrustWork());
        map.put("picList", new ArrayList<EntrustWorkPic>());
        map.put("isMany", "1");
        return "tms/trace/pick";
    }

    /**
     * 提货详细
     *
     * @param map
     * @return
     */
    @RequiresPermissions(value ={"tms:trace:pick","carrier:entrust:pick"},logical = Logical.OR)
    @GetMapping("/pickDetail/{entrustId}")
    public String pickDetail(ModelMap map, @PathVariable("entrustId") String entrustId) {
        //获取货品明细
        EntPackGoods entPackGoods = new EntPackGoods();
        entPackGoods.setEntrustId(entrustId);
        entPackGoods.setDelFlag(0);
        List<EntPackGoods> entPackGoodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
        map.put("entPackGoodsList", entPackGoodsList);
        //合计
        Double numCnt = 0d;
        Double volCnt = 0d;
        Double wightCnt = 0d;
        for(EntPackGoods goods : entPackGoodsList){
            numCnt += goods.getNum();
            volCnt += goods.getVolume();
            wightCnt += goods.getWeight();
        }
        map.put("numCnt", numCnt);
        map.put("volCnt", volCnt);
        map.put("wightCnt", wightCnt);

        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        map.put("entrustDto", entrustDto);
        //获取提货信息 1:提货 2:到货
        List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "1");
        pickArrivalCommon(entrustWorkList,map);
        //获取运单下运费信息
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustDto.getLotId());
        payDetail.setDelFlag(0);
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetail);
        map.put("payDetailList", payDetailList);
        //合计
        BigDecimal transFeeCount =
                payDetailList.stream().map(PayDetail::getTransFeeCount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("transFeeCount", transFeeCount);
        BigDecimal gotAmount =
                payDetailList.stream().map(PayDetail::getGotAmount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("gotAmount", gotAmount);
        BigDecimal ungotAmount =
                payDetailList.stream().map(PayDetail::getUngotAmount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("ungotAmount", ungotAmount);

        //应付单状态
        List<Map<String, Object>> payDetailStatusEnum  = PayDetailStatusEnum.getAllToMap();
        map.put("payDetailStatusEnum",payDetailStatusEnum );
        //运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDto.getLotId());
        map.put("entrustLot",entrustLot);
        return PREFIX + "/pickDetail";
    }

    /**
     * 位置跟踪
     *
     * @param map
     * @return
     */
    @RequiresPermissions("tms:trace:location")
    @GetMapping("/location/{entrustId}")
    public String location(ModelMap map, @PathVariable("entrustId") String entrustId) {
        //获取委托单明细,填写高德地图起始到达地点，城市
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        //起点终点标记
        Map<String, String> pointMap = new HashMap<>();
        String deliAddr = entrustDto.getDeliAddrName();
        String arriAddr = entrustDto.getArriAddrName();
        if(deliAddr.contains("上海市市辖区")){
            deliAddr = deliAddr.replace("市辖区","");
        }
        if(arriAddr.contains("北京市市辖区")){
            arriAddr = arriAddr.replace("市辖区","");
        }
        pointMap.put("deliAddr", deliAddr);
        pointMap.put("arriAddr", arriAddr);
        map.put("pointMap", pointMap);
        //根据委托单获取定位轨迹
        CarLocus carLocus = new CarLocus();
        carLocus.setEntrustId(entrustId);
        List<CarLocus> carLocusList =  carLocusService.selectCarLocusList(carLocus);
        //委托单状态为已提货时，获取车辆当前时间的位置
        /*if(EntrustStatusEnum.PICK_UP.getValue().equals(entrustDto.getVbillstatus())){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.MINUTE,-2);
            Date startTime = calendar.getTime();
            calendar.add(Calendar.MINUTE,2);
            Date endTime = calendar.getTime();
            //根据车牌号，时间段查询车辆位置
            CarLocus currentLocation =  traceService.saveCarLocation(entrustId,startTime,endTime,"trace","");
            if( currentLocation != null ) {
                carLocusList.add(currentLocation);
            }
        }*/
        map.put("carLocusList",carLocusList);
        return PREFIX + "/carLocation";
    }


    @GetMapping("/tacking/carLocation/{entrustId}/{vbillstatus}")
    public String tackingLocation(ModelMap map, @PathVariable("entrustId") String entrustId,  @PathVariable("vbillstatus") String vbillstatus) {
        map.put("vbillstatus", vbillstatus);
        //获取委托单明细,填写高德地图起始到达地点，城市
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        //起点终点标记
        Map<String, String> pointMap = new HashMap<>();
        String deliAddr = entrustDto.getDeliAddrName();
        String arriAddr = entrustDto.getArriAddrName();
        if(deliAddr.contains("上海市市辖区")){
            deliAddr = deliAddr.replace("市辖区","");
        }
        if(arriAddr.contains("北京市市辖区")){
            arriAddr = arriAddr.replace("市辖区","");
        }
        pointMap.put("deliAddr", deliAddr);
        pointMap.put("arriAddr", arriAddr);

        map.put("pointMap", pointMap);
        //根据委托单获取定位轨迹
        CarLocus carLocus = new CarLocus();
        carLocus.setEntrustId(entrustId);
        List<CarLocus> carLocusList =  carLocusService.selectCarLocusList(carLocus);
        //委托单状态为已提货时，获取车辆当前时间的位置
        /*if(EntrustStatusEnum.PICK_UP.getValue().equals(entrustDto.getVbillstatus())){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.MINUTE,-2);
            Date startTime = calendar.getTime();
            calendar.add(Calendar.MINUTE,2);
            Date endTime = calendar.getTime();
            //根据车牌号，时间段查询车辆位置
            CarLocus currentLocation =  traceService.saveCarLocation(entrustId,startTime,endTime,"trace","");
            if( currentLocation != null ) {
                carLocusList.add(currentLocation);
            }
        }*/
        map.put("carLocusList",carLocusList);
        return PREFIX + "/carLocation";
    }

    /**
     * 提货保存
     *
     * @param
     * @return
     */
    @RepeatSubmit
    @PostMapping("/savePick")
    @Log(title = "提货", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult savePick(EntrustWork entrustWork, @RequestParam Map<String, Object> params) throws ParseException {
        //关账判断
        String entrustIds =  entrustWork.getEntrustId();
        String[] entrustIdList = entrustIds.split(",");
        /*for(String entrustId : entrustIdList){
            EntrustDto entrust = traceService.selectEntrustWorkById(entrustId);
            if ("1".equals(entrust.getIsClose())) {
                return AjaxResult.error("该月委托单已关账！");
            }
        }*/
        /*if (!entrustWork.getCorDate().equals(entrust.getCorDate())) {
            return AjaxResult.error("该数据已被修改，请刷新页面！");
        }*/
        entrustWork.setWorkType(EntrustWorkTypeEnum.PICK.getValue().toString());//提货作业
        entrustWork.setStatus(EntrustWorkStatusEnum.CARLEAVE.getValue().toString());//状态
        return toAjax(traceService.savePickOrArrival(entrustWork, params, EntrustStatusEnum.PICK_UP.getValue()));
    }

    /**
     * 提货保存
     *
     * @param
     * @return
     */
    @RepeatSubmit
    @PostMapping("/editPick")
    @Log(title = "提货", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult editPick(EntrustWork entrustWork, @RequestParam Map<String, Object> params) throws ParseException {
        //关账判断
        EntrustDto entrust = traceService.selectEntrustWorkById(entrustWork.getEntrustId());
        /*if ("1".equals(entrust.getIsClose())) {
            return AjaxResult.error("该月委托单已关账！");
        }*/
        if (!entrustWork.getCorDate().equals(entrust.getCorDate())) {
            return AjaxResult.error("该数据已被修改，请刷新页面！");
        }
        entrustWork.setWorkType(EntrustWorkTypeEnum.PICK.getValue().toString());//提货作业
        entrustWork.setStatus(EntrustWorkStatusEnum.CARLEAVE.getValue().toString());//状态
        return toAjax(traceService.editPick(entrustWork, params, EntrustStatusEnum.PICK_UP.getValue()));
    }

    /**
     * 到货
     *
     * @param entrustIds     委托单id
     * @param isRefresh     是否刷新页面      0不刷新 1刷新
     * @param openType      打开方式         tab新开页面   其余为弹窗
     * @return
     */
    @RequiresPermissions(value ={"tms:trace:arrive","carrier:entrust:arrive"},logical = Logical.OR)
    @GetMapping("/arrival/{entrustIds}")
    public String arrival(ModelMap map, @PathVariable("entrustIds") String entrustIds
            ,@RequestParam("isRefresh") String isRefresh,@RequestParam("openType") String openType) {
        map.put("isRefresh", "1".equals(isRefresh));
        map.put("openTab", "tab".equals(openType));

        String[] entrustIdList = entrustIds.split(",");
        List<EntPackGoods> entPackGoodsList = new ArrayList<>();
        //判断是否为干线段
        Integer ltlType = 0;
        for(String entrustId: entrustIdList){
            //获取货品明细
            EntPackGoods entPackGoods = new EntPackGoods();
            entPackGoods.setEntrustId(entrustId);
            entPackGoods.setDelFlag(0);
            List<EntPackGoods> goodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
            entPackGoodsList.addAll(goodsList);

            Entrust entrust = entrustMapper.selectEntrustById(entrustId);
            if(entrust.getIsArrivalSection() != 0){
                ltlType = 1;
            }
        }
        map.put("entPackGoodsList", entPackGoodsList);//货品
        map.put("ltlType", ltlType);

        //货品合计
        Double numSum = 0d;
        Double weightSum = 0d;
        Double volumeSum = 0d;
        for(EntPackGoods entPackGoods1 : entPackGoodsList){
            numSum += entPackGoods1.getNum();
            weightSum += entPackGoods1.getWeight();
            volumeSum += entPackGoods1.getVolume();
        }
        map.put("numSum", numSum);
        map.put("weightSum", weightSum);
        map.put("volumeSum", volumeSum);

        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustIdList[0]);
        map.put("entrustDto", entrustDto);//货品
        //要求到货日期
        map.put("reqArriDate", DateFormatUtils.format(entrustDto.getReqArriDate(),"yyyy-MM-dd"));
        map.put("entrustId", entrustIds);//委托单ID
        map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片

        //获取运单下运费信息
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustDto.getLotId());
        payDetail.setDelFlag(0);
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetail);
        map.put("payDetailList", payDetailList);

        //计算合计
        BigDecimal payDetailTransFeeSum = payDetailList.stream().map(PayDetail::getTransFeeCount).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal payDetailGotSum = payDetailList.stream().map(PayDetail::getGotAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        map.put("payDetailTransFeeSum", payDetailTransFeeSum);
        map.put("payDetailGotSum", payDetailGotSum);

        //应付单状态
        List<Map<String, Object>> payDetailStatusEnum  = PayDetailStatusEnum.getAllToMap();
        map.put("payDetailStatusEnum",payDetailStatusEnum );
        //运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDto.getLotId());
        map.put("entrustLot",entrustLot);
        //查询最晚的提货作业时间
        String pickDate = traceService.selectEntrustWorkPickInfoById(entrustIdList);
        map.put("pickDate",pickDate);
        //查询委托单对应运单下司机信息
        EntrustLotCarDriver entrustLotCarDriver = new EntrustLotCarDriver();
        entrustLotCarDriver.setLotId(entrustDto.getLotId());
        List<EntrustLotCarDriver> entrustLotCarDriverList = entrustLotCarDriverService.selectEntrustLotCarDriverList(entrustLotCarDriver);
        //运单下对应司机ID，司机名称循环拼接
        String driverIds = "";
        String driverNames = "";
        for (int i = 0; i < entrustLotCarDriverList.size(); i++) {
            EntrustLotCarDriver lotCarDriver = entrustLotCarDriverList.get(i);
            driverIds += lotCarDriver.getDriverId();
            driverNames += lotCarDriver.getDriverName();
            //除了最后拼接逗号
            if (i != entrustLotCarDriverList.size() - 1) {
                driverIds += ",";
                driverNames += ",";
            }
        }
        map.put("driverIds", driverIds);
        map.put("driverNames", driverNames);

        final EntrustLotDeposit deposit = newDepositService.selectEntrustLotDepositByLotId(entrustLot.getEntrustLotId());
        map.put("deposit", deposit);

        return "tms/trace/arrival";
    }

    /**
     * /**
     * 提货详细
     *
     * @param map
     * @return
     */
    @RequiresPermissions(value ={"tms:trace:arrive","carrier:entrust:arrive"},logical = Logical.OR)
    @GetMapping("/arrivalDetail/{entrustId}")
    public String arrivalDetail(ModelMap map, @PathVariable("entrustId") String entrustId) {
        //获取货品明细
        EntPackGoods entPackGoods = new EntPackGoods();
        entPackGoods.setEntrustId(entrustId);
        entPackGoods.setDelFlag(0);
        List<EntPackGoods> entPackGoodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
        map.put("entPackGoodsList", entPackGoodsList);//货品
        //合计
        Double numCnt = 0d;
        Double volCnt = 0d;
        Double wightCnt = 0d;
        for(EntPackGoods goods : entPackGoodsList){
            numCnt += goods.getNum();
            volCnt += goods.getVolume();
            wightCnt += goods.getWeight();
        }
        map.put("numCnt", numCnt);
        map.put("volCnt", volCnt);
        map.put("wightCnt", wightCnt);
        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        map.put("entrustDto", entrustDto);//货品
        map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片
        //获取提货信息 1:提货 2:到货
        List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "2");
        pickArrivalCommon(entrustWorkList,map);
        //获取运单下运费信息
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustDto.getLotId());
        payDetail.setDelFlag(0);
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetail);
        map.put("payDetailList", payDetailList);
        //合计
        BigDecimal transFeeCount =
                payDetailList.stream().map(PayDetail::getTransFeeCount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("transFeeCount", transFeeCount);
        BigDecimal gotAmount =
                payDetailList.stream().map(PayDetail::getGotAmount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("gotAmount", gotAmount);
        BigDecimal ungotAmount =
                payDetailList.stream().map(PayDetail::getUngotAmount).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("ungotAmount", ungotAmount);
        //应付单状态
        List<Map<String, Object>> payDetailStatusEnum  = PayDetailStatusEnum.getAllToMap();
        map.put("payDetailStatusEnum",payDetailStatusEnum );
        //运单信息
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(entrustDto.getLotId());
        map.put("entrustLot",entrustLot);
        return PREFIX + "/arrivalDetail";
    }

    /**
     * 提货到货共同，查看详情，取出图片
     * @param entrustWorkList
     * @param map
     */
    public Boolean pickArrivalCommon(List<EntrustWork> entrustWorkList,ModelMap map){
        if( entrustWorkList != null && entrustWorkList.size() > 0 ){
            EntrustWork entrustWork = entrustWorkList.get(0);
            map.put("entrustWork", entrustWork);
            //提货图片
            List<EntrustWorkPic> picList = traceService.selectEntrustWorkPicById(entrustWork.getEntrustWorkId());
            map.put("picList", picList);
            return true;
        }else{
            map.put("entrustWork", new EntrustWork());
            map.put("picList", new ArrayList<EntrustWorkPic>());
            return false;
        }
    }

    /**
     * 到货保存
     *
     * @param
     * @return
     */
    @RepeatSubmit
    @PostMapping("/saveArrival")
    @Log(title = "到货", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult saveArrival(EntrustWork entrustWork, @RequestParam Map<String, Object> params) throws ParseException {
        //关账判断
         /*String entrustIds =  entrustWork.getEntrustId();
        String[] entrustIdList = entrustIds.split(",");
       for(String entrustId : entrustIdList){
            EntrustDto entrust = traceService.selectEntrustWorkById(entrustId);
            if ("1".equals(entrust.getIsClose())) {
                return AjaxResult.error("该月委托单已关账！");
            }
        }*/
        entrustWork.setWorkType(EntrustWorkTypeEnum.ARRIVAL.getValue().toString());//到货作业
        return toAjax(traceService.savePickOrArrival(entrustWork, params, EntrustStatusEnum.ARRIVALS.getValue()));
    }

    @RepeatSubmit
    @PostMapping("/saveArrivalAndConfirm")
    @Log(title = "到货", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult saveArrivalAndConfirm(EntrustWork entrustWork, @RequestParam Map<String, Object> params) throws ParseException {
        //关账判断
         /*String entrustIds =  entrustWork.getEntrustId();
        String[] entrustIdList = entrustIds.split(",");
       for(String entrustId : entrustIdList){
            EntrustDto entrust = traceService.selectEntrustWorkById(entrustId);
            if ("1".equals(entrust.getIsClose())) {
                return AjaxResult.error("该月委托单已关账！");
            }
        }*/
        entrustWork.setWorkType(EntrustWorkTypeEnum.ARRIVAL.getValue().toString());//到货作业
        return traceService.saveArrivalAndConfirm(entrustWork, params, EntrustStatusEnum.ARRIVALS.getValue());
    }




    /**
     * 费用确认页面
     * @return
     * http://localhost:8980/trace/confirmPayment/BAT202109062984/2/2/1
     */
    @GetMapping(value={"/confirmPayment/{lot}/{entrustId}/{carrBalaType}/{isfleetAssign}",
            "/confirmPayment/{lot}/{carrBalaType}/{vbillno}",
            "/confirmPayment/xxlJob/{lot}/{carrBalaType}/{vbillno}"})
    public String confirmPayment(@PathVariable("lot") String lot,
                                 @PathVariable("entrustId") String entrustId,
                                 @PathVariable("carrBalaType") String carrBalaType,
                                 @PathVariable(value = "vbillno",required = false) String vbillno,
                                 @PathVariable(value = "isfleetAssign",required = false) String isfleetAssign,
                                 ModelMap map) {
        map.put("lot",lot);
        map.put("carrBalaType",carrBalaType);
        map.put("vbillno",vbillno);
        map.put("isfleetAssign",isfleetAssign);
        map.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());
        //应付状态map
        Map<String, Object> value = EnumUtil.getNameFieldMap(PayDetailStatusEnum.class, "value");
        map.put("payDetailStatusMap",value);

        //委托单id
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust", entrust);
        map.put("lotId", entrust.getLotId());

        String biz = "yfsq";
        String templateId = wecomService.getTemplateIdByBiz(biz);
        map.put("biz", biz);
        map.put("templateId", templateId);

        return "tms/trace/payDetail";
    }



    @PostMapping("/sunshineArrival")
    @ResponseBody
    public AjaxResult sunshineArrival(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("2");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.sunshineArrival(entrust.getEntrustId(),entrustWork.getActArriDate(),entrustWork.getActLeaveDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @GetMapping("/sunshineArrivalTime")
    public String sunshineArrivalTime(String entrustVbillno,ModelMap map) {
        Entrust entrust = entrustMapper.selectEntrustByVbillno(entrustVbillno);
        EntrustWork entrustWorkSel = new EntrustWork();
        entrustWorkSel.setEntrustId(entrust.getEntrustId());
        entrustWorkSel.setWorkType("2");
        List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
        if(entrustWorks == null || entrustWorks.size() != 1){
            throw new BusinessException("查询到0条或多条到货信息，无法推送");
        }
        EntrustWork entrustWork = entrustWorks.get(0);
        map.put("entrust",entrust);
        map.put("actArriDate",entrustWork.getActArriDate());
        map.put("actLeaveDate",entrustWork.getActLeaveDate());
        return PREFIX + "/sunshineArrivalTime";
    }

    @PostMapping("/sunshineArrivalTimeSave")
    @ResponseBody
    public AjaxResult sunshineArrivalTimeSave(EntrustWork entrustWork) {
        asyncJobService.sunshineArrival(entrustWork.getEntrustId(),entrustWork.getActArriDate(),entrustWork.getActLeaveDate());
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @PostMapping("/sunshinePick")
    @ResponseBody
    public AjaxResult sunshinePick(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("1");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.sunshinePickOnly(entrust.getEntrustId(),entrustWork.getActArriDate(),entrustWork.getActLeaveDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @PostMapping("/sunshineCarDriver")
    @ResponseBody
    public AjaxResult sunshineCarDriver(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.sunshineCarDriverUpload(entrust,false);
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @PostMapping("/sunshineConfirm")
    @ResponseBody
    public AjaxResult sunshineConfirm(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.sunshineConfirm(entrust);
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }


    @PostMapping("/sunshinePickAll")
    @ResponseBody
    public AjaxResult sunshinePickAll(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("1");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.sunshinePick(entrust.getEntrustId(),entrustWork.getActArriDate(),entrustWork.getActLeaveDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @PostMapping("/sunshineReceipt")
    @ResponseBody
    public AjaxResult sunshineReceipt(String entrustVbillnos) {
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.ygdyReceipt(entrust.getEntrustId(),false);
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgPc")
    @ResponseBody
    public AjaxResult zllgPc(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.zllgPc(entrust.getEntrustId(),entrust.getCarnoId(),entrust.getDriverId());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgPickJC")
    @ResponseBody
    public AjaxResult zllgPickJC(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){

            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("1");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条提货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.zllgPickJC(entrust.getEntrustId(),entrustWork.getActArriDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgPickLC")
    @ResponseBody
    public AjaxResult zllgPickLC(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("1");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条提货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.zllgPickLC(entrust.getEntrustId(),entrustWork.getActLeaveDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgPickQR")
    @ResponseBody
    public AjaxResult zllgPickQR(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.zllgPickQR(entrust.getEntrustId());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgArrivalJC")
    @ResponseBody
    public AjaxResult zllgArrivalJC(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("2");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.zllgArrivalJC(entrust.getEntrustId(),entrustWork.getActArriDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @RepeatSubmit
    @PostMapping("/zllgArrivalQR")
    @ResponseBody
    public AjaxResult zllgArrivalQR(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("2");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.zllgArrivalQR(entrust.getEntrustId(),entrustWork.getActLeaveDate(),false);
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }


    @RepeatSubmit
    @PostMapping("/zllgReceipt")
    @ResponseBody
    public AjaxResult zllgReceipt(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            asyncJobService.zllgReceipt(entrust.getEntrustId());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }


    @RepeatSubmit
    @PostMapping("/zllgLeave")
    @ResponseBody
    public AjaxResult zllgLeave(String entrustVbillnos) throws ParseException {
        String[] split =
                Arrays.stream(entrustVbillnos.split(","))
                        .distinct()
                        .toArray(String[]::new);
        for(String vbillno : split){
            Entrust entrust = entrustMapper.selectEntrustByVbillno(vbillno);
            EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("2");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.zllgLeave(entrust.getEntrustId(),entrustWork.getActLeaveDate());
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }



    /**
     * 承运商对账
     * @return
     */
    @GetMapping("/payCheckSheet")
    public String payCheckSheet() {
        return PREFIX + "/payCheckSheet";
    }

    /**
     * 承运商对账列表
     *
     * @return
     */
    @PostMapping("/checkList")
    @ResponseBody
    public TableDataInfo checkList(PayCheckSheet payCheckSheet) {
        startPage();
        List<PayCheckSheet> list = payCheckSheetService.selectPayCheckSheetListGroupByCarrier(payCheckSheet);
        return getDataTable(list);
    }

    /**
     * 承运商对账
     * @return
     */
    @GetMapping("/check/{carrCode}")
    public String check(@PathVariable String carrCode,  ModelMap map) {
        map.put("carrCode",carrCode);
        return "tms/payCheckSheet/payCheckSheet";
    }

    /**
     * 根据车辆ID查找司机
     *
     * @param
     * @return
     */
    @PostMapping("/getCarDriver")
    @ResponseBody
    public AjaxResult getCarDriver(@RequestParam String carId){
        CarDriver carDriver = new CarDriver();
        carDriver.setCarId(carId);
        List<CarDriver> carDriverList = carDriverService.selectCarDriverList(carDriver);
        String driverIds = "";
        String driverNames = "";
        for(CarDriver carDriverVo : carDriverList){
            Driver driver = driverService.selectDriverById(carDriverVo.getDriverId());
            driverNames = driverNames + driver.getDriverName() + ",";
            driverIds = driverIds + driver.getDriverId() + ",";
        }
        if(driverNames.length()>1)
            driverNames = driverNames.substring(0,driverNames.length()-1);
        if(driverIds.length()>1)
            driverIds = driverIds.substring(0,driverIds.length()-1);
        Map<String,String> returnMap = new HashMap<>();
        returnMap.put("driverIds",driverIds);
        returnMap.put("driverNames",driverNames);
        return AjaxResult.success(returnMap);
    }

    /**
     * 提货、到货、回单反确认的方法
     * @param unconfirm  委托单对象
     * @return
     */
    @RepeatSubmit
    @RequiresPermissions(value = {"tms:invoice:back_confirm", "tms:trace:pickOppositeAffirm"},logical=Logical.OR)
    @RequestMapping(value = "/backConfirmPick", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult backConfirmPick(Unconfirm unconfirm) {
        if(StringUtils.isEmpty(unconfirm.getEntrustId())) {
            throw new BusinessException("数据异常!");
        }

        if (unconfirm.getDeConfirmation().equals("8")) {
            return traceService.backDispatch(unconfirm);
        } else {
            return traceService.backConfirm(unconfirm,true);
        }

    }


    /**
     * 提货、到货、回单反确认的方法
     * @param unconfirm  委托单对象
     * @return
     */
    @RepeatSubmit
    @RequestMapping(value = "/backConfirmCarrier", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult backConfirmCarrier(Unconfirm unconfirm) {
        if(StringUtils.isEmpty(unconfirm.getEntrustId()) || StringUtils.isEmpty(unconfirm.getCarrierId())) {
            throw new BusinessException("数据异常!");
        }
        return traceService.backConfirmCarrier(unconfirm);
    }

    @RepeatSubmit
    @RequestMapping(value = "/addUnconfirm", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult addUnconfirm(Unconfirm unconfirm) {
        if(StringUtils.isEmpty(unconfirm.getEntrustId())) {
            throw new BusinessException("请填写关联单号!");
        }
        return traceService.addUnconfirm(unconfirm);
    }


    @RepeatSubmit
    @RequestMapping(value = "/editUnconfirm", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult editUnconfirm(Unconfirm unconfirm) {
        return traceService.editUnconfirm(unconfirm);
    }

    /**
     * 判断是否可以进行货量更新
     * @param entrustId
     * @return
     */
    @RequestMapping(value = "/checkConfirmPick", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult checkConfirmPick(@RequestParam("entrustId") String entrustId){
        //查询是否存在费用
        List<EntrustCost> entrustCostList = traceService.selectEntrustCostById(entrustId);
        if(entrustCostList.size()>0){
            return AjaxResult.error("该委托单已产生费用，请先删除费用！");
        }
        return AjaxResult.success();
    }

    @RequestMapping(value = "/closeInfo", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public AjaxResult closeInfo(@RequestParam("entrustId") String entrustId){
        Entrust entrust = entrustMapper.selectEntrustById(entrustId);

        List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
        //应收
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailService.selectReceiveByInvoiceId(entrust.getOrderno());
        BigDecimal totalReceiveAmount = receiveDetailVOS.stream().map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //三方
        List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeListByInvoiceId(entrust.getOrderno());
        BigDecimal totalOtherFeeAmount = otherFeeList.stream().map(OtherFee::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //应付
        List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(entrust.getLotId());
        BigDecimal totalPayDetailAmount = payDetailList.stream().map(PayDetail::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<String,Object> res = new HashMap<>();
        String joinedLot = entrusts.stream()
                .map(Entrust::getLot)
                .collect(Collectors.joining(", "));

        res.put("lot",joinedLot);
        res.put("totalReceiveAmount",totalReceiveAmount);
        res.put("totalOtherFeeAmount",totalOtherFeeAmount);
        res.put("totalPayDetailAmount",totalPayDetailAmount);
        res.put("invoiceVbillno",entrust.getInvoiceVbillno());
        return AjaxResult.success(res);
    }


    /**
     * 跳转新增异常跟踪页面
     *
     * @param map
     * @param entrustExpId
     * @return
     */
/*    @RequiresPermissions("tms:trace:abnormal:handle")
    @GetMapping("/handleAbnormal/{entrustExpId}")
    public String handleAbnormal(ModelMap map, @PathVariable("entrustExpId") String entrustExpId) {
        EntrustExp entrustExp = entrustExpService.selectEntrustExpById(entrustExpId);
        map.put("entrustExp",entrustExp);
        //根据异常ID查询处理记录
        EntrustExpResult entrustExpResult = new EntrustExpResult();
        entrustExpResult.setEntrustExpId(entrustExpId);
        List<EntrustExpResult> entrustExpResultList = entrustExpResultServiceService.selectEntrustExpResultList(entrustExpResult);
        for(EntrustExpResult result : entrustExpResultList){
            if(StringUtils.isNotBlank(result.getHandleAppendixId())){
                List<SysUploadFile> uploadFileList = uploadFileService.selectSysUploadFileByTid(result.getHandleAppendixId());
                result.setUploadFileList(uploadFileList);
            }
        }
        map.put("currentUser",shiroUtils.getSysUser().getUserName());
        map.put("entrustExpResultList", entrustExpResultList);
        return PREFIX + "/abnormalHandle";
    }*/

    /**
     * 异常跟踪处理
     *
     * @param entrustExpResult
     * @return
     */
    @RepeatSubmit
    @PostMapping("/saveHandleResult")
    @Log(title = "异常跟踪", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult saveHandleResult(EntrustExpResult entrustExpResult) {
        return toAjax(traceService.handleEntrustExp(entrustExpResult));
    }

    /**
     * 异常跟踪处理
     *
     * @param entrustExp
     * @return
     */
    @RepeatSubmit
    @PostMapping("/saveHandleException")
    @Log(title = "异常跟踪", businessType = BusinessType.UPDATE)
    @ResponseBody
    public AjaxResult saveHandleException(EntrustExp entrustExp) {
        return toAjax(traceService.saveHandleException(entrustExp));
    }

    /**
     * 跳转修改异常页面
     * @param map
     * @param entrustExpId
     * @return
     */
    @GetMapping("/handleAbnormalDetail/{entrustExpId}")
    public String handleAbnormalDetail(ModelMap map, @PathVariable("entrustExpId") String entrustExpId) {
        return PREFIX + "/handleAbnormalDetail";
    }

    /**
     * 跳转月度回单页面
     *
     * @param map
     * @return
     */
    @GetMapping("/receiptForMonth")
    public String receiptForMonth(ModelMap map) {
        map.put("entrustStatusEnum", EntrustStatusEnum.getAllToMap());
        //获取关账时间
        List<CloseAccount> list = closeAccountService.selectCloseAccountList(null);
        map.put("CloseAccountList", list);
        //委托单待确认状态
        String toAffirmstatus = EntrustStatusEnum.TO_AFFIRM.getValue();
        map.put("toAffirmstatus", toAffirmstatus);
        //委托单已确认状态
        String affirmStatus = EntrustStatusEnum.AFFIRM.getValue();
        map.put("affirmStatus", affirmStatus);
        return PREFIX + "/receipt_month";
    }

    /**
     * 查看月度回单列表
     *
     * @return
     */
    @PostMapping("/receiptMonthList")
    @ResponseBody
    public TableDataInfo receiptMonthList(EntrustDto entrustDto) {
        //查看已确认的
        entrustDto.setReceiptConfirmFlag(1);
        entrustDto.setAddressFilter("1");
        startPage();
        List<EntrustDto> list = traceService.selectTraceList(entrustDto);
        return getDataTable(list);
    }

    /**
     * 查看月度回单列表
     *
     * @return
     */
    @PostMapping("/getCountReceiptMonth")
    @ResponseBody
    public AjaxResult getCountReceiptMonth(EntrustDto entrustDto) {
        //查看已确认的
        entrustDto.setReceiptConfirmFlag(1);
        entrustDto.setAddressFilter("1");
        Map<String, Object> map = traceService.getCountReceiptMonth(entrustDto);
        return AjaxResult.success(map);
    }

    /**
     * 跳转司机代收页面
     * @param map
     * @param lotId
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:view", "tms:fleet:trace:collection"}, logical=Logical.OR)
    @GetMapping("/collection")
    public String collection(ModelMap map, @RequestParam String lotId , @RequestParam String entrustId) {
        map.put("lotId",lotId);
        map.put("entrustId",entrustId);
        return "tms/trace/collection";
    }

    /**
     * 根据运单号查询司机代收列表
     *
     * @return
     */
    @PostMapping("/collectionList")
    @ResponseBody
    public TableDataInfo list(DriverCollection driverCollection) {
        driverCollection.setDelFlag(0);
        startPage();
        List<DriverCollection> list = driverCollectionService.selectDriverCollectionList(driverCollection);
        return getDataTable(list);
    }

    /**
     * 跳转司机代收页面
     * @param map
     * @param
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:add", "tms:fleet:trace:collection:add"}, logical=Logical.OR)
    @GetMapping("/addCollection")
    public String addCollection(ModelMap map, @RequestParam String entrustId) {
        map.put("entrustId",entrustId);

        /*
         * 查询应收，根据应付单运单id—>查询关联的委托单—>根据委托单list去重查出所有发货单id->查询发货单对应应收
         */
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        //根据发货单ids 查询所有应收
        List<ReceiveDetailVO> receiveDetailList = receiveDetailService.selectReceiveByInvoiceId(entrust.getOrderno());

        receiveDetailList = receiveDetailList.stream()
                .filter(x -> ReceiveDetailStatusEnum.NEW.getValue() == x.getVbillstatus()
                        || ReceiveDetailStatusEnum.AFFIRM.getValue() == x.getVbillstatus())
//                .filter(x -> x.getIsCollect() == 0)
//                .filter(x -> FreeTypeEnum.PREPAID_CASH.getValue().equals(x.getFreeType()))
                .collect(Collectors.toList());
        map.put("receiveDetailList", receiveDetailList);

        Invoice invoice = invoiceService.selectInvoiceById(entrust.getOrderno());
        map.put("balaType", invoice.getBalaType());

        BigDecimal transFeeCountAll = receiveDetailList.stream()
                .map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("transFeeCountAll", transFeeCountAll);

        //应收明细状态
        map.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());
        //应收已确认状态
        map.put("receiveDetailAffirm", ReceiveDetailStatusEnum.AFFIRM.getValue());
        //费用类型
        map.put("freeTypeEnum", FreeTypeEnum.getAllToMap());

        /*
         * 查询应付
         */
        PayDetail payDetailSearch = new PayDetail();
        payDetailSearch.setDelFlag(0);
        payDetailSearch.setLotId(entrust.getLotId());
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetailSearch);
        map.put("payDetailList", payDetailList);
        //应付单状态
        map.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());
        //应付已确认状态
        map.put("payDetailAffirm", PayDetailStatusEnum.AFFIRM.getValue());

        return "tms/trace/addCollection";
    }

    /**
     * 保存司机代收
     *
     * @param driverCollection
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:add", "tms:fleet:trace:collection:add"}, logical=Logical.OR)
    @RepeatSubmit
    @PostMapping("/saveCollection")
    @Log(title = "司机代收", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult saveCollection(DriverCollectionAddVO driverCollection) {
        return driverCollectionService.saveCollection(driverCollection);
    }

    /**
     * 司机代收确认
     * @param driverCollection
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:affirm", "tms:fleet:trace:collection:affirm"}, logical=Logical.OR)
    @RequestMapping("/driverCollectionAffirm")
    @ResponseBody
    public AjaxResult driverCollectionAffirm(DriverCollection driverCollection) {
        return driverCollectionService.driverCollectionAffirm(driverCollection);
    }

    /**
     * 删除司机代收
     *
     * @param entrustId
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:delete", "tms:fleet:trace:collection:delete"}, logical=Logical.OR)
    @PostMapping("/removeCollection")
    @Log(title = "司机代收", businessType = BusinessType.DELETE)
    @ResponseBody
    public AjaxResult removeCollection(String entrustId) {
        return driverCollectionService.removeCollectionByEntrustId(entrustId);
    }

    /**
     * 验证金额
     *
     * @param driverCollection
     * @return
     */
    @PostMapping("/checkAmount")
    @ResponseBody
    public String checkCarNo(DriverCollection driverCollection) {
        DriverCollection driverCollectionSel = new DriverCollection();
        driverCollectionSel.setEntrustId(driverCollection.getEntrustId());
        driverCollectionSel.setDelFlag(0);
        List<DriverCollection> list = driverCollectionService.selectDriverCollectionList(driverCollectionSel);
        //计算已经保存的司机代收
        BigDecimal collectionAmount = list.stream().map(DriverCollection::getCollectionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        collectionAmount = collectionAmount.add(driverCollection.getCollectionAmount());
        //计算应收合计
        /*
         * 查询应收，根据应付单运单id—>查询关联的委托单—>根据委托单list去重查出所有发货单id->查询发货单对应应收
         */
        Entrust entrustSearch = new Entrust();
        entrustSearch.setEntrustId(driverCollection.getEntrustId());
        List<Entrust> entrustList = entrustService.selectEntrustList(entrustSearch);
        //去重查询发货单
        String[] invoiceIds = entrustList.stream().map(Entrust::getOrderno).distinct().toArray(String[]::new);
        //根据发货单ids 查询所有应收
        List<ReceiveDetail> receiveDetailList = receiveDetailService.selectReceiveDetailListByInvoiceIds(invoiceIds);
        BigDecimal transFeeCnt = receiveDetailList.stream()
//                .filter(x -> FreeTypeEnum.PREPAID_CASH.getValue().equals(x.getFreeType()))
                .map(ReceiveDetail::getTransFeeCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(collectionAmount.compareTo(transFeeCnt) > 0){
            return "1";
        }else{
            return "0";
        }
    }

    /**
     * 跳转反确认页面
     * @param entrustId 委托单Id
     * @param map
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:oppositeAffirm", "tms:fleet:trace:collection:oppositeAffirm"}, logical=Logical.OR)
    @RequestMapping(value = "/backConfirmCollection/{payDetailId}", method = {RequestMethod.GET,RequestMethod.POST})
    public String backConfirmPage(@PathVariable String payDetailId,ModelMap map) {
        map.put("payDetailId", payDetailId);
        return PREFIX + "/backConfirmCollection";
    }

    /**
     * 司机代收反确认
     * @param driverCollection
     * @return
     */
    @RequiresPermissions(value = {"tms:trace:collection:oppositeAffirm", "tms:fleet:trace:collection:oppositeAffirm"}, logical=Logical.OR)
    @RequestMapping("/backConfirmCollection")
    @ResponseBody
    public AjaxResult backConfirmCollection(DriverCollection driverCollection){
        return driverCollectionService.backConfirmCollection(driverCollection);
    }

    /**
     * 核销记录
     * @param payDetailId
     * @param map
     * @return
     */
    @GetMapping("/payRecord/{payDetailId}")
    public String payRecord(@PathVariable("payDetailId") String payDetailId, ModelMap map) {
        //司机代收记录
        DriverCollection driverCollection = driverCollectionService.selectDriverCollectionById(payDetailId);
        map.put("driverCollection", driverCollection);

        /*
         * 查询应收，根据应付单运单id—>查询关联的委托单—>根据委托单list去重查出所有发货单id->查询发货单对应应收
         */
        Entrust entrustSearch = new Entrust();
        entrustSearch.setLotId(driverCollection.getLotId());
        List<Entrust> entrustList = entrustService.selectEntrustList(entrustSearch);
        //去重查询发货单
        String[] invoiceIds = entrustList.stream().map(Entrust::getOrderno).distinct().toArray(String[]::new);
        //根据发货单ids 查询所有应收
        List<ReceiveDetail> receiveDetailList = receiveDetailService.selectReceiveDetailListByInvoiceIds(invoiceIds);
        map.put("receiveDetailList", receiveDetailList);
        //应收明细状态
        map.put("receiveDetailStatusEnum", ReceiveDetailStatusEnum.getAllToMap());
        //应收已确认状态
        map.put("receiveDetailAffirm", ReceiveDetailStatusEnum.AFFIRM.getValue());
        //费用类型
        map.put("freeTypeEnum", FreeTypeEnum.getAllToMap());

        /*
         * 查询应付
         */
        PayDetail payDetailSearch = new PayDetail();
        payDetailSearch.setDelFlag(0);
        payDetailSearch.setLotId(driverCollection.getLotId());
        List<PayDetail> payDetailList = payDetailService.selectPayDetailList(payDetailSearch);
        map.put("payDetailList", payDetailList);
        //应付单状态
        map.put("payDetailStatusEnum", PayDetailStatusEnum.getAllToMap());
        //应付已确认状态
        map.put("payDetailAffirm", PayDetailStatusEnum.AFFIRM.getValue());

        return PREFIX + "/pay_record";
    }

    /**
     * 跳转修改要求提货日期
     * @param entrustId
     * @param map
     * @return
     */
    @RequestMapping("/editReqDeliDate/{entrustId}/{deConfirmation}")
    public String editReqDeliDate(@PathVariable String entrustId, @PathVariable String deConfirmation, ModelMap map){
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust",entrust);
        map.put("deConfirmation", deConfirmation);
        return PREFIX + "/edit_req_deli_date";
    }

    /**
     * 修改要求提货日期
     * @param unconfirmVO
     * @return
     */
    @PostMapping("/saveReqDeliDate")
    @ResponseBody
    public AjaxResult saveReqDeliDate(UnconfirmVO unconfirmVO) {
        return traceService.saveReqDeliDate(unconfirmVO);
    }

    /**
     * 跳转修改要求到货日期
     * @param entrustId
     * @param map
     * @return
     */
    @RequestMapping("/editReqArriDate/{entrustId}/{deConfirmation}")
    public String editReqArriDate(@PathVariable String entrustId, @PathVariable String deConfirmation, ModelMap map){
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust",entrust);
        map.put("deConfirmation", deConfirmation);
        return PREFIX + "/edit_req_arri_date";
    }

    /**
     * 修改要求到货日期
     * @param unconfirmVO
     * @return
     */
    @PostMapping("/saveReqArriDate")
    @ResponseBody
    public AjaxResult saveReqArriDate(UnconfirmVO unconfirmVO) {
        return traceService.saveReqArriDate(unconfirmVO);
    }

    /**
     * 动态获取数字字典数据
     * @return
     */
    @PostMapping("/queryDictData")
    @ResponseBody
    public AjaxResult queryDictData(String dictType,String cssClass) {
        List<SysDictData> sysDictDataList = sysDictDataService.selectDictDataByTypeAndCssClass(dictType,cssClass);
        return AjaxResult.success(sysDictDataList);

    }

    @RequestMapping("/getArrivalTime")
    @ResponseBody
    public AjaxResult getArrivalTime(String deliTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parse = sdf.parse(deliTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        //增加一小时
        calendar.add(Calendar.HOUR,1);
        String arriTime = sdf.format(calendar.getTime());
        return AjaxResult.success(arriTime);
    }



    /**
     * 到货司机代收校验
     *
     * @param entrustId
     * @return
     */
    @PostMapping("/check_driver_receipt_arrive")
    @ResponseBody
    public AjaxResult checkDriverReceiptArrive(String entrustId){
        return traceService.checkDriverReceiptArrive(entrustId);
    }

    /**
     * 提货司机代收校验
     *
     * @param entrustId
     * @return
     */
    @PostMapping("/check_driver_receipt_pick")
    @ResponseBody
    public AjaxResult checkDriverReceiptPick(String entrustId){
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        Invoice invoice = invoiceMapper.selectInvoiceById(entrust.getOrderno());

        int i = traceService.checkDriverReceiptPick(entrustId);

        String balaType = invoice.getBalaType();

        String msg = "5".equals(balaType) ? "到付" : "代收";


        if (i == 1) {
            return AjaxResult.success("该单需要" + msg + "【" + entrust.getCollectAmount() + "元】,请及时添加数据！");

        } else if (i == 2) {
            return AjaxResult.success("该单需要" + msg + "【" + entrust.getCollectAmount() + "元】，系统将自动生成数据！");

        }

        return AjaxResult.success("");
    }

    /**
     * 到货推送
     *
     * @param
     * @return
     */
    @Log(title = "到货推送", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:trace:pick_push"}, logical = Logical.OR)
    @PostMapping("/pick_arri_push")
    @ResponseBody
    public AjaxResult cancel(String entrustWorkId) {
        return nfpSyncService.syncPickOrArrive(entrustWorkId);
    }

    /**
     * 邀请司机签约
     *
     * @param
     * @return
     */
    @Log(title = "邀请司机签约", businessType = BusinessType.OTHER)
    @RequiresPermissions(value = {"tms:trace:invite_driver"}, logical = Logical.OR)
    @PostMapping("/invite_driver")
    @ResponseBody
    public AjaxResult inviteDriver(String driverId) {
        return nfpSyncService.inviteDriver(driverId);
    }


    @GetMapping("/generateQrCode")
    public void generateQrCode(@RequestParam("entrustLotId") String entrustLotId, HttpServletResponse response) throws Exception{
        ServletOutputStream stream = null;
        try {
            String content = "https://www.qixin56.com/orderDetail?entrustLotId=" + entrustLotId;
            String logoUrl = "https://oss.qixin56.com/tms/1413e727c330d635ba97eb5a557ebe94.png";
            //String logoUrl = "d:/api_logo.png";
            //String logoUrl = "http://localhost:1888/img/login/api_logo.png";
            stream = response.getOutputStream();
            QRCodeUtil.getQRCode(content, logoUrl, stream);
        } catch (Exception e) {
            e.getStackTrace();
        } finally {
            if (stream != null) {
                stream.flush();
                stream.close();
            }
        }
    }

    /**
     * 应付调整信息
     * @param map
     * @return
     */
    @RequiresPermissions(value = {"finance:receive:adjust","finance:fleet:receive:adjust"
            ,"trace:payReconciliation:adjustSingle","trace:payReconciliation:adjustSingleMonth"
            ,"tms:segment:adjustPayDetail"},logical = Logical.OR)
    @RequestMapping("/addPayDetailAdjust")
    public String addPayDetailAdjust(ModelMap map,@RequestParam String invoiceLotId,@RequestParam Integer adjustType){
        map.put("invoiceLotId",invoiceLotId);
        map.put("adjustType",adjustType);
        String billingType = "";
        if(adjustType == 0){
            Invoice invoice = invoiceMapper.selectInvoiceById(invoiceLotId);
            billingType = invoice.getBillingType();
            //应收
            String[] ids = new String[1];
            ids[0] = invoice.getVbillno();
            List<ReceiveDetail> receiveDetailList = traceService.sumReceiveDetailListByInvoiceIds(ids);
            map.put("receiveDetailList",receiveDetailList);
            //查询是否有待审核
            Integer cnt = payDetailMapper.selectReceiveDetailUncheckCntByInvoiceNo(invoice.getVbillno());
            map.put("waitCheckCnt",cnt);
            //查询最近审核记录
            AdjustRecordVO adjustRecord = payDetailMapper.selectReceiveDetailUncheckInfoByInvoiceNo(invoice.getVbillno());
            map.put("adjustRecord",adjustRecord);
            if(cnt > 0){
                return PREFIX + "/adjust_history";
            }
        }else if(adjustType == 1){
            //运单
            EntrustLot entrustLot = entrustLotService.selectEntrustLotById(invoiceLotId);
            billingType = entrustLot.getBillingType().toString();
            //应付
            List<PayDetail> payDetailList = traceMapper.sumPayDetailListByLotId(invoiceLotId);
            map.put("payDetailList",payDetailList);
            //验证是否存在待审核的运单信息
            Integer payDetailCnt = payDetailMapper.selectPayDetailUncheckCntByLot(entrustLot.getLot());
            map.put("waitCheckCnt",payDetailCnt);
            AdjustRecordVO payDetailAdjustRecord = payDetailMapper.selectPayDetailUncheckInfoByLot(entrustLot.getLot());
            map.put("adjustRecord",payDetailAdjustRecord);
            if(payDetailCnt > 0){
                return PREFIX + "/adjust_history";
            }
        }
        map.put("billingType",billingType);
        return "tms/trace/add_adjust";
    }

    @RequiresPermissions(value = {"finance:receive:adjust","finance:fleet:receive:adjust"
            ,"trace:payReconciliation:adjustSingle","trace:payReconciliation:adjustSingleMonth"
            ,"tms:segment:adjustPayDetail"},logical = Logical.OR)
    @RepeatSubmit
    @PostMapping("/addAdjust")
    @ResponseBody
    public AjaxResult addAdjust(TraceAdjustDto traceAdjustDto){
        return traceService.addAdjust(traceAdjustDto);
    }

    /**
     * 增加应付费用跳过审验
     * @param traceAdjustDto
     * @return
     */
    @PostMapping("/addAdjustAndChecked")
    @ResponseBody
    public AjaxResult addAdjustAndChecked(TraceAdjustDto traceAdjustDto){
        return traceService.addAdjustAndChecked(traceAdjustDto);
    }

    /**
     * 确认承运商费用
     * @param lotIds
     * @return
     */
    @PostMapping("/confirmCarrierFee")
    @ResponseBody
    public AjaxResult confirmCarrierFee(String lotIds){
        return traceService.confirmCarrierFee(lotIds);
    }

    /**
     * 应付调整信息
     * @param map
     * @return
     */
    @RequestMapping("/addAdjustMuitiple")
    public String addPayDetailAdjust(ModelMap map,@RequestParam String lotId,@RequestParam String invoiceId,@RequestParam String entrustId){

        MultipleGoodsConn multipleGoodsConn = new MultipleGoodsConn();
        multipleGoodsConn.setEntrustId(entrustId);
        List<MultipleGoodsConn> multipleGoodsConns = multipleGoodsConnMapper.selectMultipleGoodsConnList(multipleGoodsConn);
        map.put("multipleGoodsConns",multipleGoodsConns);

        List<PayDetail> payDetailList = traceMapper.sumPayDetailListByLotId(lotId);
        map.put("payDetailList",payDetailList);

        //发货单
        Invoice invoice = invoiceMapper.selectInvoiceById(invoiceId);
        map.put("invoice",invoice);

        ReceivableReconciliationDTO receivableReconciliation = new ReceivableReconciliationDTO();
        receivableReconciliation.setVbillno(invoice.getVbillno());
        List<ReceivableReconciliationVO> list = receivableReconciliationMapper.listReceivableReconciliation(receivableReconciliation);
        Map<String,Object> netMap = new HashMap<>();
        netMap.put("invoiceId",invoice.getInvoiceId());
        netMap.put("invoiceVbillno",invoice.getVbillno());
        netMap.put("billingType",invoice.getBillingType());
        if(list != null && list.size() == 1){
            BigDecimal netProfits = list.get(0).getNetProfits();
            netMap.put("netProfits", netProfits);
            netMap.put("profitsFlag", 1);
        }else{
            netMap.put("profitsFlag", 0);
        }
        map.put("netMap",netMap);

        //现金计算税率
        String cash_tax_rate = configService.selectConfigByKey("cash_tax_rate");
        map.put("cash_tax_rate", cash_tax_rate);

        map.put("customerId",invoice.getCustomerId());

        //查询发货单货品
        Entrust entrustSel = new Entrust();
        entrustSel.setOrderno(invoiceId);
        entrustSel.setDelFlag(0);
        List<Entrust> entrusts = entrustService.selectEntrustList(entrustSel);
        String invoiceGoodsName = "";
        for(Entrust entrust : entrusts){
            invoiceGoodsName = invoiceGoodsName + entrust.getGoodsName() + ",";
        }
        map.put("invoiceGoodsName",invoiceGoodsName.substring(0,invoiceGoodsName.length()-1));

        entrustSel = new Entrust();
        entrustSel.setLotId(lotId);
        entrustSel.setDelFlag(0);
        entrusts = entrustService.selectEntrustList(entrustSel);
        String lotGoodsName = "";
        for(Entrust entrust : entrusts){
            lotGoodsName = lotGoodsName + entrust.getGoodsName() + ",";
        }
        map.put("lotGoodsName",lotGoodsName.substring(0,lotGoodsName.length()-1));

        //运单
        EntrustLot entrustLot = entrustLotService.selectEntrustLotById(lotId);
        map.put("entrustLot",entrustLot);

        Set<String> deliAreaList = new HashSet();
        Set<String> arriAreaList = new HashSet();
        Set<String> arriAddrName = new HashSet();

        List<MultipleShippingAddress> multipleShippingAddressLotList = multipleShippingAddressMapper.selectListByLotId(entrustLot.getEntrustLotId());
        for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddressLotList){
            if(multipleShippingAddress.getAddressType() == 0){
                deliAreaList.add(multipleShippingAddress.getAreaId());
            }else{
                arriAreaList.add(multipleShippingAddress.getAreaId());
                arriAddrName.add(multipleShippingAddress.getAddrName());
            }
        }

        map.put("deliAreaList",deliAreaList);
        map.put("arriAreaList",arriAreaList);
        map.put("arriAddrName",arriAddrName);

        //找对应运单的运费
        PayDetail payDetail = new PayDetail();
        payDetail.setLotId(entrustLot.getEntrustLotId());
        List<PayDetail> payDetailList1 = payDetailService.selectPayDetailList(payDetail);
        //计算运费和在途
        BigDecimal frightPayAmount = payDetailList1.stream().filter(e -> e.getFreeType().equals("0")).map(PayDetail::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        map.put("frightPayAmount",frightPayAmount);


        //三方
        String[] ids = new String[1];
        ids[0] = invoice.getVbillno();
        List<OtherFee> otherFeeList = traceMapper.sumOtherFeeListByInvoiceVbillno(ids);
        map.put("otherFeeList",otherFeeList);

        //应收
        List<ReceiveDetail> receiveDetailList = traceService.sumReceiveDetailListByInvoiceIds(ids);
        map.put("receiveDetailList",receiveDetailList);


        return PREFIX + "/add_adjust_muitiple";
    }

    @PostMapping("/addMultipleAdjust")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult addMultipleAdjust(MultipleAdjustDto multipleAdjustDto){
        return traceService.addMultipleAdjust(multipleAdjustDto);
    }

    /**
     * 应付调整信息
     * @param map
     * @return
     */
    @RequestMapping("/advancePay")
    public String addPayDetailAdjust(ModelMap map, String carrierId){
        //查询承运商信息
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier",carrier);
        //计算剩余额度
        if(carrier.getAdvancePayMoneyQuota() == null){
            carrier.setAdvancePayMoneyQuota(BigDecimal.ZERO);
        }
        BigDecimal leaveQuota  = carrier.getAdvancePayMoneyQuota().subtract(carrier.getAdvancePayMoney()==null?BigDecimal.ZERO:carrier.getAdvancePayMoney());
        //查询申请次数
        AdvancePayRecord advancePayRecord = new AdvancePayRecord();
        advancePayRecord.setCarrierId(carrierId);
        advancePayRecord.setType(0);
        List<AdvancePayRecord> advancePayRecords = advancePayService.selectAdvancePayRecordList(advancePayRecord);
        map.put("applyCnt", advancePayRecords.size());
        //合计未付款单量/金额
        Map<String, Object> ungotMap = payDetailMapper.selectUnPayCntAndAmountByCarrierId(carrierId);
        map.put("ungotAmount", ungotMap.get("AMOUNT"));
        map.put("ungotCnt", ungotMap.get("CNT"));
        //对账未确认单量
        Map<String, Object> unConfrimMap = payDetailMapper.selectUnConfirmCntAndAmountByCarrier(carrierId);
        map.put("unConfrimAmount", unConfrimMap.get("AMOUNT"));
        map.put("unConfrimCnt", unConfrimMap.get("CNT"));
        //查询对账已确认单量
        Map<String, Object> confrimMap = payDetailMapper.selectConfirmCntAndAmountByCarrier(carrierId);
        map.put("confrimAmount", confrimMap.get("AMOUNT"));
        map.put("confrimCnt", confrimMap.get("CNT"));
        //查询未对账单量
        Map<String, Object> unPackageMap = payDetailMapper.selectUnPackageCntAndAmountByCarrierId(carrierId);
        map.put("unPackageAmount", unPackageMap.get("AMOUNT"));
        map.put("unPackageCnt", unPackageMap.get("CNT"));


        return PREFIX + "/advance_pay";
    }

    /**
     * 保存预借申请
     * @param advancePayRecord
     * @return
     */
    @PostMapping("/saveAdvancePay")
    @ResponseBody
    public AjaxResult addMultipleAdjust(AdvancePayRecord advancePayRecord){
        return traceService.saveAdvancePay(advancePayRecord);
    }

    /**
     * 外呼
     * @param phone
     * @return
     */
    @RequestMapping("/callOut")
    @ResponseBody
    public AjaxResult callOut(String phone){
        //查询当前用户绑定的座机信息
        List<CallUser> callUsers = callUserMapper.selectCallUserByUserId(shiroUtils.getUserId().toString());
        if(callUsers == null || callUsers.size() == 0){
            return AjaxResult.error("当前账号未绑定坐席呼叫，如需开通请联系信息部。");
        }else if(callUsers.size() > 1){
            return AjaxResult.error("当前账号绑定多个坐席呼叫，请联系信息部解决异常。");
        }else{
            return CallUtils.call(callUsers.get(0),phone);
        }
    }

    /**
     * 坐席签入/呼叫转移
     * @param type 0坐席 1转移
     * @return
     */
    @RequestMapping("/changeCall")
    @ResponseBody
    public AjaxResult changeCall(String type,@RequestParam(required = false) String id){
        logger.info("进入方法成功,type{}",type);
        CallUser callUser = null;
        //查询当前用户绑定的座机信息
        if(id == null) {
            List<CallUser> callUsers = callUserMapper.selectCallUserByUserId(shiroUtils.getUserId().toString());
            if(callUsers == null || callUsers.size() == 0){
                return AjaxResult.error("当前账号未绑定坐席呼叫，如需开通请联系信息部。");
            }else if(callUsers.size() > 1){
                return AjaxResult.error("当前账号绑定多个坐席呼叫，请联系信息部解决异常。");
            }
            callUser = callUsers.get(0);
        }else{
            callUser = callUserMapper.selectCallUserById(id);
        }
        if("0".equals(type))
            return CallUtils.login(callUser.getWorkNo(),callUser.getPhoneNumber());
        else
            return CallUtils.login(callUser.getWorkNo(),callUser.getTransferNumber());

    }

    @RequestMapping("/callRefresh")
    @ResponseBody
    public AjaxResult callRefresh() {
        logger.info("更新呼叫软件GUID开始执行！");
        //查询登记的信息
        List<CallUser> callUsers = callUserMapper.selectCallUserList(new CallUser());
        for(CallUser callUser : callUsers){
            CallUtils.refreshGuid(callUser.getWorkNo());
        }
        logger.info("更新呼叫软件GUID执行结束！");
        return AjaxResult.success();
    }

    /**
     * 跳转修改要求到货日期
     * @param entrustId
     * @param map
     * @return
     */
    @RequestMapping("/editTransCode/{entrustId}")
    public String editReqArriDate(@PathVariable String entrustId, ModelMap map){
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust",entrust);
        return PREFIX + "/edit_trans_code";
    }

    /**
     * 修改运输方式
     * @param entrust
     * @return
     */
    @RequestMapping("/saveTransCode")
    @Transactional
    @ResponseBody
    public AjaxResult saveTransCode(Entrust entrust){
        Entrust entrustCur = entrustService.selectEntrustById(entrust.getEntrustId());
        //修改运单运输方式
        EntrustLot entrustLot = new EntrustLot();
        entrustLot.setEntrustLotId(entrustCur.getLotId());
        entrustLot.setTransType(entrust.getTransCode());
        int i = entrustLotService.updateEntrustLot(entrustLot);
        if(i != 1){
            throw new BusinessException("修改失败");
        }
        //修改运单下所有委托单
        List<Entrust> entrustList = entrustService.selectEntrustListByLotId(entrustCur.getLotId());
        for(Entrust entrust1 : entrustList){
            entrust1.setTransCode(entrust.getTransCode());
            //查询委托单对应业务员名称
            String serviceName = entrustMapper.selectServiceNameByTransCodeAndCustomerId(entrust1.getCustomerId(), entrust1.getTransCode());
            entrust1.setServiceName(serviceName);
            entrustService.updateEntrust(entrust1);
        }
        return AjaxResult.success("修改成功");
    }

    @RequestMapping("/advancePrint")
    public String advancePrint(ModelMap map,String carrierId,BigDecimal amount,Integer payType) {
        map.put("carrierId", carrierId);
        Carrier carrier = carrierService.selectCarrierById(carrierId);
        map.put("carrier", carrier);
        BigDecimal unDcAmount = carrier.getAdvancePayMoney().add(carrier.getAdvancePayMoneyXj());
        unDcAmount = unDcAmount.divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_UP);
        map.put("unDcAmount", unDcAmount);
        map.put("date",DateFormatUtils.format(new Date(),"yyyy-MM-dd"));
        //金额
        map.put("amount", amount);
        //付款类型
        map.put("payType", payType);
        //合计未付款单量/金额
        Map<String, Object> ungotMap = payDetailMapper.selectUnPayCntAndAmountByCarrierId(carrierId);
        map.put("ungotAmount", new BigDecimal(ungotMap.get("AMOUNT").toString()).divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_UP));
        map.put("ungotCnt", ungotMap.get("CNT"));
        //客户维度
        List<Map<String, Object>> ungotCustList = payDetailMapper.selectUnPayCustAndAmountByCarrierId(carrierId);

        List<Map<String, Object>> ungotCustListCp = new ArrayList<>();
        for(int i = 0 ; i < ungotCustList.size() ; i++){
            if(i % 2 == 0){
                Map<String, Object> dist = ungotCustList.get(i);
                if(i+1< ungotCustList.size()){
                    Map<String, Object> dist_next = ungotCustList.get(i+1);
                    dist.put("DEPT_NAME_NEXT",dist_next.get("DEPT_NAME"));
                    dist.put("CUST_ABBR_NEXT",dist_next.get("CUST_ABBR"));
                    dist.put("AMOUNT_NEXT",dist_next.get("AMOUNT"));
                }else{
                    dist.put("DEPT_NAME_NEXT","");
                    dist.put("CUST_ABBR_NEXT","");
                    dist.put("AMOUNT_NEXT",BigDecimal.ZERO);
                }
                ungotCustListCp.add(dist);
            }
        }
        map.put("ungotCustList",ungotCustListCp);


        //对账未确认单量
        Map<String, Object> unConfrimMap = payDetailMapper.selectUnConfirmCntAndAmountByCarrier(carrierId);
        map.put("unConfrimAmount", new BigDecimal(unConfrimMap.get("AMOUNT").toString()).divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_UP));
        map.put("unConfrimCnt", unConfrimMap.get("CNT"));
        List<Map<String, Object>> unConfrimDetailMap = payDetailMapper.selectUnConfirmDetailAndAmountByCarrier(carrierId);
        List<Map<String, Object>> unConfrimDetailMapCp = new ArrayList<>();
        for(int i = 0 ; i < unConfrimDetailMap.size() ; i++){
            if(i % 2 == 0){
                Map<String, Object> dist = unConfrimDetailMap.get(i);
                if(i+1< unConfrimDetailMap.size()){
                    Map<String, Object> dist_next = unConfrimDetailMap.get(i+1);
                    dist.put("VBILLNO_NEXT",dist_next.get("VBILLNO"));
                    dist.put("YEARMONTH_NEXT",dist_next.get("YEARMONTH"));
                    dist.put("AMOUNT_NEXT",dist_next.get("AMOUNT"));
                }else{
                    dist.put("VBILLNO_NEXT","");
                    dist.put("YEARMONTH_NEXT","");
                    dist.put("AMOUNT_NEXT",BigDecimal.ZERO);
                }
                unConfrimDetailMapCp.add(dist);
            }
        }
        map.put("unConfrimDetailMap", unConfrimDetailMapCp);

        //查询对账已确认单量
        Map<String, Object> confrimMap = payDetailMapper.selectConfirmCntAndAmountByCarrier(carrierId);
        map.put("confrimAmount", new BigDecimal(confrimMap.get("AMOUNT").toString()).divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_UP));
        map.put("confrimCnt", confrimMap.get("CNT"));
        List<Map<String, Object>> confrimDetailMap = payDetailMapper.selectConfirmDetailAndAmountByCarrier(carrierId);
        List<Map<String, Object>> confrimDetailMapCp = new ArrayList<>();
        for(int i = 0 ; i < confrimDetailMap.size() ; i++){
            if(i % 2 == 0){
                Map<String, Object> dist = confrimDetailMap.get(i);
                if(i+1< confrimDetailMap.size()){
                    Map<String, Object> dist_next = confrimDetailMap.get(i+1);
                    dist.put("VBILLNO_NEXT",dist_next.get("VBILLNO"));
                    dist.put("YEARMONTH_NEXT",dist_next.get("YEARMONTH"));
                    dist.put("AMOUNT_NEXT",dist_next.get("AMOUNT"));
                }else{
                    dist.put("VBILLNO_NEXT","");
                    dist.put("YEARMONTH_NEXT","");
                    dist.put("AMOUNT_NEXT",BigDecimal.ZERO);
                }
                confrimDetailMapCp.add(dist);
            }
        }
        map.put("confrimDetailMap", confrimDetailMapCp);
        //查询未对账单量
        Map<String, Object> unPackageMap = payDetailMapper.selectUnPackageCntAndAmountByCarrierId(carrierId);
        map.put("unPackageAmount", new BigDecimal(unPackageMap.get("AMOUNT").toString()).divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_UP));
        map.put("unPackageCnt", unPackageMap.get("CNT"));
        return PREFIX + "/advance_pay_print";
    }

    /**
     * 承运商协议价
     * @param carrierPeriodDTO
     * @return
     */
    @RequestMapping("/carrierPeriodQuery")
    @ResponseBody
    public AjaxResult carrierPeriodQuery(@RequestBody CarrierPeriodDTO carrierPeriodDTO){
        int periodType = -1;
        BigDecimal compareNum = null;
        int priceType = -1;
        String billingMethod = null;
        if(carrierPeriodDTO.getPricingMethod().equals("0")){
            //按吨
            periodType = 1;
            priceType = 1;
            compareNum = carrierPeriodDTO.getWeightCount();
            billingMethod = "1";
        }else if(carrierPeriodDTO.getPricingMethod().equals("1")){
            //按方
            periodType = 2;
            priceType = 1;
            compareNum = carrierPeriodDTO.getVolumeCount();
            billingMethod = "2";
        }else if(carrierPeriodDTO.getPricingMethod().equals("2")){
            //按件
            periodType = 3;
            priceType = 1;
            compareNum = carrierPeriodDTO.getNumCount();
            billingMethod = "5";
        }else if(carrierPeriodDTO.getPricingMethod().equals("4")){
            //按吨(包车)
            periodType = 1;
            priceType = 2;
            compareNum = carrierPeriodDTO.getWeightCount();
        }else if(carrierPeriodDTO.getPricingMethod().equals("5")){
            //按方(包车)
            periodType = 2;
            priceType = 2;
            compareNum = carrierPeriodDTO.getVolumeCount();
        }else if(carrierPeriodDTO.getPricingMethod().equals("6")){
            //按件(包车)
            periodType = 3;
            priceType = 2;
            compareNum = carrierPeriodDTO.getNumCount();
        }else{
            return AjaxResult.error("计价方式需要为按吨、按件、 按方");
        }

        //首先查询客户自动调度配置的协议价
        List<Entrust> entrusts = entrustService.selectEntrustListByLotId(carrierPeriodDTO.getLotId());
        if(entrusts.size() == 1){
            Entrust entrust = entrusts.get(0);
            Invoice invoice = invoiceService.selectInvoiceById(entrust.getOrderno());

            String goodsName = null;
            List<SegPackGoods> segPackGoods = segPackGoodsMapper.selectSegPackGoodsBySegmentId(entrust.getSegmentId());
            List<String> goodsNames = segPackGoods.stream().map(SegPackGoods::getGoodsName).distinct().collect(Collectors.toList());
            if (goodsNames.size() == 1) {
                goodsName = goodsNames.get(0);
            }

            Map<String, Object> priceRes = autoDispatchConfigService.getPrice(entrust.getCustomerId()
                    , carrierPeriodDTO.getDeliAreaList(), carrierPeriodDTO.getArriAreaList(), invoice.getBillingMethod()
                    , invoice.getCarLen(), invoice.getCarType(), invoice.getUnitPrice(), invoice.getCostAmount()
                    , carrierPeriodDTO.getNumCount().doubleValue(), carrierPeriodDTO.getWeightCount().doubleValue()
                    , carrierPeriodDTO.getVolumeCount().doubleValue(), null, entrust.getCarrierId()
                    , carrierPeriodDTO.getArriAddrName(), null, goodsName, carrierPeriodDTO.getAutoDisVersion()
                    , invoice.getOtherFeeType(), invoice.getOtherFee(), invoice.getTransCode(),invoice.getIsRoundTrip());
            if("1".equals(priceRes.get("type"))){
                List<Map<String, String>> dataList = (List<Map<String, String>>)priceRes.get("dataList");
                if(dataList.size() > 1){
                    //判断总价是否一致
                    BigDecimal payTotalFee = new BigDecimal(dataList.get(0).get("payTotalFee"));
                    for(int j = 1 ; j < dataList.size() ; j++){
                        BigDecimal tempFee = new BigDecimal(dataList.get(j).get("payTotalFee"));
                        if(tempFee.compareTo(payTotalFee) != 0){
                            return AjaxResult.warn("自动配置协议价出现多条错误！");
                        }
                    }
                }
                return AjaxResult.success("自动调度配置协议价",dataList.get(0).get("payTotalFee"));
            }
        }

        Date curRegDate = entrusts.get(0).getRegDate(); // 运单创建时间
        final BigDecimal totalPrice = carrierPeriodService.historyCarrierPeriodPrice(carrierPeriodDTO.getCarrierId(), carrierPeriodDTO.getDeliArea(), carrierPeriodDTO.getArriArea(),
                periodType, priceType, carrierPeriodDTO.getGoodsKind(), curRegDate, compareNum);
        if (totalPrice == null) {
            return AjaxResult.error();
        } else {
            return AjaxResult.success(totalPrice);
        }

        /*List<Map<String, Object>> maps = carrierPeriodMapper.carrierPeriodQuery(carrierPeriodDTO.getCarrierId(), carrierPeriodDTO.getDeliArea(), carrierPeriodDTO.getArriArea(),periodType,priceType,carrierPeriodDTO.getGoodsKind());

        if(maps == null || maps.size() == 0){
            return AjaxResult.error();
        }else{
            for(Map<String, Object> map : maps){
                if(map.containsKey("startFlag") && map.containsKey("periodStart")){
                    String startFlag = map.get("startFlag").toString();
                    BigDecimal periodStart = new BigDecimal(map.get("periodStart").toString());
                    if(startFlag.equals("62")){
                        if( compareNum.compareTo(periodStart) <= 0)  continue;
                    }else if(startFlag.equals("123")){
                        if( compareNum.compareTo(periodStart) < 0)  continue;
                    }
                }
                if(map.containsKey("endFlag") && map.containsKey("periodEnd")){
                    String endFlag = map.get("endFlag").toString();
                    BigDecimal periodEnd = new BigDecimal(map.get("periodEnd").toString());
                    if(endFlag.equals("60")){
                        if(compareNum.compareTo(periodEnd) >= 0) continue;
                    }else if(endFlag.equals("121")){
                        if(compareNum.compareTo(periodEnd) > 0) continue;
                    }
                }
                BigDecimal price = new BigDecimal(map.get("price").toString());
                if(map.get("priceType").toString().equals("1")){
                    //单价
                    BigDecimal totalPrice = price.multiply(compareNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                    //查看最低价
                    if(!map.containsKey("minPrice") || StringUtils.isBlank(map.get("minPrice").toString())){
                        return AjaxResult.success(totalPrice);
                    }
                    BigDecimal minPrice = new BigDecimal(map.get("minPrice").toString());
                    if(minPrice != null && minPrice.compareTo(totalPrice) > 0){
                        return AjaxResult.success(minPrice);
                    }else{
                        return AjaxResult.success(totalPrice);
                    }
                }else if(map.get("priceType").toString().equals("2")){
                    //包车
                    return AjaxResult.success(price);
                }
            }
        }
        return AjaxResult.error();*/
    }

    @RequestMapping("/editArriPic")
    public String editArriPic(ModelMap map,String entrustId){
        //委托单ID
        map.put("entrustId", entrustId);
        //获取委托单明细
        EntrustDto entrustDto = traceService.selectEntrustWorkById(entrustId);
        map.put("entrustDto", entrustDto);
        map.put("pic", EntrustPicEnum.getDeliPicEnum());//到货图片

        //获取提货信息 1:提货 2:到货
        List<EntrustWork> entrustWorkPickList = traceService.selectEntrustWorkInfoById(entrustId, "1");
        if( entrustWorkPickList != null && entrustWorkPickList.size() > 0 ){
            map.put("entrustPickWork", entrustWorkPickList.get(0));
            //提货图片
            List<EntrustWorkPic> pickPicList = traceService.selectEntrustWorkPicById(entrustWorkPickList.get(0).getEntrustWorkId());
            map.put("pickPicList", pickPicList);
        }else{
            map.put("entrustPickWork", new EntrustWork());
            map.put("pickPicList", new ArrayList<EntrustWorkPic>());
        }
        List<EntrustWork> entrustWorkList = traceService.selectEntrustWorkInfoById(entrustId, "2");
        if( entrustWorkList != null && entrustWorkList.size() > 0 ){
            EntrustWork entrustWork = entrustWorkList.get(0);
            map.put("entrustWork", entrustWork);
            //到货图片
            List<EntrustWorkPic> picList = traceService.selectEntrustWorkPicById(entrustWork.getEntrustWorkId());
            map.put("picList", picList);
            /*
             * todo: 上传对单照片 APP改好了撤销
             */
            List<SysUploadFile> sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid("");
            String tid = "";
            if(!picList.isEmpty()){
                for(EntrustWorkPic entrustWorkPic:picList){
                    if("3".equals(entrustWorkPic.getWorkAppendixType())){
                        sysUploadFiles = sysUploadFileService.selectSysUploadFileByTid(entrustWorkPic.getAppendixId());
                        tid = entrustWorkPic.getAppendixId();
                    }
                }
            }
            map.put("sysUploadFiles",sysUploadFiles);
            map.put("tid",tid);

            //判断是否要回单且是否有到货作业上传回单照片
            //3为中转站不需要回单
            if("3".equals(entrustDto.getAddrType())){
                map.put("receiptErrorFlag","0");
            }else{
                //判断是否存在回单照片
                String receiptErrorFlag = "1";
                for(EntrustWorkPic entrustWorkPic : picList){
                    if(EntrustPicEnum.CERTPIC.getValue().equals(entrustWorkPic.getWorkAppendixType())){
                        receiptErrorFlag = "0";
                    }
                }
                map.put("receiptErrorFlag",receiptErrorFlag);
            }
        }else{
            map.put("entrustWork", new EntrustWork());
            map.put("picList", new ArrayList<EntrustWorkPic>());
            if("3".equals(entrustDto.getAddrType())){
                map.put("receiptErrorFlag","0");
            }else{
                map.put("receiptErrorFlag","1");
            }
        }
        //获取货品明细
        EntPackGoods entPackGoods = new EntPackGoods();
        entPackGoods.setEntrustId(entrustId);
        entPackGoods.setDelFlag(0);
        List<EntPackGoods> entPackGoodsList = entPackGoodsService.selectEntPackGoodsList(entPackGoods);
        map.put("entPackGoodsList", entPackGoodsList);
        //查询应付
        List<PayDetail> payDetailList = payDetailService.selectPayDetailListByLotId(entrustDto.getLotId());
        map.put("payDetailList", payDetailList);
        //查询应收
        List<ReceiveDetailVO> receiveDetailList = receiveDetailService.selectReceiveByInvoiceId(entrustDto.getOrderno());
        map.put("receiveDetailList", receiveDetailList);

        return "tms/trace/receiptConfirmModel";
    }

    /**
     * 跳转反核销页面
     * @param map
     * @param payDetailId
     * @return
     */
    @GetMapping("/lockPayDetailPage/{lotId}")
    public String backWritePage(ModelMap map,@PathVariable String lotId) {
        map.put("lotId", lotId);
        return PREFIX + "/lock_page";
    }

    /**
     * 跳转预约送货页面
     * @param map
     * @return
     */
    @GetMapping("/bookingSend")
    public String bookingSend(ModelMap map, String entrustId) {
        map.put("entrustId", entrustId);
        //查询是否预约过
        BookingSend bookSend = new BookingSend();
        bookSend.setEntrustId(entrustId);
        bookSend.setType("1");
        List<BookingSend> bookingSends = bookingSendService.selectAllList(bookSend);
        if(bookingSends != null && bookingSends.size() > 0){
            map.put("bookSend", bookingSends.get(0));
        }else{
            map.put("bookSend", new BookingSend());
        }
        return PREFIX + "/booking_send";
    }

    @RequestMapping("/saveBookingSend")
    @ResponseBody
    @Transactional
    public AjaxResult saveBookingSend(BookingSend bookingSend){
        if(StringUtils.isBlank(bookingSend.getId())){
            if(bookingSend.getType().equals("4") || bookingSend.getType().equals("5")){
                if(bookingSend.getFixAmount() != null && bookingSend.getFixAmount().compareTo(BigDecimal.ZERO) != 0) {
                    Entrust entrust = entrustService.selectEntrustById(bookingSend.getEntrustId());
                    AddRegisterDto addRegisterDto = new AddRegisterDto();
                    addRegisterDto.setEntrustId(entrust.getEntrustId());
                    addRegisterDto.setEntrustNo(entrust.getVbillno());
                    addRegisterDto.setEntrustLotId(entrust.getLotId());
                    addRegisterDto.setLot(entrust.getLot());
                    addRegisterDto.setAppendixId(bookingSend.getPicTid());
                    List<OtherFee> otherFeeList = new ArrayList<>();
                    OtherFee otherFee = new OtherFee();
                    otherFee.setLotId(entrust.getOrderno());
                    //维修费
                    if (bookingSend.getType().equals("4")) {
                        otherFee.setFeeType("36");
                    } else if (bookingSend.getType().equals("5")) {
                        otherFee.setFeeType("1");
                    }
                    otherFee.setFeeAmount(bookingSend.getFixAmount());
                    otherFee.setMemo(bookingSend.getRemark());
                    otherFeeList.add(otherFee);
                    addRegisterDto.setOtherFeeList(otherFeeList);
                    addRegisterDto.setParams(new ArrayList<>());
                    addRegister(addRegisterDto);
                }
            }
            bookingSend.setId(IdUtil.simpleUUID());
            return toAjax(bookingSendService.insertSelective(bookingSend));
        }else{
            return toAjax(bookingSendService.updateByPrimaryKeySelective(bookingSend));
        }
    }

    /**
     * 跳转预约送货页面
     * @param map
     * @return
     */
    @GetMapping("/install")
    public String install(ModelMap map, String entrustId) {
        map.put("entrustId", entrustId);
        List<BookingSend> bookingSends = bookingSendService.selectAllByEntrustId(entrustId);
        if (bookingSends != null && bookingSends.size() > 0) {
            bookingSends.sort(Comparator.comparing(BookingSend::getOperateTime));
            map.put("time", bookingSends.get(0).getOperateTime());
        }
        for (BookingSend bookingSend : bookingSends) {
            Map<String, Object> params = new HashMap<>();

            if(StringUtils.isNotBlank(bookingSend.getVideoTid())){
                List<SysUploadFile> videoFiles = sysUploadFileService.selectSysUploadFileByTid(bookingSend.getVideoTid());
                params.put("videoFiles", videoFiles);
            }

            if(StringUtils.isNotBlank(bookingSend.getPicTid())) {
                List<SysUploadFile> picFiles = sysUploadFileService.selectSysUploadFileByTid(bookingSend.getPicTid());
                params.put("picFiles", picFiles);
            }
            bookingSend.setParams(params);
        }
        map.put("bookingSends", bookingSends);
        return PREFIX + "/install";
    }




    @RequestMapping("/getEntrustByLotId")
    @ResponseBody
    public AjaxResult getEntrustByLotId(String entrustLotId){
        List<Entrust> entrusts = entrustService.selectEntrustListByLotId(entrustLotId);
        return AjaxResult.success(entrusts);
    }

    @RequestMapping("/getLotInfoByLotId")
    @ResponseBody
    public AjaxResult getLotInfoByLotId(String entrustLotId){
        EntrustLot entrustLot = entrustLotMapper.selectEntrustLotById(entrustLotId);
        return AjaxResult.success(entrustLot);
    }

    /**
     * 跳转预约送货页面
     * @param map
     * @return
     */
    @GetMapping("/changeAddress")
    public String changeAddress(ModelMap map, String entrustId) {
        Entrust entrust = entrustService.selectEntrustById(entrustId);
        map.put("entrust", entrust);
        map.put("entrustId", entrustId);
        //查询地址
        List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService.selectAddrAndGoodsByEntrustId(entrustId);
        multipleShippingAddresses.forEach(x -> x.getShippingGoodsList().forEach(y -> {
            Map<String, Object> param = new HashMap<>();
            param.put("uid", IdUtil.randomUUID().substring(0, 8));
            y.setParams(param);
        }));

        map.put("multipleShippingAddresses",multipleShippingAddresses);


        double numCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(1))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getNum).sum();

        double weightCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(1))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getWeight).sum();

        double volumeCount = multipleShippingAddresses.stream()
                .filter(x -> x.getAddressType().equals(1))
                .flatMap(x -> x.getShippingGoodsList().stream())
                .mapToDouble(MultipleShippingGoods::getVolume).sum();
        map.put("numCount", numCount);
        map.put("weightCount", weightCount);
        map.put("volumeCount", volumeCount);

        map.put("addr_index", multipleShippingAddresses.size());


        String invoiceGoodsId = "";

        List<Integer> fhdzAddrList = new ArrayList<>();
        List<Integer> shdzAddrList = new ArrayList<>();
        Map<Object, List<Map<String, Object>>> addrGoodsIndex = new HashMap<>();
        List<String> areaIds = new ArrayList<>();
        for (int i = 0; i < multipleShippingAddresses.size(); i++) {
            if (multipleShippingAddresses.get(i).getAddressType() == 0) {
                fhdzAddrList.add(i);
            }else {
                shdzAddrList.add(i);
                areaIds.add(multipleShippingAddresses.get(i).getAreaId());
                invoiceGoodsId = multipleShippingAddresses.get(i).getShippingGoodsList().get(0).getInvoiceGoodsId();
            }

            //货品list
            List<MultipleShippingGoods> shippingGoodsList = multipleShippingAddresses.get(i).getShippingGoodsList();
            List<Map<String, Object>> goodsList = new ArrayList<>();
            for (int j = 0; j < shippingGoodsList.size(); j++) {
                Map<String, Object> goodsMap = new HashMap<>();
                goodsMap.put("id", j);
                goodsMap.put("value", shippingGoodsList.get(j).getParams().get("uid"));
                goodsList.add(goodsMap);
            }
            addrGoodsIndex.put(i, goodsList);
        }
        map.put("invoiceGoodsId",invoiceGoodsId);
        map.put("areaIds",areaIds);
        //发货单地址的集合
        map.put("fhdz_addr_list", fhdzAddrList);
        //收货地址的集合
        map.put("shdz_addr_list", shdzAddrList);
        //每个地址对应的货品下标
        map.put("addr_goods_index", addrGoodsIndex);
        return PREFIX + "/change_address";
    }

    /**
     * 委托单地址
     * @param entrustAddressEditVO 发货单对象
     * @return AjaxResult
     */
    @RepeatSubmit
    @Log(title = "委托单", businessType = BusinessType.UPDATE)
    @PostMapping("/changeAddress")
    @ResponseBody
    public AjaxResult changeAddress(EntrustAddressEditVO entrustAddressEditVO) {
        return traceService.changeAddress(entrustAddressEditVO);
    }

    @PostMapping("/checkIfOneAddrByEntrustId")
    @ResponseBody
    public AjaxResult checkIfOneAddrByEntrustId(String entrustId){
        List<MultipleShippingAddress> multipleShippingAddressList = multipleShippingAddressService.selectAddrAndGoodsByEntrustId(entrustId);
        Set<String> address = new HashSet<String>();
        for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddressList){
            if(multipleShippingAddress.getAddressType() == 1){
                multipleShippingAddress.getShippingGoodsList().forEach(s -> address.add(s.getInvoiceGoodsId()));
            }
        }
        if(address.size() == 1){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }


    @PostMapping("/checkException")
    @ResponseBody
    public AjaxResult checkException(String lotId){
        //查询运单下所有委托单
        Entrust entrustAll = new Entrust();
        entrustAll.setLotId(lotId);
        entrustAll.setDelFlag(0);
        List<Entrust> entrustAllList = entrustMapper.selectEntrustList(entrustAll);

        for (Entrust ent : entrustAllList) {
            TEntrustExpExample entrustExpExample = new TEntrustExpExample();
            entrustExpExample.createCriteria().andEntrustIdEqualTo(ent.getEntrustId()).andDelFlagEqualTo((short) 0);
            List<TEntrustExp> tEntrustExps = tEntrustExpMapper.selectByExample(entrustExpExample);
            if (tEntrustExps.size() > 0) {
                return AjaxResult.error("存在异常数据，无法操作，请联系质控或保险专员。");
            }
        }

        TExpEntrustMoreExample tExpEntrustMoreExample = new TExpEntrustMoreExample();
        tExpEntrustMoreExample.createCriteria().andDelFlagEqualTo((short) 0).andLotIdEqualTo(lotId);
        List<TExpEntrustMore> tExpEntrustMores = tExpEntrustMoreMapper.selectByExample(tExpEntrustMoreExample);
        if (tExpEntrustMores.size() > 0) {
            return AjaxResult.error("存在异常数据，无法操作，请联系质控或保险专员。");
        }
        return AjaxResult.success();
    }

   /* @RequestMapping("/sunshineArrival")
    @ResponseBody
    private AjaxResult sunshineArrival(String entrustVbillnos){
        String[] split = entrustVbillnos.split(",");
        for(String vbillno : split){
          traceService.addReceiptConfirm(null);
           *//* EntrustWork entrustWorkSel = new EntrustWork();
            entrustWorkSel.setEntrustId(entrust.getEntrustId());
            entrustWorkSel.setWorkType("2");
            List<EntrustWork> entrustWorks = entrustWorkMapper.selectEntrustWorkList(entrustWorkSel);
            if(entrustWorks == null || entrustWorks.size() != 1){
                throw new BusinessException("查询到0条或多条到货信息，无法推送");
            }
            EntrustWork entrustWork = entrustWorks.get(0);
            asyncJobService.sunshineArrival(entrust.getEntrustId(),entrustWork.getActArriDate(),entrustWork.getActLeaveDate());*//*
        }
        return AjaxResult.success();
    }*/

    /**
     * 费用确认页面
     * @return
     */
    @RequestMapping("/sunshineBackRecord")
    public String sunshineBackRecord(ModelMap map) {
        return "tms/trace/sunshine_back_record";
    }

    @RequestMapping("/zllgBackRecord")
    public String zllgBackRecord(ModelMap map) {
        return "tms/trace/zllg_back_record";
    }

    @RequestMapping("/zjhyRecord")
    public String zjhyRecord(ModelMap map) {
        return "tms/trace/zjhy_record";
    }




    /**
     * 承运商对账列表
     *
     * @return
     */
    @PostMapping("/sunshineBackRecordList")
    @ResponseBody
    public TableDataInfo sunshineBackRecordList(SunshineBackRecord sunshineBackRecord) {
        startPage();
        List<SunshineBackRecord> list = traceService.sunshineBackRecordList(sunshineBackRecord);
        return getDataTable(list);
    }


    /**
     * 承运商对账列表
     *
     * @return
     */
    @PostMapping("/zjhyRecordList")
    @ResponseBody
    public TableDataInfo zjhyRecordList(HYInvoiceRecord hyInvoiceRecord) {
        startPage();
        List<HYInvoiceRecord> list = traceService.zjhyRecordList(hyInvoiceRecord);
        return getDataTable(list);
    }


    /**
     * 承运商对账列表
     *
     * @return
     */
    @PostMapping("/zllgBackRecordList")
    @ResponseBody
    public TableDataInfo zllgBackRecordList(ZLLGBackRecord zllgBackRecord) {
        startPage();
        List<ZLLGBackRecord> list = traceService.zllgBackRecordList(zllgBackRecord);
        return getDataTable(list);
    }

    @PostMapping("/zllgBackRecordHandList")
    @ResponseBody
    public TableDataInfo zllgBackRecordHandList(ZLLGBackRecord zllgBackRecord) {
        startPage();
        List<ZLLGBackRecord> list = traceService.zllgBackRecordHandList(zllgBackRecord);
        return getDataTable(list);
    }


    /**
     * 导出数据
     */
    @PostMapping("/sunshineBackRecordExport")
    @ResponseBody
    public AjaxResult exportData(SunshineBackRecord sunshineBackRecord) {
        List<SunshineBackRecord> list = traceService.sunshineBackRecordList(sunshineBackRecord);
        ExcelUtil<SunshineBackRecord> util = new ExcelUtil<SunshineBackRecord>(SunshineBackRecord.class);
        return util.exportExcel(list, "阳光操作记录");
    }

    @PostMapping("/zjhyReceipt")
    @ResponseBody
    public AjaxResult zjhyReceipt(String invoiceVbillnos) {
        String[] split = invoiceVbillnos.split(",");
        for(String vbillno : split){
            Invoice invoice = invoiceMapper.selectInvoiceByInvoiceNo(vbillno);
            List<Entrust> entrustList = entrustMapper.selectEntrustListByInvoiceId(invoice.getInvoiceId());
            if(entrustList != null && entrustList.size() != 0){
                asyncJobService.zjhyReceiptOnly(entrustList.get(0).getEntrustId());
            }else{
                throw new BusinessException("找到多个委托单，无法推送");
            }
        }
        return AjaxResult.success("推送成功，请手动刷新查询推送结果。");
    }

    @GetMapping("/close/{id}")
    public String close(ModelMap map, @PathVariable String id) {
        map.put("entrustId", id);

        Entrust entrust = entrustMapper.selectEntrustById(id);

        List<Entrust> entrusts = entrustMapper.selectEntrustListByInvoiceId(entrust.getOrderno());
        //应收
        List<ReceiveDetailVO> receiveDetailVOS = receiveDetailService.selectReceiveByInvoiceId(entrust.getOrderno());
        BigDecimal totalReceiveAmount = receiveDetailVOS.stream().map(ReceiveDetailVO::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //三方
        List<OtherFee> otherFeeList = otherFeeMapper.selectOtherFeeListByInvoiceId(entrust.getOrderno());
        BigDecimal totalOtherFeeAmount = otherFeeList.stream().map(OtherFee::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //应付
        List<PayDetail> payDetailList = payDetailMapper.selectPayDetailListByLotId(entrust.getLotId());
        BigDecimal totalPayDetailAmount = payDetailList.stream().map(PayDetail::getTransFeeCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<String,Object> res = new HashMap<>();
        String joinedLot = entrusts.stream()
                .map(Entrust::getLot)
                .collect(Collectors.joining(", "));

        map.put("lot",joinedLot);
        map.put("totalReceiveAmount",totalReceiveAmount);
        map.put("totalOtherFeeAmount",totalOtherFeeAmount);
        map.put("totalPayDetailAmount",totalPayDetailAmount);
        map.put("invoiceVbillno",entrust.getInvoiceVbillno());

        return PREFIX + "/close";
    }
}