package com.ruoyi.tms.vo.invoice;

import com.ruoyi.tms.vo.client.CustMiscFeeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 发货单确认vo
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-07-26 16:42
 */
@Data
public class InvoiceAffirmVO {
    /** 发货单ID */
    private String invoiceId;
    /** 发货单号 */
    private String vbillno;

    /** 运段ID */
    private String segmentId;

    /** 提货地址*/
    private String deliAddr;
    /** 到货地址*/
    private String arriAddr;

    /** 货物名称  */
    private String goodsName;

    /** 计费方式  */
    private Integer billingMethod;
    /** 计费方式  */
    private String billingMethodName;

    /** 总应收 */
    private BigDecimal totalFee;
    /** 单价  */
    private BigDecimal unitPrice;

    /** 拆段中转点*/
    private String autoSubsectionAddrId;
    private String autoSubsectionAddrName;

    /** 自动调度时 是否可以录入送货费 0不可以 1可以*/
    private Integer autoDispatchDeliFee;
    /** 自动调度时 是否可以录入提货费 0不可以 1可以*/
    private Integer autoDispatchPickUpFee;

    /** 用于计算负毛利*/
    private BigDecimal profit;

    /** 默认开票类型：4=增值税专用发票（9%） */
    private String billingType;

    /** 自动调度配置*/
    private List<InvoiceAffirmVO.AutoDispatchConfigVO> autoConfigList;

    /** 确认时的传参*/
    private List<InvoiceAffirmVO.AffirmVO> affirmList;

    /** 在途的应收*/
    private List<CustMiscFeeVO> ysMiscFeeList;
    /** 在途的应收*/
    private List<CustMiscFeeVO> yfMiscFeeList;

    /**
     * 自动调度配置
     */
    @Data
    public static class AutoDispatchConfigVO {
        /** 自动调度配置id */
        private String autoDispatchConfigId;
        /** 承运商名称 */
        private String carrName;
        private String carrierId;
        /** 总应付 */
        private BigDecimal payTotalFee;
        /** 应付单价  */
        private BigDecimal payUnitPrice;

        private String hasOnWay;
        private BigDecimal otherFee;
        private String otherFeeType;
        private String otherFeeTypeText;

        private BigDecimal oilAmount;
        private BigDecimal cashAmount;

        private String billingType;

        /**
         * 1 车辆司机必填
         * 2 车辆必填
         * 3 司机必填
         * 4 都不必填
         */
        private Integer requiredFlag;

    }
    /**
     * 确认时的传参
     */
    @Data
    public static class AffirmVO {
        /** 发货单ID */
        private String invoiceId;
        private String segmentId;

        /** 是否自动调度  0否 1是*/
        private Integer isAutoDispatch;
        /** 是否自动拆段  0否 1是*/
        private Integer isSubsection;

        /** 自动调度配置id */
        private String autoDispatchConfigId;
        /** 总应付 */
        private BigDecimal payTotalFee;
        /** 应付单价  */
        private BigDecimal payUnitPrice;
        /** 计费方式  */
        private Integer billingMethod;
        /** 车辆id  */
        private String carnoId;
        /** 司机id  */
        private String driverId;
        /** 送货费*/
        private BigDecimal deliveryFee;
        /** 提货费*/
        private BigDecimal pickUpFee;
        /** */
        private String otherFeeType;
        private BigDecimal otherFee;

        /** 收款人*/
        private String carrBankId;

    }

    private Boolean process; // 是否走审批流程
}
