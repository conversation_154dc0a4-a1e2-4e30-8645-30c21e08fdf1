package com.ruoyi.tms.service.client.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.domain.client.*;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.mapper.basic.ProvinceMapper;
import com.ruoyi.tms.mapper.client.*;
import com.ruoyi.tms.service.client.IContractpcVersionService;
import com.ruoyi.tms.service.client.ICustContractpcService;
import com.ruoyi.tms.vo.client.*;
import com.ruoyi.util.ShiroUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import com.ruoyi.common.core.text.Convert;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客户合同价目 服务层实现
 *
 * <AUTHOR>
 * @date 2019-09-18
 */
@Service
public class CustContractpcServiceImpl implements ICustContractpcService {

    @Autowired
    private CustContractpcMapper custContractpcMapper;

    @Autowired
    private ContractpcSectionMapper contractpcSectionMapper;
    @Autowired
    private ShiroUtils shiroUtils;
    @Autowired
    private ProvinceMapper provinceMapper;
    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private ContractpcVersionMapper contractpcVersionMapper;
    @Autowired
    private IContractpcVersionService contractpcVersionService;
    @Autowired
    private ContractpcCostPriceLogMapper contractpcCostPriceLogMapper;



    /**
     * 根据客户Id查询合同价
     * @param clientId 客户id
     * @return 合同价集合
     */
    @Override
    public List<CustContractpc> selectCustContractpc(String clientId) {
        // 根据客户ID查询合同价
        List<CustContractpc> custContractpcs = custContractpcMapper.selectCustContractpc(clientId);
        // 若没有结果则保存一个空的结果在页面展示
        if (custContractpcs.size() == 0) {
            custContractpcs.add(new CustContractpc());
        }
        // 根据合同Id查询合同价的区间进行封装
        ContractpcSection contractpcSection = new ContractpcSection();
        for (CustContractpc custContractpc : custContractpcs) {
            contractpcSection.setCustContractpcId(custContractpc.getCustContractpcId());
            // 合同价的区间集合
            List<ContractpcSection> contractpcSections = contractpcSectionMapper.selectContractpcSectionList(contractpcSection);
            // 若没有结果则保存一个空的结果在页面展示
            if (contractpcSections.size() == 0) {
                contractpcSections.add(new ContractpcSection());
            }
            custContractpc.setContractpcSectionList(contractpcSections);
        }
        return custContractpcs;
    }

    @Override
    public List<CustContractpcVO> selectCustContractpcVoList(CustContractpcVO custContractpcVO) {
        // 根据客户ID查询合同价
        List<CustContractpcVO> custContractpcs = custContractpcMapper.selectCustContractpcVoList(custContractpcVO);

        // 根据合同Id查询合同价的区间进行封装
        ContractpcSection contractpcSection = new ContractpcSection();
        for (CustContractpcVO custContractpc : custContractpcs) {
            if ("1".equals(custContractpc.getIfSection())) {
                contractpcSection.setCustContractpcId(custContractpc.getCustContractpcId());
                contractpcSection.setDelFlag(0);
                // 合同价的区间集合
                List<ContractpcSection> contractpcSections = contractpcSectionMapper.selectContractpcSectionList(contractpcSection);
                contractpcSections.sort(
                        Comparator.comparing(
                                ContractpcSection::getStartKil,
                                Comparator.nullsFirst(BigDecimal::compareTo)
                        ).thenComparing(
                                ContractpcSection::getStartSection,
                                Comparator.nullsFirst(BigDecimal::compareTo)
                        )
                );
                for (ContractpcSection section : contractpcSections) {
                    //0:大于 1: 大于等于
                    String start = section.getStartOperator() == 0 ? "<" : "<=";
                    //2:小于 3 小于等于
                    String end = section.getEndOperator() == 2 ? "<" : "<=";
                    section.setSectionText((section.getStartSection() == null ? "∞" : section.getStartSection()) + start
                            + "x" + end + (section.getEndSection() == null ? "∞" : section.getEndSection()));

                    if (custContractpc.getBillingMethod() == BillingMethod.UNIT_KM_WEIGHT.getValue()
                            || custContractpc.getBillingMethod() == BillingMethod.UNIT_KM_PIECE.getValue()) {
                        section.setKilText((section.getStartKil() == null ? "∞" : section.getStartKil()) + "<="
                                + "x" + "<" + (section.getEndKil() == null ? "∞" : section.getEndKil()));
                    }
                }

                // 若没有结果则保存一个空的结果在页面展示
                custContractpc.setContractpcSectionList(contractpcSections);

                custContractpc.setGuidingPrice(null);
            }

//            custContractpc.setDeliName(CharSequenceUtil.concat(true, custContractpc.getDeliProName()
//                    , custContractpc.getDeliCityName(), custContractpc.getDeliAreaName()));

            String arriName;
            if (StringUtils.isNotEmpty(custContractpc.getArriAddrName())) {
                arriName = CharSequenceUtil.concat(true, custContractpc.getArriAddrName(), "（"
                        , custContractpc.getArriProName(), custContractpc.getArriCityName()
                        , custContractpc.getArriAreaName(), "）");
            }else {
                arriName = CharSequenceUtil.concat(true, custContractpc.getArriProName()
                        , custContractpc.getArriCityName(), custContractpc.getArriAreaName());
            }
            custContractpc.setArriName(arriName);

            String deliName;
            if (StringUtils.isNotEmpty(custContractpc.getDeliAddrName())) {
                deliName = CharSequenceUtil.concat(true, custContractpc.getDeliAddrName(), "（"
                        , custContractpc.getDeliProName(), custContractpc.getDeliCityName()
                        , custContractpc.getDeliAreaName(), "）");
            }else {
                deliName = CharSequenceUtil.concat(true, custContractpc.getDeliProName()
                        , custContractpc.getDeliCityName(), custContractpc.getDeliAreaName());
            }
            custContractpc.setDeliName(deliName);
        }

        return custContractpcs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteCustContractpcById(String custContractpcId) {
        CustContractpc old = custContractpcMapper.selectByPrimaryKey(custContractpcId);
        if (old == null) {
            throw new BusinessException("未查询到合同价信息。");
        }
//        if (old.getPriceReview() == 1) {
//            throw new BusinessException("已审核通过无法删除。");
//        }

        String customerId = old.getCustomerId();
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(customerId);
        if (clientPopupVO == null) {
            throw new BusinessException("未查询到客户。");
        }

        CustContractpc custContractpc = new CustContractpc();
        custContractpc.setCustContractpcId(custContractpcId);
        custContractpc.setDelFlag(1);
        custContractpc.setDelDate(new Date());
        custContractpc.setDelUserId(shiroUtils.getUserId().toString());
        custContractpc.setCorScrId("CustContractpcServiceImpl.deleteCustContractpcById");
        int i = custContractpcMapper.updateByPrimaryKeySelective(custContractpc);

        ContractpcSection contractpcSection = new ContractpcSection();
        contractpcSection.setCustContractpcId(custContractpc.getCustContractpcId());

        List<ContractpcSection> contractpcSections = contractpcSectionMapper.selectContractpcSectionList(contractpcSection);

        ContractpcSection sectionUpdate;
        for (ContractpcSection section : contractpcSections) {
            sectionUpdate = new ContractpcSection();
            sectionUpdate.setContractpcSectionId(section.getContractpcSectionId());
            sectionUpdate.setDelDate(new Date());
            sectionUpdate.setDelFlag(1);
            sectionUpdate.setDelUserId(shiroUtils.getUserId().toString());
            sectionUpdate.setCorScrId("CustContractpcServiceImpl.deleteCustContractpcById");
            contractpcSectionMapper.updateContractpcSection(sectionUpdate);
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addCustContractpc(CustContractpcAddVO custContractpcAddVO) {
        String customerId = custContractpcAddVO.getCustomerId();
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(customerId);
        if (clientPopupVO == null) {
            throw new BusinessException("未查询到客户。");
        }

        ContractpcVersion cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(customerId);
        if (cv != null) {
            custContractpcAddVO.setVersionId(cv.getId());
        }else {
            ContractpcVersion contractpcVersion = new ContractpcVersion();
            contractpcVersion.setId(IdUtil.simpleUUID());
            contractpcVersion.setCustomerId(customerId);
            contractpcVersion.setVersionNum(contractpcVersionService.buildVersionNo(customerId));
            contractpcVersion.setIsLatest(1);
            contractpcVersion.setRegScrId("addCustContractpc");
            contractpcVersionMapper.insertSelective(contractpcVersion);

            custContractpcAddVO.setVersionId(contractpcVersion.getId());
        }

        List<CustContractpcAddVO> list = new ArrayList<>();

        Integer billingMethod = custContractpcAddVO.getBillingMethod();

//        if (BillingMethod.UNIT_KM.getValue() == billingMethod) {
//            /*
//             * 单价（元/公里）无需地址
//             */
//            CustContractpcAddVO add = new CustContractpcAddVO();
//            BeanUtils.copyProperties(custContractpcAddVO, add);
//            list.add(add);
//        }else {

            if ((BillingMethod.UNIT_KM.getValue() == billingMethod
                    || BillingMethod.UNIT_KM_WEIGHT.getValue() == billingMethod
                    || BillingMethod.UNIT_KM_PIECE.getValue() == billingMethod)
                    && (StringUtils.isEmpty(custContractpcAddVO.getDeliAreaId())
                        || StringUtils.isEmpty(custContractpcAddVO.getArriAreaId()))) {
                CustContractpcAddVO add = new CustContractpcAddVO();
                BeanUtils.copyProperties(custContractpcAddVO, add);

                add.setDeliProvinceId(null);
                add.setDeliCityId(null);
                add.setDeliAreaId(null);
                add.setArriProvinceId(null);
                add.setArriCityId(null);
                add.setArriAreaId(null);

                add.setArriAddrName(null);
                add.setArrivalId(null);
                add.setArriDetailAddr(null);

                add.setDeliId(null);
                add.setDeliAddrName(null);
                add.setDeliDetailAddr(null);

                add.setArrivalId(null);
                add.setArriAddrName(null);
                add.setArriDetailAddr(null);

                list.add(add);

            }else {
                //提货区
                String[] deliAreaIds = custContractpcAddVO.getDeliAreaId().split(",");
                //到货区
                String[] arriAreaIds = custContractpcAddVO.getArriAreaId().split(",");

                for (String deliAreaId : deliAreaIds) {
                    for (String arriAreaId : arriAreaIds) {
                        CustContractpcAddVO add = new CustContractpcAddVO();

                        BeanUtils.copyProperties(custContractpcAddVO, add);
                        add.setDeliAreaId(deliAreaId);
                        add.setArriAreaId(arriAreaId);
                        list.add(add);
                    }
                }
            }



//        }


        for (CustContractpcAddVO addVo : list) {
            CustContractpc pc = new CustContractpc();
            BeanUtils.copyProperties(addVo, pc);
            List<CustContractpc> pcOldList = custContractpcMapper.selecList(pc);
            if (pcOldList != null && pcOldList.size() > 0) {
                throw new BusinessException("存在相同的数据，请删除后再新添。");
            }


            CustContractpc custContractpc = new CustContractpc();
            BeanUtils.copyProperties(addVo, custContractpc);
            // 主键
            custContractpc.setCustContractpcId(IdUtil.simpleUUID());
            // 删除状态
            custContractpc.setDelFlag(0);
            // 新增一条合同价信息
            if (StringUtils.isEmpty(custContractpc.getIfSection())) {
                // 页面 是否有区间价格
                custContractpc.setIfSection("0");
            }

//            if (BillingMethod.UNIT_KM.getValue() != billingMethod) {
                //提货省
                String deliProName = provinceMapper.getProvince(custContractpc.getDeliProvinceId());
                //提货市
                String deliCityName = provinceMapper.getCity(custContractpc.getDeliCityId());
                //提货区
                String deliAreaName = provinceMapper.getArea(custContractpc.getDeliAreaId());
                //
                String arriProName = provinceMapper.getProvince(custContractpc.getArriProvinceId());
                String arriCityName = provinceMapper.getCity(custContractpc.getArriCityId());
                String arriAreaName = provinceMapper.getArea(custContractpc.getArriAreaId());

                custContractpc.setDeliProName(deliProName);
                custContractpc.setDeliCityName(deliCityName);
                custContractpc.setDeliAreaName(deliAreaName);
                custContractpc.setArriProName(arriProName);
                custContractpc.setArriCityName(arriCityName);
                custContractpc.setArriAreaName(arriAreaName);
//            }

            custContractpc.setPriceReview(0);
            custContractpc.setRegScrId("CustContractpcServiceImpl.addCustContractpc");
            custContractpc.setCorScrId("CustContractpcServiceImpl.addCustContractpc");
            custContractpcMapper.insertSelective(custContractpc);

            if (custContractpc.getIfSection() != null && "1".equals(custContractpc.getIfSection())) {
                // 合同价中的区间集合
                List<ContractpcSection> contractpcSectionList = custContractpc.getContractpcSectionList();
                if (contractpcSectionList == null || contractpcSectionList.size() == 0) {
                    throw new BusinessException("区间价格不能为空。");
                }

                // 去除区间集合的空对象
                List<ContractpcSection> contractpcSections = contractpcSectionList.stream()
                        .filter((ContractpcSection s) -> !Objects.isNull(s.getGuidingPrice()))
                        .collect(Collectors.toList());

                if (contractpcSections.size() == 0) {
                    throw new BusinessException("区间价格不能为空。");
                }

                // 遍历插入合同价区间
                contractpcSections.forEach(contractpcSection -> {
                    // 合同区间关联度客户ID
                    contractpcSection.setCustomerId(custContractpc.getCustomerId());
                    // 区间关联的合同价Id
                    contractpcSection.setCustContractpcId(custContractpc.getCustContractpcId());
                    // 区间主键
                    contractpcSection.setContractpcSectionId(IdUtil.simpleUUID());
                    // 删除状态
                    contractpcSection.setDelFlag(0);
                    contractpcSection.setRegScrId("CustContractpcServiceImpl.addCustContractpc");
                    contractpcSection.setCorScrId("CustContractpcServiceImpl.addCustContractpc");
                    // 新增一条合同价区间
                    contractpcSectionMapper.insertContractpcSection(contractpcSection);
                });
            }
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcEdit(CustContractpcAddVO addVO) {
        String customerId = addVO.getCustomerId();
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(customerId);
        if (clientPopupVO == null) {
            throw new BusinessException("未查询到客户。");
        }

        CustContractpc custContractpc = custContractpcMapper.selectByPrimaryKey(addVO.getCustContractpcId());
        if (custContractpc == null) {
            throw new BusinessException("未查到合同价。");
        }
        CustContractpc pcUpdate = new CustContractpc();
        BeanUtils.copyProperties(custContractpc, pcUpdate);

        pcUpdate.setCustContractpcId(addVO.getCustContractpcId());
        pcUpdate.setCarLen(addVO.getCarLen());
        pcUpdate.setCarType(addVO.getCarType());
        pcUpdate.setGoodsCharacter(addVO.getGoodsCharacter());
        pcUpdate.setBillingMethod(addVO.getBillingMethod());
        pcUpdate.setIsIncludeTax(addVO.getIsIncludeTax());

        pcUpdate.setGoodsId(addVO.getGoodsId());
        pcUpdate.setGoodsName(addVO.getGoodsName());

        pcUpdate.setIsKilRound(addVO.getIsKilRound());
        pcUpdate.setIsRound(addVO.getIsRound());

        pcUpdate.setIsOversize(addVO.getIsOversize());

        // 页面 是否有区间价格
        pcUpdate.setIfSection(addVO.getIfSection() != null && "1".equals(addVO.getIfSection()) ? "1" : "0");
        pcUpdate.setCorScrId("CustContractpcServiceImpl.cntractpcEdit");
        pcUpdate.setGuidingPrice(addVO.getGuidingPrice());

        pcUpdate.setDeliveryFee(addVO.getDeliveryFee());
        pcUpdate.setReservePrice(addVO.getReservePrice());
        pcUpdate.setPriceReview(0);
        pcUpdate.setIsFtl(addVO.getIsFtl());
        pcUpdate.setIsSkipMileage(addVO.getIsSkipMileage());
        pcUpdate.setIsRoundTrip(addVO.getIsRoundTrip());
        pcUpdate.setCostBillingType(addVO.getCostBillingType());

        Subject subject = ShiroUtils.getSubject();
        if (subject.isPermitted("client:contract:setCostPrice")) {
            pcUpdate.setCostPrice(addVO.getCostPrice());
        }else {
            pcUpdate.setCostPrice(custContractpc.getCostPrice());
        }
        custContractpcMapper.updateByPrimaryKey(pcUpdate);


        List<ContractpcSection> sectionOldList = contractpcSectionMapper.selectAllByCustContractpcId(addVO.getCustContractpcId());

        ContractpcSection section = new ContractpcSection();
        section.setCustContractpcId(addVO.getCustContractpcId());
        section.setDelUserId(shiroUtils.getUserId().toString());
        section.setDelFlag(1);
        section.setDelDate(new Date());
        section.setCorScrId("CustContractpcServiceImpl.cntractpcEdit");
        contractpcSectionMapper.updateContractpcSectionByContractpcId(section);


        // 合同价中的区间集合
        List<ContractpcSection> contractpcSectionList = addVO.getContractpcSectionList();
        if (contractpcSectionList != null && "1".equals(addVO.getIfSection())) {
            // 去除区间集合的空对象
            List<ContractpcSection> contractpcSections = contractpcSectionList.stream()
                    .filter((ContractpcSection s) -> !Objects.isNull(s.getGuidingPrice()))
                    .collect(Collectors.toList());
            // 遍历插入合同价区间
            contractpcSections.forEach(contractpcSection -> {

                if (!subject.isPermitted("client:contract:setCostPrice")) {
                    BigDecimal costPrice = sectionOldList.stream()
                            .filter(x -> contractpcSection.getContractpcSectionId() != null
                                    && contractpcSection.getContractpcSectionId().equals(x.getContractpcSectionId()))
                            .map(ContractpcSection::getCostPrice)
                            .filter(Objects::nonNull)
                            .findFirst().orElse(null);
                    contractpcSection.setCostPrice(costPrice);

                    String costBillingType = sectionOldList.stream()
                            .filter(x -> contractpcSection.getContractpcSectionId() != null
                                    && contractpcSection.getContractpcSectionId().equals(x.getContractpcSectionId()))
                            .map(ContractpcSection::getCostBillingType)
                            .filter(Objects::nonNull)
                            .findFirst().orElse(null);

                    contractpcSection.setCostBillingType(costBillingType);
                }

                // 合同区间关联度客户ID
                contractpcSection.setCustomerId(custContractpc.getCustomerId());
                // 区间关联的合同价Id
                contractpcSection.setCustContractpcId(addVO.getCustContractpcId());
                // 区间主键
                contractpcSection.setContractpcSectionId(IdUtil.simpleUUID());
                // 删除状态
                contractpcSection.setDelFlag(0);
                contractpcSection.setRegScrId("CustContractpcServiceImpl.cntractpcEdit");
                contractpcSection.setCorScrId("CustContractpcServiceImpl.cntractpcEdit");
                // 新增一条合同价区间
                contractpcSectionMapper.insertContractpcSection(contractpcSection);
            });
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcEditBatch(CustContractpcAddVO addVO) {
        String customerId = addVO.getCustomerId();
        ClientPopupVO clientPopupVO = clientMapper.selectClientById(customerId);
        if (clientPopupVO == null) {
            throw new BusinessException("未查询到客户。");
        }

        ContractpcVersion cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(customerId);
        if (cv == null || StringUtils.isEmpty(cv.getId())) {
            throw new BusinessException("未查询到版本号");
        }

        CustContractpc pc = new CustContractpc();
        BeanUtils.copyProperties(addVO, pc);

        pc.setVersionId(cv.getId());
        List<CustContractpc> pcOldList = custContractpcMapper.selecList(pc);

        for (CustContractpc contractpc : pcOldList) {
            CustContractpc pcUpdate = new CustContractpc();
            pcUpdate.setCustContractpcId(contractpc.getCustContractpcId());
            pcUpdate.setIfSection(addVO.getIfSection() != null && "1".equals(addVO.getIfSection()) ? "1" : "0");
            pcUpdate.setCorScrId("CustContractpcServiceImpl.cntractpcEditBatch");
            pcUpdate.setGuidingPrice(addVO.getGuidingPrice());
            pcUpdate.setCostPrice(addVO.getCostPrice());
            pcUpdate.setReservePrice(addVO.getReservePrice());
            pcUpdate.setDeliveryFee(addVO.getDeliveryFee());

            pcUpdate.setIsIncludeTax(addVO.getIsIncludeTax());

            pcUpdate.setPriceReview(0);

            pcUpdate.setCostBillingType(addVO.getCostBillingType());
            custContractpcMapper.updateByPrimaryKeySelective(pcUpdate);


            ContractpcSection section = new ContractpcSection();
            section.setCustContractpcId(contractpc.getCustContractpcId());
            section.setDelUserId(shiroUtils.getUserId().toString());
            section.setDelFlag(1);
            section.setDelDate(new Date());
            section.setCorScrId("CustContractpcServiceImpl.cntractpcEditBatch");
            contractpcSectionMapper.updateContractpcSectionByContractpcId(section);

            // 合同价中的区间集合
            List<ContractpcSection> contractpcSectionList = addVO.getContractpcSectionList();
            if (contractpcSectionList != null && "1".equals(addVO.getIfSection())) {
                // 去除区间集合的空对象
                List<ContractpcSection> contractpcSections = contractpcSectionList.stream()
                        .filter((ContractpcSection s) -> !Objects.isNull(s.getGuidingPrice()))
                        .collect(Collectors.toList());
                // 遍历插入合同价区间
                contractpcSections.forEach(contractpcSection -> {
                    // 合同区间关联度客户ID
                    contractpcSection.setCustomerId(contractpc.getCustomerId());
                    // 区间关联的合同价Id
                    contractpcSection.setCustContractpcId(contractpc.getCustContractpcId());
                    // 区间主键
                    contractpcSection.setContractpcSectionId(IdUtil.simpleUUID());
                    // 删除状态
                    contractpcSection.setDelFlag(0);
                    contractpcSection.setRegScrId("CustContractpcServiceImpl.cntractpcEditBatch");
                    contractpcSection.setCorScrId("CustContractpcServiceImpl.cntractpcEditBatch");
                    // 新增一条合同价区间
                    contractpcSectionMapper.insertContractpcSection(contractpcSection);
                });
            }
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcReview(CustContractpcVO custContractpcVO, Integer checkStatus) {
        //审核状态
//        checkStatus = checkStatus != null && checkStatus == 1 ? 1 : 0;

        ClientPopupVO clientPopupVO = clientMapper.selectClientById(custContractpcVO.getCustomerId());
        if (clientPopupVO == null) {
            throw new BusinessException("未查询到客户");
        }
        ContractpcVersion cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(custContractpcVO.getCustomerId());
        if (cv == null || StringUtils.isEmpty(cv.getId())) {
            throw new BusinessException("未查询到版本号");
        }
        // 根据客户ID查询合同价
        custContractpcVO.setPriceReview(0);
        List<CustContractpcVO> custContractpcs = custContractpcMapper.selectCustContractpcVoList(custContractpcVO);

        if (checkStatus == 1) {
            int i = custContractpcMapper.cntractpcReview(custContractpcVO, checkStatus, shiroUtils.getUserId().toString()
                    , shiroUtils.getLoginName(), new Date(),null,null,null);

            if (custContractpcs.size() != i) {
                throw new BusinessException("审核失败，请刷新后重试");
            }
            //更新客户
//            clientMapper.updateContractPriceReview(custContractpcVO.getCustomerId(), shiroUtils.getUserId().toString()
//                    , shiroUtils.getLoginName(), new Date());

        }else {
        /*    int i = custContractpcMapper.cntractpcReview(custContractpcVO, checkStatus
                    , null, null, null, shiroUtils.getUserId().toString()
                    , shiroUtils.getLoginName(), new Date());

            if (custContractpcs.size() != i) {
                throw new BusinessException("审核失败，请刷新后重试");
            }
*/
            //更新客户
//            clientMapper.updateContractPriceReview(custContractpcVO.getCustomerId()
//                    , null, null, null);
        }

        if (checkStatus == 2) {
            int i = custContractpcMapper.cntractpcReview(custContractpcVO, checkStatus
                    , null, null, null, shiroUtils.getUserId().toString()
                    , shiroUtils.getLoginName(), new Date());

            if (custContractpcs.size() != i) {
                throw new BusinessException("审核失败，请刷新后重试");
            }
        }

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcSingleReview(String custContractpcId, Integer checkStatus) {
        CustContractpc custContractpc = custContractpcMapper.selectByPrimaryKey(custContractpcId);
        if (custContractpc ==null) {
            throw new BusinessException("未查询到合同价，请刷新重试");
        }
        CustContractpc contractpcUpdate = new CustContractpc();
        contractpcUpdate.setCustContractpcId(custContractpcId);
        contractpcUpdate.setPriceReview(checkStatus);
        if (checkStatus == 2) {
            contractpcUpdate.setDismissReviewUserId(shiroUtils.getUserId().toString());
            contractpcUpdate.setDismissReviewUserName(shiroUtils.getLoginName());
            contractpcUpdate.setDismissReviewDate(new Date());
        }
        if (checkStatus == 1) {
            contractpcUpdate.setPriceReviewUserId(shiroUtils.getUserId().toString());
            contractpcUpdate.setPriceReviewUserName(shiroUtils.getLoginName());
            contractpcUpdate.setPriceReviewDate(new Date());

//            clientMapper.updateContractPriceReview(custContractpc.getCustomerId(), shiroUtils.getUserId().toString()
//                    , shiroUtils.getLoginName(), new Date());
        }else {
//            clientMapper.updateContractPriceReview(custContractpc.getCustomerId()
//                    , null, null, null);
        }
        custContractpcMapper.updateByPrimaryKeySelective(contractpcUpdate);

        return AjaxResult.success();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcDelBatch(String customerId) {
        ContractpcVersion cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(customerId);
        if (cv == null || StringUtils.isEmpty(cv.getId())) {
            throw new BusinessException("未查询到版本号");
        }

        CustContractpc custContractpc = new CustContractpc();
        custContractpc.setDelFlag(1);
        custContractpc.setDelDate(new Date());
        custContractpc.setDelUserId(shiroUtils.getUserId().toString());
        custContractpc.setCorScrId("CustContractpcServiceImpl.cntractpcDelBatch");
        int i = custContractpcMapper.updateByCustomerIdAndVersionId(custContractpc, customerId, cv.getId());

        ContractpcSection contractpcSection = new ContractpcSection();
        contractpcSection.setDelFlag(1);
        contractpcSection.setDelDate(new Date());
        contractpcSection.setDelUserId(shiroUtils.getUserId().toString());
        contractpcSection.setCorScrId("CustContractpcServiceImpl.cntractpcDelBatch");

        int i1 = contractpcSectionMapper.updateByCustomerIdAndVersionId(contractpcSection, customerId, cv.getId());

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcDelBatch(CustContractpcVO custContractpcVO) {
        // 根据客户ID查询合同价
        List<CustContractpcVO> custContractpcs = custContractpcMapper.selectCustContractpcVoList(custContractpcVO);

        for (CustContractpcVO custContractpc : custContractpcs) {
            CustContractpc del = new CustContractpc();
            del.setDelFlag(1);
            del.setDelDate(new Date());
            del.setDelUserId(shiroUtils.getUserId().toString());
            del.setCustContractpcId(custContractpc.getCustContractpcId());
            del.setCorScrId("CustContractpcServiceImpl.cntractpcDelBatch");

            int i = custContractpcMapper.updateByPrimaryKeySelective(del);

            ContractpcSection sectionDel = new ContractpcSection();
            sectionDel.setDelFlag(1);
            sectionDel.setDelDate(new Date());
            sectionDel.setDelUserId(shiroUtils.getUserId().toString());
            sectionDel.setCustContractpcId(custContractpc.getCustContractpcId());
            sectionDel.setCorScrId("CustContractpcServiceImpl.cntractpcDelBatch");

            int i1 = contractpcSectionMapper.updateContractpcSectionByContractpcId(sectionDel);
        }

        return AjaxResult.success();
    }

    @Override
    public List<CustContractpcCost> selectCustContractpcCost(CustContractpcCost param) {
        List<CustContractpcCost> list = custContractpcMapper.listContractpcAndSection(param);
        for (int i = 0; i < list.size(); i++) {
            CustContractpcCost ccc = list.get(i);
            if (ccc.getIfSection().equals("1")) {
                final List<ContractpcSectionCost> sections = ccc.getContractpcSectionList();
                for (ContractpcSectionCost section : sections) {
                    String start = section.getStartOperator() == 0 ? "<" : "<=";
                    //2:小于 3 小于等于
                    String end = section.getEndOperator() == 2 ? "<" : "<=";
                    section.setSectionText((section.getStartSection() == null ? "0" : section.getStartSection()) + start
                            + "x" + end + (section.getEndSection() == null ? "∞" : section.getEndSection()));

                    if (ccc.getBillingMethod() == BillingMethod.UNIT_KM_WEIGHT.getValue()
                            || ccc.getBillingMethod() == BillingMethod.UNIT_KM_PIECE.getValue()) {
                        section.setKilText((section.getStartKil() == null ? "0" : section.getStartKil()) + "<="
                                + "x" + "<" + (section.getEndKil() == null ? "∞" : section.getEndKil()));
                    }
                }
            } else {
                if (ccc.getContractpcSectionList() == null) {
                    ccc.setContractpcSectionList(new ArrayList<>());
                }
                if (ccc.getContractpcSectionList().size() == 0) { // 无区间的补1空区间
                    ccc.getContractpcSectionList().add(new ContractpcSectionCost() {{
                        setCostPrice(ccc.getCostPrice());
                        setCostBillingTypeName(ccc.getCostBillingTypeName());
                        setCostBillingType(ccc.getCostBillingType());
                    }});
                }
            }
        }
        return list;
    }

    static class Pca {
        public String id;
        public String name;
        public Map<String, Pca> child;

        static Pca init(String id, String name, Map<String, Pca> chid) {
            Pca pca = new Pca();
            pca.id = id;
            pca.name = name;
            pca.child = chid;
            return pca;
        }
    }

    private void initPcaTree() {
        Map<String, Pca> pcaTree = new HashMap<>();
        Set<String> provinceNames = new HashSet<>();
        if (provinceNames.size() > 0) {
            List<Map<String, Object>> pcaList = custContractpcMapper.listPca(provinceNames);
            for (int i = 0; i < pcaList.size(); i++) {
                String provinceName = (String) pcaList.get(i).get("provinceName");
                Pca pro = pcaTree.get(provinceName);
                if (pro == null) {
                    String provinceId = (String) pcaList.get(i).get("provinceId");
                    pro = Pca.init(provinceId, provinceName, new HashMap<>());
                    pcaTree.put(provinceName, pro);
                }
                String cityName = (String) pcaList.get(i).get("cityName");
                Pca city = pro.child.get(cityName);
                if (city == null) {
                    String cityId = (String) pcaList.get(i).get("cityId");
                    city = Pca.init(cityId, cityName, new HashMap<>());
                    pro.child.put(cityName, city);
                }
                String areaName = (String) pcaList.get(i).get("areaName");
                Pca area = city.child.get(areaName);
                if (area == null) {
                    String areaId = (String) pcaList.get(i).get("areaId");
                    area = Pca.init(areaId, areaName, null);
                    city.child.put(areaName, area);
                }
            }
            //System.out.println(JSONUtil.toJsonStr(pcaTree));
        }
    }

    @Transactional
    @Override
    public AjaxResult importCostPrice(String customerId, List<CustContractpcCost> list) {
        CustContractpcCost param = new CustContractpcCost();
        param.setCustomerId(customerId);
        List<CustContractpcCost> dbList = custContractpcMapper.listContractpcAndSection(param);
        Map<String, Object> data = new HashMap<>();
        Set<Integer> unMatchRowNums = new LinkedHashSet<>();
        Set<Integer> manyMatchRowNums = new LinkedHashSet<>();
        data.put("unMatchRowNums", unMatchRowNums);
        data.put("manyMatchRowNums", manyMatchRowNums);
        int success = 0;
        for (int i = 0; i < list.size(); i++) {
            final CustContractpcCost cost = list.get(i);
            String[] arriAreaNameArray; // 到货区县可能的格式："肥西县、庐江县、蜀山区"
            if (cost.getArriAreaName() == null) {
                arriAreaNameArray = new String[1];
            } else {
                arriAreaNameArray = cost.getArriAreaName().split("、");
            }
            for (int j = 0; j < arriAreaNameArray.length; j++) {
                final List<Integer> dbCostFrom = getDbCostFrom(dbList, cost, arriAreaNameArray[j]);// 查找该数据对应数据库的合同价
                if (dbCostFrom.size() == 0) { //
                    unMatchRowNums.add(cost.getRowNum());
                } else if (dbCostFrom.size() > 1) {
                    manyMatchRowNums.add(cost.getRowNum());
                } else { // 精确匹配到
                    CustContractpcCost dbCost = dbList.get(dbCostFrom.get(0));
                    dbList.remove(dbCostFrom.get(0));
                    if (dbCost.getIfSection().equals("1")) { // 有区间时更新成本价到区间
                        final List<ContractpcSectionCost> sectionList = cost.getContractpcSectionList();
                        for (int k = 0; k < sectionList.size(); k++) {
                            final ContractpcSectionCost section = sectionList.get(k);
                            final ContractpcSectionCost dbSection = dbCost.getContractpcSectionList().get(k);
                            final BigDecimal costPrice = section.getCostPrice();
                            final String costBillingType = section.getCostBillingType();
                            boolean condition1 = costPrice != null && !NumberUtil.equals(dbSection.getCostPrice(), costPrice) && costPrice.compareTo(BigDecimal.ZERO) > 0;
                            boolean condition2 = StringUtils.isNotBlank(costBillingType) && !Objects.equals(dbSection.getCostBillingType(), costBillingType);
                            if (condition1 || condition2) {
                                custContractpcMapper.updateSectionCostPriceById(dbSection.getContractpcSectionId(), condition1 ? costPrice : null, condition2 ? costBillingType : null);
                                success++;
                            }
                        }
                    } else { // 无区间时更新成本价到主表
                        final BigDecimal costPrice = cost.getContractpcSectionList().get(0).getCostPrice();
                        final String costBillingType = cost.getContractpcSectionList().get(0).getCostBillingType();
                        boolean condition1 = costPrice != null && !NumberUtil.equals(dbCost.getCostPrice(), costPrice) && costPrice.compareTo(BigDecimal.ZERO) > 0;
                        boolean condition2 = StringUtils.isNotBlank(costBillingType) && !Objects.equals(dbCost.getCostBillingType(), costBillingType);
                        if (condition1 || condition2) {
                            custContractpcMapper.updateCostPriceById(dbCost.getCustContractpcId(), condition1 ? costPrice : null, condition2 ? costBillingType : null);
                            success++;
                        }
                    }
                }
            }
        }
        data.put("success", success);
        return AjaxResult.success(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cntractpcEditCostPrice(CustContractpcAddVO addVO) {
        String custContractpcId = addVO.getCustContractpcId();
        List<ContractpcSection> contractpcSectionList = addVO.getContractpcSectionList();

        CustContractpc custContractpc = custContractpcMapper.selectByPrimaryKey(custContractpcId);
        if (custContractpc == null) {
            throw new BusinessException("未查到合同价。");
        }

        if (addVO.getCostPrice() != null && "0".equals(custContractpc.getIfSection())) {
            CustContractpc update = new CustContractpc();
            update.setCustContractpcId(custContractpcId);
            update.setCostPrice(addVO.getCostPrice());
            update.setCostBillingType(addVO.getCostBillingType());
            update.setCorScrId("cntractpcEditCostPrice");
            int i = custContractpcMapper.updateByPrimaryKeySelective(update);

            if (!compareBigDecimals(custContractpc.getCostPrice(), addVO.getCostPrice())
                    || !custContractpc.getCostBillingType().equals(addVO.getCostBillingType())) {
                ContractpcCostPriceLog log = new ContractpcCostPriceLog();
                log.setId(IdUtil.simpleUUID());
                log.setContractpcId(custContractpcId);
                log.setIfSection(custContractpc.getIfSection());
                log.setCostPriceOld(custContractpc.getCostPrice());
                log.setCostPrice(addVO.getCostPrice());
                log.setCostBillingType(addVO.getCostBillingType());
                log.setCostBillingTypeOld(custContractpc.getCostBillingType());
                log.setRegScrId("cntractpcEditCostPrice");
                log.setAdjustType(0);
                contractpcCostPriceLogMapper.insertSelective(log);
            }
        } else if (contractpcSectionList != null && "1".equals(custContractpc.getIfSection())) {
            for (ContractpcSection contractpcSection : contractpcSectionList) {
                ContractpcSection sectionOld = contractpcSectionMapper
                        .selectContractpcSectionById(contractpcSection.getContractpcSectionId());

                ContractpcSection update = new ContractpcSection();
                update.setContractpcSectionId(contractpcSection.getContractpcSectionId());
                update.setCostPrice(contractpcSection.getCostPrice());
                update.setCostBillingType(contractpcSection.getCostBillingType());
                update.setCorScrId("cntractpcEditCostPrice");
                int i = contractpcSectionMapper.updateByPrimaryKeySelective(update);

                String costBillingTypeOld = sectionOld.getCostBillingType() == null ? "" : sectionOld.getCostBillingType();
                String costBillingType = contractpcSection.getCostBillingType() == null ? "" : contractpcSection.getCostBillingType();

                if (!compareBigDecimals(sectionOld.getCostPrice(), contractpcSection.getCostPrice())
                        || !costBillingTypeOld.equals(costBillingType)) {
                    ContractpcCostPriceLog log = new ContractpcCostPriceLog();
                    log.setId(IdUtil.simpleUUID());
                    log.setContractpcId(custContractpcId);
                    log.setContractpcSectionId(contractpcSection.getContractpcSectionId());
                    log.setIfSection(custContractpc.getIfSection());
                    log.setCostPriceOld(sectionOld.getCostPrice());
                    log.setCostPrice(contractpcSection.getCostPrice());
                    log.setCostBillingTypeOld(costBillingTypeOld);
                    log.setCostBillingType(costBillingType);
                    log.setRegScrId("cntractpcEditCostPrice");
                    log.setAdjustType(0);
                    contractpcCostPriceLogMapper.insertSelective(log);
                }
            }
        }


        return AjaxResult.success();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyCustCntractpcConfig(String sourceCustId, String targetCustId) {
        //客户信息
        ClientPopupVO sourceClient = clientMapper.selectClientById(sourceCustId);
        ClientPopupVO targetClient = clientMapper.selectClientById(targetCustId);

        if (sourceClient == null || targetClient == null) {
            throw new BusinessException("未查询到客户");
        }

        String versionId = null;
        ContractpcVersion cv = contractpcVersionMapper.selectCurrentVersionByCustomerId(targetCustId);
        if (cv != null) {
            versionId = cv.getId();
        }else {
            ContractpcVersion contractpcVersion = new ContractpcVersion();
            contractpcVersion.setId(IdUtil.simpleUUID());
            contractpcVersion.setCustomerId(targetCustId);
            contractpcVersion.setVersionNum(contractpcVersionService.buildVersionNo(targetCustId));
            contractpcVersion.setIsLatest(1);
            contractpcVersion.setRegScrId("addCustContractpc");
            contractpcVersionMapper.insertSelective(contractpcVersion);

            versionId = contractpcVersion.getId();
        }

        List<CustContractpc> sourceContractList = custContractpcMapper.selectLastListByCustomerId(sourceCustId);

        for (CustContractpc source : sourceContractList) {
            String contractpcId = IdUtil.simpleUUID();
            CustContractpc add = new CustContractpc();
            BeanUtils.copyProperties(source, add);
            add.setCustContractpcId(contractpcId);
            add.setCustomerId(targetCustId);
            add.setVersionId(versionId);
            add.setPriceReview(0);
            add.setPriceReviewUserId(null);
            add.setPriceReviewUserName(null);
            add.setPriceReviewDate(null);
            add.setRegScrId("copyCustCntractpcConfig");
            add.setCorScrId("copyCustCntractpcConfig");
            int i = custContractpcMapper.insertSelective(add);
            if (i == 0) {
                throw new BusinessException("复制合同价失败");
            }

            if ("1".equals(source.getIfSection())) {
                List<ContractpcSection> contractpcSections = contractpcSectionMapper
                        .selectAllByCustContractpcId(source.getCustContractpcId());

                for (ContractpcSection sourceSection : contractpcSections) {
                    ContractpcSection addSection = new ContractpcSection();
                    BeanUtils.copyProperties(sourceSection, addSection);
                    addSection.setContractpcSectionId(IdUtil.simpleUUID());
                    addSection.setCustContractpcId(contractpcId);
                    addSection.setCustomerId(targetCustId);
                    add.setRegScrId("copyCustCntractpcConfig");
                    add.setCorScrId("copyCustCntractpcConfig");

                    int i1 = contractpcSectionMapper.insertContractpcSection(addSection);
                    if (i1 == 0) {
                        throw new BusinessException("复制合同价区间失败");
                    }
                }

            }
        }

        return AjaxResult.success();
    }

    @Override
    public List<ContractpcCostPriceLogPageVO> selectContractpcCostPriceLogPage(ContractpcCostPriceLogPageVO contractpcCostPriceLogPageVO) {
        return contractpcCostPriceLogMapper.selectContractpcCostPriceLogPage(contractpcCostPriceLogPageVO);
    }

    private List<Integer> getDbCostFrom(List<CustContractpcCost> dbList, CustContractpcCost cost, String targetArriAreaName) {
        List<Integer> dbMatchIndex = new ArrayList<>();
        for (int i = 0; i < dbList.size(); i++) {
            CustContractpcCost t = dbList.get(i);
            //System.out.println(t);
            if (!Objects.equals(t.getDeliProName(), cost.getDeliProName())) {
                continue;
            }
            if (!Objects.equals(t.getDeliCityName(), cost.getDeliCityName())) {
                continue;
            }
            if (!Objects.equals(t.getDeliAreaName(), cost.getDeliAreaName())) {
                continue;
            }
            if (!Objects.equals(t.getDeliAddrName(), cost.getDeliAddrName())) {
                continue;
            }
            if (!Objects.equals(t.getArriProName(), cost.getArriProName())) {
                continue;
            }
            if (!Objects.equals(t.getArriCityName(), cost.getArriCityName())) {
                continue;
            }
            if (!Objects.equals(t.getArriAreaName(), targetArriAreaName)) {
                continue;
            }
            if (!Objects.equals(t.getArriAddrName(), cost.getArriAddrName())) {
                continue;
            }
            if (!Objects.equals(t.getBillingMethod(), cost.getBillingMethod())) {
                continue;
            }
            if (!Objects.equals(t.getGoodsCharacter(), cost.getGoodsCharacter())) {
                continue;
            }
            if (!Objects.equals(t.getGoodsName(), cost.getGoodsName())) {
                continue;
            }
            if (!Objects.equals(t.getCarLen(), cost.getCarLen())) {
                continue;
            }
            if (!Objects.equals(t.getCarType(), cost.getCarType())) {
                continue;
            }
            if (!Objects.equals(t.getIsOversize(), cost.getIsOversize())) {
                continue;
            }
            if (!Objects.equals(t.getIsFtl(), cost.getIsFtl())) {
                continue;
            }
            if (!Objects.equals(t.getIsRoundTrip(), cost.getIsRoundTrip())) {
                continue;
            }
            if (!Objects.equals(t.getIsSkipMileage(), cost.getIsSkipMileage())) {
                continue;
            }
            if (t.getIfSection().equals("1")) {
                // 判断区间是否一致：只判断通顺序的公里数结束区间、货量结束区间、价格类型
                final List<ContractpcSectionCost> dbSection = t.getContractpcSectionList();
                final List<ContractpcSectionCost> _section = cost.getContractpcSectionList();
                if (dbSection.size() != _section.size()) {
                    continue;
                } else {
                    boolean sectionMatch = true;
                    for (int j = 0; j < dbSection.size(); j++) {
                        if (!NumberUtil.equals(dbSection.get(j).getEndKil(), _section.get(j).getEndKil())) {
                            sectionMatch = false;
                            break;
                        }
                        if (!NumberUtil.equals(dbSection.get(j).getEndSection(), _section.get(j).getEndSection())) {
                            sectionMatch = false;
                            break;
                        }
                        if (!Objects.equals(dbSection.get(j).getIsFixedPrice(), _section.get(j).getIsFixedPrice())) {
                            sectionMatch = false;
                            break;
                        }
                    }
                    if (!sectionMatch) {
                        continue;
                    }
                }
            } else {
                // 没有区间的数据，前端组合时会默认1个没有区间的数值的section
                final List<ContractpcSectionCost> sections = cost.getContractpcSectionList();
                if (sections.size() != 1) {
                    continue;
                }
                if (sections.get(0).getIsFixedPrice() != null) {
                    continue;
                }
            }
            dbMatchIndex.add(i);
        }
        return dbMatchIndex;
    }


    private static boolean compareBigDecimals(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

}
