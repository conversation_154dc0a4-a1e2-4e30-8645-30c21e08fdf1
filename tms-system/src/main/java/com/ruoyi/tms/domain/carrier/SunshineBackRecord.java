package com.ruoyi.tms.domain.carrier;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 阳光回调信息
 * <AUTHOR>
 * @date 2024/8/5
 */
@Data
public class SunshineBackRecord {
    private static final long serialVersionUID = 1L;

    private String id;

    @Excel(name = "发货单号")
    private String invoiceVbillno;
    @Excel(name = "委托单号")
    private String entrustVbillno;
    @Excel(name = "客户单号")
    private String custOrderNo;

    //0到货 1回单 2提货
    @Excel(name = "类型",readConverterExp = "0=到货,1=回单,2=提货,3=附加费,4=调整单,5=派车,6=车辆行驶证,7=确认")
    private Integer type;
    //成功标记 0成功 1失败
    @Excel(name = "成功标记",readConverterExp = "0=成功,1=失败")
    private Integer successFlag;

    @Excel(name = "推送结果")
    private String backMemo;

    //创建时间
    @Excel(name = "推送时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date regDate;

    private Map<String,Object> params;
}
